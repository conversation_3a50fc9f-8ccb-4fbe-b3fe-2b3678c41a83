#!/usr/bin/env python
"""
اختبار أسماء الصفحات في المزامنة العكسية
"""

import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'homeupdate.settings')
django.setup()

def test_sheet_name_handling():
    """اختبار معالجة أسماء الصفحات"""
    try:
        from odoo_db_manager.google_sync import GoogleSyncConfig, create_sheets_service
        
        print("=== اختبار معالجة أسماء الصفحات ===")
        
        # التحقق من وجود إعداد نشط
        config = GoogleSyncConfig.get_active_config()
        if not config:
            print("❌ لا يوجد إعداد مزامنة نشط")
            return False
        
        print(f"✅ تم العثور على إعداد المزامنة: {config.name}")
        
        # الحصول على بيانات الاعتماد
        credentials = config.get_credentials()
        if not credentials:
            print("❌ لا يمكن قراءة بيانات الاعتماد")
            return False
        
        print("✅ تم الحصول على بيانات الاعتماد")
        
        # إنشاء خدمة Google Sheets
        sheets_service = create_sheets_service(credentials)
        if not sheets_service:
            print("❌ فشل في إنشاء خدمة Google Sheets")
            return False
        
        print("✅ تم إنشاء خدمة Google Sheets")
        
        # الحصول على قائمة الصفحات المتاحة
        try:
            spreadsheet_metadata = sheets_service.spreadsheets().get(
                spreadsheetId=config.spreadsheet_id
            ).execute()
            
            sheets = spreadsheet_metadata.get('sheets', [])
            print(f"\n📊 عدد الصفحات المتاحة: {len(sheets)}")
            
            if sheets:
                print("\n📋 الصفحات المتاحة:")
                for i, sheet in enumerate(sheets, 1):
                    sheet_title = sheet['properties']['title']
                    sheet_id = sheet['properties']['sheetId']
                    print(f"   {i}. {sheet_title} (ID: {sheet_id})")
                
                # البحث عن الصفحة المناسبة
                possible_names = [
                    'Complete Orders Lifecycle',
                    'دورة حياة الطلبات الكاملة',
                    'Orders',
                    'الطلبات',
                    'Sheet1'
                ]
                
                target_sheet = None
                for sheet in sheets:
                    sheet_title = sheet['properties']['title']
                    if sheet_title in possible_names:
                        target_sheet = sheet_title
                        print(f"\n✅ تم العثور على صفحة مناسبة: {target_sheet}")
                        break
                
                if not target_sheet:
                    target_sheet = sheets[0]['properties']['title']
                    print(f"\n⚠️ لم يتم العثور على صفحة مناسبة، سيتم استخدام أول صفحة: {target_sheet}")
                
                # اختبار قراءة البيانات من الصفحة
                try:
                    result = sheets_service.spreadsheets().values().get(
                        spreadsheetId=config.spreadsheet_id,
                        range=f"'{target_sheet}'!A1:C5"  # قراءة عينة صغيرة
                    ).execute()
                    
                    values = result.get('values', [])
                    print(f"\n✅ تم قراءة البيانات بنجاح من الصفحة: {target_sheet}")
                    print(f"📊 عدد الصفوف المقروءة: {len(values)}")
                    
                    if values:
                        print("\n📋 عينة من البيانات:")
                        for i, row in enumerate(values[:3], 1):
                            print(f"   الصف {i}: {row[:3] if len(row) >= 3 else row}")
                    
                    return True
                    
                except Exception as read_error:
                    print(f"❌ خطأ في قراءة البيانات: {str(read_error)}")
                    return False
            else:
                print("❌ لا توجد صفحات في الجدول")
                return False
                
        except Exception as metadata_error:
            print(f"❌ خطأ في الحصول على معلومات الجدول: {str(metadata_error)}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالجة أسماء الصفحات: {str(e)}")
        return False

def test_reverse_sync_logic():
    """اختبار منطق المزامنة العكسية"""
    try:
        print("\n=== اختبار منطق المزامنة العكسية ===")
        
        # قراءة كود المزامنة العكسية للتحقق من الإصلاحات
        sync_file = 'odoo_db_manager/google_sync.py'
        if os.path.exists(sync_file):
            with open(sync_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود منطق البحث عن الصفحات
            if 'spreadsheet_metadata = service.spreadsheets().get' in content:
                print("✅ تم إضافة منطق الحصول على معلومات الجدول")
            else:
                print("❌ لم يتم العثور على منطق الحصول على معلومات الجدول")
                return False
            
            # التحقق من وجود قائمة الأسماء المحتملة
            if 'possible_names = [' in content:
                print("✅ تم إضافة قائمة الأسماء المحتملة للصفحات")
            else:
                print("❌ لم يتم العثور على قائمة الأسماء المحتملة")
                return False
            
            # التحقق من وجود منطق البحث
            if 'for sheet in sheets:' in content:
                print("✅ تم إضافة منطق البحث في الصفحات")
            else:
                print("❌ لم يتم العثور على منطق البحث في الصفحات")
                return False
            
            # التحقق من استخدام target_sheet
            if "range=f\"'{target_sheet}'!A:BH\"" in content:
                print("✅ تم استخدام target_sheet في قراءة البيانات")
            else:
                print("❌ لم يتم استخدام target_sheet في قراءة البيانات")
                return False
            
            return True
        else:
            print("❌ ملف google_sync غير موجود")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار منطق المزامنة العكسية: {str(e)}")
        return False

def main():
    print("🔄 بدء اختبار أسماء الصفحات في المزامنة العكسية...")
    print("="*70)
    
    # اختبار معالجة أسماء الصفحات
    sheet_handling_test = test_sheet_name_handling()
    
    # اختبار منطق المزامنة العكسية
    logic_test = test_reverse_sync_logic()
    
    # النتيجة النهائية
    print("\n" + "="*70)
    print("📊 ملخص نتائج الاختبار:")
    print("="*70)
    
    if sheet_handling_test and logic_test:
        print("🎉 تم إصلاح مشكلة أسماء الصفحات بنجاح!")
        print("\n✅ الإصلاحات المطبقة:")
        print("   ✅ الحصول على قائمة جميع الصفحات المتاحة")
        print("   ✅ البحث عن الصفحة المناسبة من قائمة محددة")
        print("   ✅ استخدام أول صفحة كبديل إذا لم توجد صفحة مناسبة")
        print("   ✅ تجنب مشاكل encoding مع الأسماء العربية")
        print("   ✅ قراءة البيانات بنجاح من الصفحة المحددة")
        print("\n🚀 المزامنة العكسية جاهزة للاستخدام!")
        print("   الرابط: /odoo-db-manager/google-sync/")
        print("   القسم: 'المزامنة العكسية' في أسفل الصفحة")
        print("   المطلوب: كلمة مرور المدير صحيحة")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        
        if not sheet_handling_test:
            print("\n💡 نصائح لحل مشكلة الصفحات:")
            print("   1. تأكد من وجود بيانات في Google Sheets")
            print("   2. تأكد من صحة بيانات الاعتماد")
            print("   3. تأكد من صحة ID الجدول")

if __name__ == "__main__":
    main()
