{% extends 'base.html' %}
{% load static %}

{% block extra_head %}
<!-- إضافة CSRF token للـ JavaScript -->
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block title %}تفاصيل الإشعار - {{ notification.title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="{{ notification.get_icon }} me-2"></i>
            تفاصيل الإشعار
        </h1>
        <div class="btn-group">
            <a href="{% url 'accounts:notifications_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i> العودة للإشعارات
            </a>
        </div>
    </div>

    <!-- بطاقة تفاصيل الإشعار -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold {{ notification.get_color_class }}">
                        <i class="{{ notification.get_icon }} me-2"></i>
                        {{ notification.title }}
                    </h6>
                    <span class="badge badge-{{ notification.get_color_class|slice:'5:' }} badge-pill">
                        {{ notification.get_priority_display }}
                    </span>
                </div>
                <div class="card-body">
                    <!-- معلومات الإشعار -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات الإشعار</h6>
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>العنوان:</strong></td>
                                    <td>{{ notification.title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>النوع:</strong></td>
                                    <td>{{ notification.get_notification_type_display }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الأولوية:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ notification.get_color_class|slice:'5:' }}">
                                            {{ notification.get_priority_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td>{{ notification.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات الطلب</h6>
                            <table class="table table-borderless table-sm">
                                {% if notification.customer_name %}
                                <tr>
                                    <td><strong>اسم العميل:</strong></td>
                                    <td>{{ notification.customer_name }}</td>
                                </tr>
                                {% endif %}
                                {% if notification.order_number %}
                                <tr>
                                    <td><strong>رقم الطلب:</strong></td>
                                    <td>
                                        <span class="text-primary font-weight-bold">
                                            {{ notification.order_number }}
                                        </span>
                                    </td>
                                </tr>
                                {% endif %}
                                {% if notification.created_by %}
                                <tr>
                                    <td><strong>تم الإنشاء بواسطة:</strong></td>
                                    <td>{{ notification.created_by.get_full_name|default:notification.created_by.username }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <!-- محتوى الإشعار -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>محتوى الإشعار
                        </h6>
                        <p class="mb-0">{{ notification.message|linebreaks }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقة إحصائيات القراءة -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>
                        إحصائيات القراءة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-right">
                                <h4 class="text-success">{{ read_count }}</h4>
                                <small class="text-muted">قرأوا</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-right">
                                <h4 class="text-warning">{{ unread_count }}</h4>
                                <small class="text-muted">لم يقرأوا</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <h4 class="text-info">{{ total_users }}</h4>
                            <small class="text-muted">إجمالي</small>
                        </div>
                    </div>
                    
                    <!-- شريط التقدم -->
                    <div class="mt-3">
                        {% widthratio read_count total_users 100 as read_percentage %}
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: {{ read_percentage }}%" 
                                 aria-valuenow="{{ read_percentage }}" 
                                 aria-valuemin="0" aria-valuemax="100">
                                {{ read_percentage }}%
                            </div>
                        </div>
                        <small class="text-muted">نسبة القراءة</small>
                    </div>
                </div>
            </div>

            <!-- بطاقة حالة القراءة للمستخدم -->
            {% if user_read %}
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-check-circle me-2"></i>
                        حالة قراءتك
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>تم القراءة</strong>
                        <br>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            {{ user_read.read_at|date:"Y-m-d H:i:s" }}
                        </small>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- قائمة من قرأ الإشعار (للمدير فقط) -->
    {% if is_admin and all_reads %}
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-users me-2"></i>
                قائمة من قرأ الإشعار ({{ all_reads.count }} من {{ total_users }})
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>القسم</th>
                            <th>تاريخ القراءة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for read in all_reads %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>
                                <i class="fas fa-user me-2"></i>
                                {{ read.user.username }}
                            </td>
                            <td>{{ read.user.get_full_name|default:"-" }}</td>
                            <td>
                                {% if read.user.department %}
                                    <span class="badge badge-info">
                                        {{ read.user.department.name }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <i class="fas fa-clock me-1"></i>
                                {{ read.read_at|date:"Y-m-d H:i:s" }}
                                <br>
                                <small class="text-muted">{{ read.read_at|timesince }} مضت</small>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث الصفحة كل دقيقة لإظهار القراءات الجديدة (للمدير فقط)
    {% if is_admin %}
    setInterval(function() {
        location.reload();
    }, 60000); // كل دقيقة
    {% endif %}
});
</script>
{% endblock %}
