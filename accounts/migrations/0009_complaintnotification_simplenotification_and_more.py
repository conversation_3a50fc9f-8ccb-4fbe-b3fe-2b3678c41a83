# Generated by Django 4.2.21 on 2025-08-10 17:35

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('accounts', '0008_initial_fixed'),
    ]

    operations = [
        migrations.CreateModel(
            name='ComplaintNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=150, verbose_name='عنوان الشكوى')),
                ('customer_name', models.CharField(max_length=100, verbose_name='اسم العميل')),
                ('complaint_number', models.CharField(max_length=50, verbose_name='رقم الشكوى')),
                ('complaint_type', models.CharField(choices=[('new', '🆕 شكوى جديدة'), ('assigned', '👤 تم التعيين'), ('in_progress', '⏳ قيد المعالجة'), ('resolved', '✅ تم الحل'), ('closed', '🔒 مغلقة'), ('escalated', '⬆️ تم التصعيد')], default='new', max_length=20, verbose_name='نوع الإشعار')),
                ('priority', models.CharField(choices=[('low', '🟢 منخفضة'), ('medium', '🟡 متوسطة'), ('high', '🟠 عالية'), ('critical', '🔴 حرجة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='complaint_notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستلم')),
            ],
            options={
                'verbose_name': '📢 إشعار شكوى',
                'verbose_name_plural': '📢 إشعارات الشكاوى',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SimpleNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='عنوان مختصر وواضح', max_length=100, verbose_name='العنوان')),
                ('customer_name', models.CharField(help_text='اسم العميل المرتبط بالإشعار', max_length=100, verbose_name='اسم العميل')),
                ('order_number', models.CharField(help_text='رقم الطلب أو المرجع', max_length=50, verbose_name='رقم الطلب')),
                ('status', models.CharField(help_text='الحالة الحالية للطلب', max_length=50, verbose_name='الحالة')),
                ('notification_type', models.CharField(choices=[('order_created', '🆕 طلب جديد'), ('order_updated', '🔄 تحديث طلب'), ('order_completed', '✅ طلب مكتمل'), ('order_cancelled', '❌ طلب ملغي'), ('complaint_new', '⚠️ شكوى جديدة'), ('complaint_resolved', '✅ شكوى محلولة'), ('inspection_scheduled', '📅 معاينة مجدولة'), ('manufacturing_started', '🏭 بدء التصنيع'), ('installation_completed', '🔧 تركيب مكتمل')], default='order_updated', max_length=30, verbose_name='نوع الإشعار')),
                ('priority', models.CharField(choices=[('low', '🟢 منخفضة'), ('normal', '🟡 عادية'), ('high', '🟠 عالية'), ('urgent', '🔴 عاجلة')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='simple_notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستلم')),
            ],
            options={
                'verbose_name': '🔔 إشعار بسيط',
                'verbose_name_plural': '🔔 الإشعارات البسيطة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.RemoveField(
            model_name='systemsettings',
            name='enable_email_notifications',
        ),
        migrations.RemoveField(
            model_name='systemsettings',
            name='enable_notifications',
        ),
        migrations.DeleteModel(
            name='Notification',
        ),
        migrations.AddIndex(
            model_name='simplenotification',
            index=models.Index(fields=['recipient', '-created_at'], name='accounts_si_recipie_cc1c8c_idx'),
        ),
        migrations.AddIndex(
            model_name='simplenotification',
            index=models.Index(fields=['is_read'], name='accounts_si_is_read_8c4e97_idx'),
        ),
        migrations.AddIndex(
            model_name='simplenotification',
            index=models.Index(fields=['notification_type'], name='accounts_si_notific_9560fe_idx'),
        ),
        migrations.AddIndex(
            model_name='simplenotification',
            index=models.Index(fields=['priority'], name='accounts_si_priorit_1470c2_idx'),
        ),
        migrations.AddIndex(
            model_name='complaintnotification',
            index=models.Index(fields=['recipient', '-created_at'], name='accounts_co_recipie_4db1ec_idx'),
        ),
        migrations.AddIndex(
            model_name='complaintnotification',
            index=models.Index(fields=['is_read'], name='accounts_co_is_read_be9b6d_idx'),
        ),
        migrations.AddIndex(
            model_name='complaintnotification',
            index=models.Index(fields=['complaint_type'], name='accounts_co_complai_4f6c5b_idx'),
        ),
        migrations.AddIndex(
            model_name='complaintnotification',
            index=models.Index(fields=['priority'], name='accounts_co_priorit_4db6fd_idx'),
        ),
    ]
