# Generated by Django 4.2.21 on 2025-08-04 16:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0007_branchmessage_is_for_all_branches_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='branchmessage',
            name='allow_outside_click',
            field=models.BooleanField(default=True, verbose_name='السماح بالإغلاق عند النقر خارج الرسالة'),
        ),
        migrations.AddField(
            model_name='branchmessage',
            name='auto_close',
            field=models.BooleanField(default=True, verbose_name='إغلاق تلقائي'),
        ),
        migrations.AddField(
            model_name='branchmessage',
            name='display_duration',
            field=models.IntegerField(default=20, help_text='المدة بالثواني (10-50 ثانية)', verbose_name='مدة العرض (ثانية)'),
        ),
        migrations.AddField(
            model_name='branchmessage',
            name='display_style',
            field=models.CharField(choices=[('sweetalert2', 'SweetAlert2 - حديث وأنيق'), ('toastr', 'Toastr - إشعارات جانبية'), ('notyf', 'Notyf - بسيط وسريع'), ('alertify', 'Alertify - كلاسيكي')], default='sweetalert2', max_length=20, verbose_name='نمط العرض'),
        ),
        migrations.AddField(
            model_name='branchmessage',
            name='icon_size',
            field=models.CharField(choices=[('small', 'صغير'), ('medium', 'متوسط'), ('large', 'كبير')], default='medium', max_length=10, verbose_name='حجم الأيقونة'),
        ),
        migrations.AddField(
            model_name='branchmessage',
            name='show_close_button',
            field=models.BooleanField(default=True, verbose_name='إظهار زر الإغلاق'),
        ),
        migrations.AlterField(
            model_name='branchmessage',
            name='message_type',
            field=models.CharField(choices=[('welcome', 'رسالة ترحيبية'), ('goal', 'هدف'), ('announcement', 'إعلان'), ('holiday', 'إجازة'), ('info', 'معلومات')], default='announcement', max_length=20, verbose_name='نوع الرسالة'),
        ),
    ]
