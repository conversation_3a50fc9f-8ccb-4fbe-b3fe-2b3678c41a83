# Generated by Django 4.2.21 on 2025-08-11 09:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0009_complaintnotification_simplenotification_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='GroupNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='محتوى الإشعار')),
                ('customer_name', models.CharField(blank=True, max_length=100, verbose_name='اسم العميل')),
                ('order_number', models.CharField(blank=True, max_length=50, verbose_name='رقم الطلب')),
                ('notification_type', models.CharField(choices=[('order_created', 'طلب جديد'), ('order_updated', 'تحديث طلب'), ('order_status_changed', 'تغيير حالة طلب'), ('inspection_scheduled', 'جدولة معاينة'), ('installation_scheduled', 'جدولة تركيب'), ('payment_received', 'دفعة جديدة'), ('general', 'عام')], default='general', max_length=25, verbose_name='نوع الإشعار')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('normal', 'عادي'), ('high', 'عالي'), ('urgent', 'عاجل')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الكائن المرتبط')),
                ('related_object_type', models.CharField(blank=True, max_length=50, verbose_name='نوع الكائن المرتبط')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_group_notifications', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('target_users', models.ManyToManyField(related_name='group_notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستخدمون المستهدفون')),
            ],
            options={
                'verbose_name': 'إشعار جماعي',
                'verbose_name_plural': 'الإشعارات الجماعية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GroupNotificationRead',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('read_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ القراءة')),
                ('notification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reads', to='accounts.groupnotification', verbose_name='الإشعار')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'قراءة إشعار جماعي',
                'verbose_name_plural': 'قراءات الإشعارات الجماعية',
                'indexes': [models.Index(fields=['notification', 'user'], name='accounts_gr_notific_a6e3e4_idx'), models.Index(fields=['read_at'], name='accounts_gr_read_at_adb42d_idx')],
                'unique_together': {('notification', 'user')},
            },
        ),
        migrations.AddIndex(
            model_name='groupnotification',
            index=models.Index(fields=['created_at'], name='accounts_gr_created_4b7d88_idx'),
        ),
        migrations.AddIndex(
            model_name='groupnotification',
            index=models.Index(fields=['notification_type'], name='accounts_gr_notific_06b696_idx'),
        ),
        migrations.AddIndex(
            model_name='groupnotification',
            index=models.Index(fields=['order_number'], name='accounts_gr_order_n_608a85_idx'),
        ),
    ]
