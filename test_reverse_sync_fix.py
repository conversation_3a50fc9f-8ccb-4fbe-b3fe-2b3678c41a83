#!/usr/bin/env python
"""
اختبار إصلاح المزامنة العكسية
"""

import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'homeupdate.settings')
django.setup()

def test_credentials_flow():
    """اختبار تدفق بيانات الاعتماد"""
    try:
        from odoo_db_manager.google_sync import create_sheets_service, GoogleSyncConfig
        
        print("=== اختبار تدفق بيانات الاعتماد ===")
        
        # التحقق من وجود إعداد نشط
        config = GoogleSyncConfig.get_active_config()
        if not config:
            print("❌ لا يوجد إعداد مزامنة نشط")
            return False
        
        print(f"✅ تم العثور على إعداد المزامنة: {config.name}")
        
        # اختبار الحصول على بيانات الاعتماد
        credentials = config.get_credentials()
        if not credentials:
            print("❌ لا يمكن قراءة بيانات الاعتماد")
            return False
        
        print("✅ تم الحصول على بيانات الاعتماد بنجاح")
        print(f"   نوع البيانات: {type(credentials)}")
        
        if isinstance(credentials, dict):
            print(f"   نوع الحساب: {credentials.get('type', 'غير محدد')}")
            print(f"   البريد الإلكتروني: {credentials.get('client_email', 'غير محدد')}")
        
        # اختبار إنشاء خدمة Google Sheets
        try:
            sheets_service = create_sheets_service(credentials)
            if sheets_service:
                print("✅ تم إنشاء خدمة Google Sheets بنجاح")
                return True
            else:
                print("❌ فشل في إنشاء خدمة Google Sheets")
                return False
        except Exception as e:
            print(f"❌ خطأ في إنشاء خدمة Google Sheets: {str(e)}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تدفق بيانات الاعتماد: {str(e)}")
        return False

def test_reverse_sync_view_fix():
    """اختبار إصلاح view المزامنة العكسية"""
    try:
        print("\n=== اختبار إصلاح view المزامنة العكسية ===")
        
        # قراءة كود view للتحقق من الإصلاح
        view_file = 'odoo_db_manager/google_sync_views.py'
        if os.path.exists(view_file):
            with open(view_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من الإصلاح
            if 'credentials = config.get_credentials()' in content:
                print("✅ تم إصلاح الحصول على بيانات الاعتماد")
            else:
                print("❌ لم يتم العثور على إصلاح بيانات الاعتماد")
                return False
            
            if 'sheets_service = create_sheets_service(credentials)' in content:
                print("✅ تم إصلاح تمرير بيانات الاعتماد لخدمة Google Sheets")
            else:
                print("❌ لم يتم العثور على إصلاح تمرير بيانات الاعتماد")
                return False
            
            # التحقق من عدم وجود الخطأ القديم
            if 'create_sheets_service(config)' not in content:
                print("✅ تم إزالة الخطأ القديم (تمرير config بدلاً من credentials)")
            else:
                print("⚠️ ما زال هناك استخدام خاطئ لـ create_sheets_service(config)")
            
            return True
        else:
            print("❌ ملف view غير موجود")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إصلاح view: {str(e)}")
        return False

def test_function_signature():
    """اختبار توقيع الدالة"""
    try:
        from odoo_db_manager.google_sync import reverse_sync_from_google_sheets
        import inspect
        
        print("\n=== اختبار توقيع دالة المزامنة العكسية ===")
        
        # فحص توقيع الدالة
        sig = inspect.signature(reverse_sync_from_google_sheets)
        params = list(sig.parameters.keys())
        
        print(f"✅ معاملات الدالة: {params}")
        
        expected_params = ['service', 'spreadsheet_id', 'admin_password', 'delete_old_data']
        if params == expected_params:
            print("✅ توقيع الدالة صحيح")
            return True
        else:
            print(f"❌ توقيع الدالة غير صحيح. المتوقع: {expected_params}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار توقيع الدالة: {str(e)}")
        return False

def main():
    print("🔄 بدء اختبار إصلاح المزامنة العكسية...")
    print("="*60)
    
    # اختبار تدفق بيانات الاعتماد
    credentials_test = test_credentials_flow()
    
    # اختبار إصلاح view
    view_fix_test = test_reverse_sync_view_fix()
    
    # اختبار توقيع الدالة
    function_test = test_function_signature()
    
    # النتيجة النهائية
    print("\n" + "="*60)
    print("📊 ملخص نتائج الاختبار:")
    print("="*60)
    
    if credentials_test and view_fix_test and function_test:
        print("🎉 تم إصلاح المزامنة العكسية بنجاح!")
        print("\n✅ الإصلاحات المطبقة:")
        print("   ✅ إصلاح الحصول على بيانات الاعتماد")
        print("   ✅ إصلاح تمرير credentials بدلاً من config")
        print("   ✅ إزالة الخطأ القديم")
        print("   ✅ توقيع الدالة صحيح")
        print("\n🚀 المزامنة العكسية جاهزة للاستخدام!")
        print("   الرابط: /odoo-db-manager/google-sync/")
        print("   القسم: 'المزامنة العكسية' في أسفل الصفحة")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    main()
