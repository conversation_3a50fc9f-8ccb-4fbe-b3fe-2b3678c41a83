/* Main Container */
.manufacturing-container {
    padding: 20px;
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.header h1 {
    color: #2c3e50;
    margin: 0;
    font-weight: 700;
}

.add-button {
    background-color: #3498db;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
}

.add-button:hover {
    background-color: #2980b9;
    color: white;
}

/* Filters */
.filters {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 25px;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
}

.filter-group .form-control {
    padding: 6px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

#search {
    min-width: 250px;
}

#search-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 6px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#search-btn:hover {
    background-color: #218838;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    margin-bottom: 25px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
}

.table.table-manufacturing {
    min-width: 1400px; /* Increase width */
}

.table.table-manufacturing thead th {
    white-space: nowrap; /* Prevent header text wrapping */
    vertical-align: middle;
}

.manufacturing-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 6px;
    overflow: hidden;
}

.manufacturing-table th,
.manufacturing-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.manufacturing-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    font-size: 0.8em;
    letter-spacing: 0.5px;
}

.manufacturing-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Status Badges - أغمق وأوضح للعين */
.badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: 700;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.badge.installation {
    background-color: #1565c0;
    color: white;
}

.badge.detail {
    background-color: #2e7d32;
    color: white;
}

.badge.accessory {
    background-color: #7b1fa2;
    color: white;
}

/* Status Dropdown - ألوان أغمق وأوضح */
.status-dropdown {
    position: relative;
    display: inline-block;
}

.status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.85em;
    font-weight: 700;
    cursor: pointer;
    min-width: 120px;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.2);
}

.status-badge.pending {
    background-color: #e6a700;
    color: #1a1a1a;
    text-shadow: 0 1px 2px rgba(255,255,255,0.3);
}

.status-badge.in_progress {
    background-color: #138496;
    color: white;
}

.status-badge.ready_for_installation {
    background-color: #e6a700;
    color: #1a1a1a;
    text-shadow: 0 1px 2px rgba(255,255,255,0.3);
}

.status-badge.completed {
    background-color: #1e7e34;
    color: white;
}

.status-badge.cancelled {
    background-color: #c82333;
    color: white;
}

.status-select {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 10;
}

/* Action Buttons */
.actions {
    display: flex;
    gap: 5px;
    justify-content: flex-start;
}

.actions .btn {
    padding: 4px 8px;
    font-size: 0.85em;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
}

.actions .btn i {
    margin: 0;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    padding: 15px 0;
}

.step-links {
    display: flex;
    gap: 10px;
    align-items: center;
}

.step-links a {
    color: #3498db;
    text-decoration: none;
    padding: 5px 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.3s;
}

.step-links a:hover {
    background-color: #f8f9fa;
    border-color: #3498db;
}

.step-links .current {
    padding: 5px 10px;
    color: #6c757d;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 25px;
    border-radius: 8px;
    width: 100%;
    max-width: 500px;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.close {
    position: absolute;
    left: 20px;
    top: 20px;
    color: #6c757d;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #2c3e50;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    margin-top: 25px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* Responsive */
@media (max-width: 992px) {
    .filters {
        flex-direction: column;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-group .form-control {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .manufacturing-table th,
    .manufacturing-table td {
        padding: 8px 10px;
        font-size: 0.9em;
    }
    
    .actions {
        flex-direction: column;
        gap: 5px;
    }
    
    .actions .btn {
        width: 100%;
    }
}
