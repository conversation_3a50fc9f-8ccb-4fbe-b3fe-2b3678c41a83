{% extends 'base.html' %}
{% load static %}
{% load humanize %}
{% load i18n %}

{% block title %}تفاصيل أمر التصنيع - {{ manufacturing_order.order.order_number|default:manufacturing_order.order.id }}{% endblock %}

{% block extra_css %}
<style>
    /* تحسين مظهر جدول عناصر الطلب */
    #order-items-card .table {
        margin-bottom: 0;
    }

    #order-items-card .table thead th {
        border-bottom: 2px solid #007bff;
        font-weight: 600;
        vertical-align: middle;
    }

    #order-items-card .table tbody tr {
        transition: background-color 0.2s ease;
    }

    #order-items-card .table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    #order-items-card .badge {
        font-size: 0.85em;
        padding: 0.5em 0.75em;
    }

    .print-btn {
        transition: all 0.2s ease;
    }

    .print-btn:hover {
        transform: scale(1.05);
    }

    /* تحسين الإحصائيات */
    .stats-section {
        border: 1px solid #e9ecef;
        border-radius: 8px;
    }

    .stats-section .col-md-4:not(:last-child) {
        border-right: 1px solid #e9ecef;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>تفاصيل أمر التصنيع</h4>
                        </div>
                        <div class="col-md-6 text-end">
                            <a href="{% url 'manufacturing:order_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة للقائمة
                            </a>
                            {% if perms.manufacturing.change_manufacturingorder %}
                                <a href="{% url 'manufacturing:order_update' manufacturing_order.id %}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                            {% endif %}
                            {% if perms.manufacturing.delete_manufacturingorder %}
                                <a href="{% url 'manufacturing:order_delete' manufacturing_order.id %}" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>معلومات أساسية</h5>
                            <table class="table table-sm">
                                <tr>
                                    <th width="30%">رقم الطلب:</th>
                                    <td>{{ manufacturing_order.order.order_number|default:manufacturing_order.order.id }}</td>
                                </tr>
                                <tr>
                                    <th>رقم العقد:</th>
                                    <td>{{ manufacturing_order.contract_number|default:'-' }}</td>
                                </tr>
                                {% if manufacturing_order.order.contract_number_2 %}
                                <tr>
                                    <th>رقم العقد الإضافي 2:</th>
                                    <td>{{ manufacturing_order.order.contract_number_2 }}</td>
                                </tr>
                                {% endif %}
                                {% if manufacturing_order.order.contract_number_3 %}
                                <tr>
                                    <th>رقم العقد الإضافي 3:</th>
                                    <td>{{ manufacturing_order.order.contract_number_3 }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>رقم الفاتورة:</th>
                                    <td>{{ manufacturing_order.invoice_number|default:'-' }}</td>
                                </tr>
                                {% if manufacturing_order.order.invoice_number_2 %}
                                <tr>
                                    <th>رقم الفاتورة الإضافي 2:</th>
                                    <td>{{ manufacturing_order.order.invoice_number_2 }}</td>
                                </tr>
                                {% endif %}
                                {% if manufacturing_order.order.invoice_number_3 %}
                                <tr>
                                    <th>رقم الفاتورة الإضافي 3:</th>
                                    <td>{{ manufacturing_order.order.invoice_number_3 }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>نوع الطلب الأصلي:</th>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ manufacturing_order.order.get_selected_type_display|default:'-' }}
                                        </span>
                                    </td>
                                </tr>

                                <tr>
                                    <th>حالة الطلب الأصلي:</th>
                                    <td>
                                        <span class="badge bg-primary">
                                            {{ manufacturing_order.order.get_order_status_display|default:'-' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>حالة أمر التصنيع:</th>
                                    <td>
                                        {% if manufacturing_order.status == 'pending_approval' %}
                                            <span class="badge bg-warning text-dark">
                                                {{ manufacturing_order.get_status_display }}
                                            </span>
                                        {% elif manufacturing_order.status == 'pending' %}
                                            <span class="badge bg-info text-white">
                                                {{ manufacturing_order.get_status_display }}
                                            </span>
                                        {% elif manufacturing_order.status == 'in_progress' %}
                                            <span class="badge bg-primary text-white">
                                                {{ manufacturing_order.get_status_display }}
                                            </span>
                                        {% elif manufacturing_order.status == 'ready_install' %}
                                            <span class="badge bg-success text-white">
                                                {{ manufacturing_order.get_status_display }}
                                            </span>
                                        {% elif manufacturing_order.status == 'completed' %}
                                            <span class="badge bg-success text-white">
                                                {{ manufacturing_order.get_status_display }}
                                            </span>
                                        {% elif manufacturing_order.status == 'delivered' %}
                                            <span class="badge bg-dark text-white">
                                                {{ manufacturing_order.get_status_display }}
                                            </span>
                                        {% elif manufacturing_order.status == 'rejected' %}
                                            <span class="badge bg-danger text-white">
                                                {{ manufacturing_order.get_status_display }}
                                            </span>
                                        {% elif manufacturing_order.status == 'cancelled' %}
                                            <span class="badge bg-secondary text-white">
                                                {{ manufacturing_order.get_status_display }}
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary text-white">
                                            {{ manufacturing_order.get_status_display }}
                                        </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>تاريخ الطلب:</th>
                                    <td>{{ manufacturing_order.order_date|date:'Y-m-d' }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ التسليم المتوقع:</th>
                                    <td>{{ manufacturing_order.expected_delivery_date|date:'Y-m-d'|default:'-' }}</td>
                                </tr>
                                {% if manufacturing_order.completion_date %}
                                <tr>
                                    <th>تاريخ الإكمال:</th>
                                    <td>{{ manufacturing_order.completion_date|date:'Y-m-d H:i' }}</td>
                                </tr>
                                {% endif %}
                                {% if manufacturing_order.delivery_date %}
                                <tr>
                                    <th>تاريخ التسليم:</th>
                                    <td>{{ manufacturing_order.delivery_date|date:'Y-m-d H:i' }}</td>
                                </tr>
                                <tr>
                                    <th>رقم إذن التسليم:</th>
                                    <td>{{ manufacturing_order.delivery_permit_number|default:'-' }}</td>
                                </tr>
                                <tr>
                                    <th>اسم المستلم:</th>
                                    <td>{{ manufacturing_order.delivery_recipient_name|default:'-' }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>معلومات العميل والمستخدمين</h5>
                            <table class="table table-sm">
                                <tr>
                                    <th width="30%">العميل:</th>
                                    <td>{{ manufacturing_order.order.customer.name|default:'-' }}</td>
                                </tr>
                                <tr>
                                    <th>البائع:</th>
                                    <td>
                                        {% if manufacturing_order.order.salesperson %}
                                            <span class="badge bg-success">
                                                {{ manufacturing_order.order.salesperson.name }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>الفرع:</th>
                                    <td>{{ manufacturing_order.order.branch.name|default:'-' }}</td>
                                </tr>
                                <tr>
                                    <th>منشئ الطلب الأصلي:</th>
                                    <td>
                                        {% if manufacturing_order.order.created_by %}
                                            <span class="badge bg-warning text-dark">
                                                {{ manufacturing_order.order.created_by.get_full_name|default:manufacturing_order.order.created_by.username }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>منشئ أمر التصنيع:</th>
                                    <td>
                                        {% if manufacturing_order.order.created_by %}
                                            <span class="badge bg-info">
                                                {{ manufacturing_order.order.created_by.get_full_name|default:manufacturing_order.order.created_by.username }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>تاريخ إنشاء الطلب الأصلي:</th>
                                    <td>{{ manufacturing_order.order.order_date|date:'Y-m-d H:i'|default:'-' }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ إنشاء أمر التصنيع:</th>
                                    <td>{{ manufacturing_order.created_at|date:'Y-m-d H:i' }}</td>
                                </tr>
                                <tr>
                                    <th>آخر تحديث:</th>
                                    <td>{{ manufacturing_order.updated_at|date:'Y-m-d H:i' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                                
                    <!-- ملفات العقد والمعاينة -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>الملفات المرفقة</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h6>ملف العقد</h6>
                                            {% if manufacturing_order.order.contract_file %}
                                                <div class="mb-2">
                                                    <a href="{{ manufacturing_order.order.contract_file.url }}" target="_blank" class="btn btn-success">
                                                        <i class="fas fa-file-pdf"></i> عرض ملف العقد (محلي)
                                                    </a>
                                                </div>
                                                {% if manufacturing_order.order.is_contract_uploaded_to_drive and manufacturing_order.order.contract_google_drive_file_url %}
                                                <div>
                                                    <a href="{{ manufacturing_order.order.contract_google_drive_file_url }}" target="_blank" class="btn btn-outline-success">
                                                        <i class="fab fa-google-drive"></i> عرض في Google Drive
                                                    </a>
                                                </div>
                                                {% endif %}
                                            {% else %}
                                                <p class="text-muted">لم يتم رفع ملف العقد</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h6>ملف المعاينة</h6>
                                            {% if manufacturing_order.order.related_inspection_type == 'customer_side' %}
                                                <div class="mb-2">
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="fas fa-user-check"></i> معاينة طرف العميل
                                                    </span>
                                                </div>
                                            {% elif manufacturing_order.order.related_inspection and manufacturing_order.order.related_inspection.inspection_file %}
                                                <div class="mb-2">
                                                    <a href="{{ manufacturing_order.order.related_inspection.inspection_file.url }}" target="_blank" class="btn btn-info">
                                                        <i class="fas fa-file-alt"></i> {{ manufacturing_order.order.customer.name }} - ملف المعاينة
                                                    </a>
                                                </div>
                                                {% if manufacturing_order.order.related_inspection.is_uploaded_to_drive and manufacturing_order.order.related_inspection.google_drive_file_url %}
                                                <div>
                                                    <a href="{{ manufacturing_order.order.related_inspection.google_drive_file_url }}" target="_blank" class="btn btn-outline-info">
                                                        <i class="fab fa-google-drive"></i> {{ manufacturing_order.order.customer.name }} - Google Drive
                                                    </a>
                                                </div>
                                                {% endif %}
                                            {% else %}
                                                <p class="text-muted">لم يتم رفع ملف المعاينة</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                    </div>
                </div>
            </div>
            
                    <!-- عناصر الطلب -->
                    {% if order_items %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            {% if user.is_superuser or user.is_factory_manager %}
                                <!-- بطاقة عناصر الطلب لمسؤول المصنع (بدون أسعار) -->
                                <div class="card" id="order-items-card">
                                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            <i class="fas fa-boxes me-2"></i>عناصر الطلب المرفقة
                                        </h5>
                                        <button class="btn btn-light btn-sm" onclick="printOrderItems()" title="طباعة عناصر الطلب">
                                            <i class="fas fa-print me-1"></i>طباعة
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <!-- جدول عناصر الطلب -->
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-primary">
                                                    <tr>
                                                        <th width="5%" class="text-center">#</th>
                                                        <th width="40%">
                                                            <i class="fas fa-cube me-2"></i>اسم العنصر
                                                        </th>
                                                        <th width="15%" class="text-center">
                                                            <i class="fas fa-sort-numeric-up me-2"></i>الكمية
                                                        </th>
                                                        <th width="40%">
                                                            <i class="fas fa-sticky-note me-2"></i>الملاحظات
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for item in order_items %}
                                                    <tr>
                                                        <td class="text-center">
                                                            <span class="badge bg-secondary">{{ forloop.counter }}</span>
                                                        </td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <i class="fas fa-box text-primary me-2"></i>
                                                                <strong>{{ item.product.name }}</strong>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <span class="badge bg-info fs-6">{{ item.quantity }}</span>
                                                        </td>
                                                        <td>
                                                            {% if item.specifications %}
                                                                <span class="text-muted">{{ item.specifications }}</span>
                                                            {% elif item.notes %}
                                                                <span class="text-muted">{{ item.notes }}</span>
                                                            {% else %}
                                                                <span class="text-muted fst-italic">لا توجد ملاحظات</span>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                    {% empty %}
                                                    <tr>
                                                        <td colspan="4" class="text-center text-muted py-4">
                                                            <i class="fas fa-inbox fa-2x mb-2"></i>
                                                            <br>لا توجد عناصر في هذا الطلب
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- إحصائيات سريعة -->
                                        {% if order_items %}
                                        <div class="mt-3 p-3 bg-light rounded">
                                            <div class="row text-center">
                                                <div class="col-md-4">
                                                    <div class="d-flex align-items-center justify-content-center">
                                                        <i class="fas fa-list-ol text-primary me-2"></i>
                                                        <div>
                                                            <h6 class="text-muted mb-0">إجمالي العناصر</h6>
                                                            <h4 class="text-primary mb-0">{{ order_items|length }}</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="d-flex align-items-center justify-content-center">
                                                        <i class="fas fa-calculator text-success me-2"></i>
                                                        <div>
                                                            <h6 class="text-muted mb-0">إجمالي الكمية</h6>
                                                            <h4 class="text-success mb-0">{{ total_quantity|default:0 }}</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="d-flex align-items-center justify-content-center">
                                                        <i class="fas fa-cogs text-warning me-2"></i>
                                                        <div>
                                                            <h6 class="text-muted mb-0">حالة التصنيع</h6>
                                                            <span class="badge bg-warning fs-6">قيد التصنيع</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
            
                    <!-- ملاحظات -->
            {% if manufacturing_order.notes %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>ملاحظات</h5>
                            <div class="alert alert-info">
                    {{ manufacturing_order.notes|linebreaks }}
                            </div>
                </div>
            </div>
            {% endif %}

                    <!-- سبب الرفض -->
                    {% if manufacturing_order.status == 'rejected' and manufacturing_order.rejection_reason %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>سبب الرفض</h5>
                            <div class="alert alert-danger">
                                {{ manufacturing_order.rejection_reason|linebreaks }}
                            </div>
        </div>
                </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });

    // دالة طباعة عناصر الطلب
    function printOrderItems() {
        // إنشاء نافذة طباعة جديدة
        const printWindow = window.open('', '_blank', 'width=800,height=600');

        // الحصول على محتوى البطاقة
        const cardContent = document.getElementById('order-items-card');

        if (!cardContent) {
            alert('لا توجد عناصر للطباعة');
            return;
        }

        // إنشاء محتوى HTML للطباعة
        const printContent = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>عناصر الطلب - أمر التصنيع</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        background: white;
                        color: #333;
                        padding: 20px;
                    }
                    .print-header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #007bff;
                        padding-bottom: 15px;
                    }
                    .print-header h1 {
                        color: #007bff;
                        margin-bottom: 10px;
                    }
                    .print-info {
                        background: #f8f9fa;
                        padding: 15px;
                        border-radius: 8px;
                        margin-bottom: 20px;
                    }
                    .card {
                        border: 1px solid #dee2e6;
                        border-radius: 8px;
                        margin-bottom: 15px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }
                    .card-body {
                        padding: 15px;
                    }
                    .badge {
                        font-size: 0.9em;
                    }
                    .stats-section {
                        background: #e9ecef;
                        padding: 15px;
                        border-radius: 8px;
                        margin-top: 20px;
                    }
                    .table {
                        width: 100%;
                        margin-bottom: 1rem;
                        border-collapse: collapse;
                    }
                    .table th, .table td {
                        padding: 12px;
                        border: 1px solid #dee2e6;
                        text-align: right;
                    }
                    .table thead th {
                        background-color: #007bff;
                        color: white;
                        font-weight: bold;
                    }
                    .table tbody tr:nth-child(even) {
                        background-color: #f8f9fa;
                    }
                    @media print {
                        body { margin: 0; padding: 15px; }
                        .no-print { display: none !important; }
                    }
                </style>
            </head>
            <body>
                <div class="print-header">
                    <h1><i class="fas fa-boxes"></i> عناصر الطلب المرفقة</h1>
                    <div class="print-info">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>رقم الطلب:</strong> {{ manufacturing_order.order.order_number|default:manufacturing_order.order.id }}
                            </div>
                            <div class="col-md-6">
                                <strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-EG')}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="items-content">
                    ${cardContent.querySelector('.card-body').innerHTML}
                </div>

                <div class="print-footer" style="margin-top: 30px; text-align: center; font-size: 0.9em; color: #666;">
                    <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الطلبات</p>
                </div>
            </body>
            </html>
        `;

        // كتابة المحتوى في النافذة الجديدة
        printWindow.document.write(printContent);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);
        };
    }
</script>
{% endblock %}


