{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{% if form.instance.pk %}تعديل{% else %}إنشاء{% endif %} أمر تصنيع{% endblock %}

{% block extra_css %}
<style>
    .form-control[readonly] {
        background-color: #f8f9fa;
        cursor: not-allowed;
    }
    .file-link {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        margin-top: 5px;
    }
    .file-link .fas {
        font-size: 1.2em;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-{% if form.instance.pk %}edit{% else %}plus{% endif %} me-2"></i>
                            {% if form.instance.pk %}تعديل{% else %}إنشاء{% endif %} أمر تصنيع
                        </h5>
                        <div>
                            <a href="{% if form.instance.pk %}{% url 'manufacturing:order_detail' form.instance.pk %}{% else %}{% url 'manufacturing:order_list' %}{% endif %}" 
                               class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-arrow-right me-1"></i> رجوع
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="manufacturingOrderForm">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="border-bottom pb-2 mb-3">معلومات الطلب</h6>
                                
                                <div class="mb-3">
                                    <label class="form-label">اسم العميل</label>
                                    <input type="text" class="form-control" value="{{ form.instance.order.customer.name }}" readonly>
                                    <small class="form-text text-muted">يتم تعبئة اسم العميل تلقائياً من الطلب</small>
                                </div>
                                
                                <input type="hidden" name="order" value="{{ form.instance.order.id }}">

                                <div class="mb-3">
                                    <label class="form-label">نوع الطلب</label>
                                    <input type="text" class="form-control" value="{{ form.instance.order.get_order_type_display }}" readonly>
                                    <input type="hidden" name="order_type" value="{{ form.instance.order.get_order_type_display }}">
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">
                                        {{ form.status.label }}
                                        {% if form.status.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {% render_field form.status class="form-select" %}
                                    <div class="invalid-feedback">
                                        {{ form.status.errors }}
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.order_date.id_for_label }}" class="form-label">
                                        {{ form.order_date.label }}
                                        {% if form.order_date.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {% render_field form.order_date type="date" class="form-control" %}
                                    <div class="invalid-feedback">
                                        {{ form.order_date.errors }}
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.expected_delivery_date.id_for_label }}" class="form-label">
                                        {{ form.expected_delivery_date.label }}
                                        {% if form.expected_delivery_date.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {% render_field form.expected_delivery_date type="date" class="form-control" %}
                                    <div class="invalid-feedback">
                                        {{ form.expected_delivery_date.errors }}
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h6 class="border-bottom pb-2 mb-3">مستندات الطلب</h6>
                                
                                <div class="mb-3">
                                    <label for="{{ form.contract_number.id_for_label }}" class="form-label">
                                        {{ form.contract_number.label }}
                                    </label>
                                    <input type="text" class="form-control" value="{{ form.instance.contract_number|default:'-' }}" readonly>
                                    <input type="hidden" name="contract_number" value="{{ form.instance.contract_number|default:'' }}">
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.invoice_number.id_for_label }}" class="form-label">
                                        {{ form.invoice_number.label }}
                                    </label>
                                    <input type="text" class="form-control" value="{{ form.instance.invoice_number|default:'-' }}" readonly>
                                    <input type="hidden" name="invoice_number" value="{{ form.instance.invoice_number|default:'' }}">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-file-contract me-1"></i>
                                        ملف العقد
                                    </label>
                                    {% with order=form.instance.order %}
                                        {% if order.contract_file %}
                                            <div class="file-link">
                                                <a href="{{ order.contract_file.url }}" target="_blank" class="text-primary">
                                                    <i class="fas fa-external-link-alt"></i> عرض ملف العقد
                                                </a>
                                                {% if order.contract_google_drive_file_url %}
                                                    <a href="{{ order.contract_google_drive_file_url }}" 
                                                       target="_blank" class="text-success ms-2">
                                                        <i class="fab fa-google-drive"></i> فتح في جوجل درايف
                                                    </a>
                                                {% endif %}
                                            </div>
                                        {% else %}
                                            <p class="text-muted">لا يوجد ملف عقد مرفق في الطلب</p>
                                        {% endif %}
                                    {% endwith %}
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-search me-1"></i>
                                        ملف المعاينة
                                    </label>
                                    {% with order=form.instance.order %}
                                        {% if order.inspection_file %}
                                            <div class="file-link">
                                                <a href="{{ order.inspection_file.url }}" target="_blank" class="text-primary">
                                                    <i class="fas fa-external-link-alt"></i> عرض ملف المعاينة
                                                </a>
                                                {% if order.inspection_google_drive_file_url %}
                                                    <a href="{{ order.inspection_google_drive_file_url }}" 
                                                       target="_blank" class="text-success ms-2">
                                                        <i class="fab fa-google-drive"></i> فتح في جوجل درايف
                                                    </a>
                                                {% endif %}
                                            </div>
                                        {% else %}
                                            <p class="text-muted">لا يوجد ملف معاينة مرفق في الطلب</p>
                                        {% endif %}
                                    {% endwith %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.exit_permit_number.id_for_label }}" class="form-label">
                                        {{ form.exit_permit_number.label }}
                                    </label>
                                    {% render_field form.exit_permit_number class="form-control" %}
                                    <div class="invalid-feedback">
                                        {{ form.exit_permit_number.errors }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <h6 class="border-bottom pb-2 mb-3">تفاصيل إضافية</h6>
                                
                                <div class="mb-3">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">
                                        {{ form.notes.label }}
                                    </label>
                                    {% render_field form.notes class="form-control" rows="3" %}
                                    <div class="invalid-feedback">
                                        {{ form.notes.errors }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> حفظ
                            </button>
                            <a href="{% if form.instance.pk %}{% url 'manufacturing:order_detail' form.instance.pk %}{% else %}{% url 'manufacturing:order_list' %}{% endif %}" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
    $(document).ready(function() {
        // Initialize form validation
        const form = document.getElementById('manufacturingOrderForm');
        
        // Add validation on form submission
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
        
        // Initialize date picker if needed
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            rtl: true,
            autoclose: true,
            language: 'ar',
            todayHighlight: true
        });
        
        // Initialize select2 for better select inputs
        $('select').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dropdownAutoWidth: true,
            language: 'ar'
        });
    });
</script>
{% endblock %}
