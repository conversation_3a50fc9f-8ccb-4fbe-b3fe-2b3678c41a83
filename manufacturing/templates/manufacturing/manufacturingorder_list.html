{% extends 'base.html' %}
{% load static %}
{% load humanize %}
{% load unified_status_tags %}
{% load order_extras %}

{% block title %}قائمة أوامر التصنيع{% endblock %}

{% block extra_head %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block extra_css %}
<meta name="csrf-token" content="{{ csrf_token }}">
<style>
/* استخدام متغيرات الألوان من النظام */
.container {
    max-width: 100%;
    padding: 1rem;
}

/* البطاقات الإحصائية */
.stats-card {
    border: 1px solid var(--neutral);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card .icon {
    font-size: 2rem;
    opacity: 0.8;
}

/* تصميم الجدول الرئيسي - نفس تصميم العملاء */
.main-table-card {
    border: 1px solid var(--neutral);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.main-table-card .card-header {
    background-color: var(--primary);
    color: white;
    border-bottom: none;
    padding: 1rem 1.25rem;
    border-radius: 8px 8px 0 0;
}

.main-table-card .card-header h5 {
    margin: 0;
    font-weight: 600;
}

/* قسم البحث والتصفية */
.filter-card {
    border: 1px solid var(--neutral);
    border-radius: 8px;
    margin-bottom: 1rem;
}

.filter-card .card-body {
    padding: 1rem;
}

/* تصميم الجدول */
.table-responsive {
    border-radius: 0 0 8px 8px;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--neutral);
    color: #060606;
    font-weight: 600;
    border: none;
    padding: 1rem 0.75rem;
    text-align: center;
    vertical-align: middle;
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    text-align: center;
    border-bottom: 1px solid var(--neutral);
}

.table tbody tr:hover {
    background-color: rgba(139, 115, 90, 0.05);
}

/* أزرار الإجراءات في صف واحد */
.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
    align-items: center;
}

.action-btn {
    padding: 0.375rem 0.5rem;
    border-radius: 4px;
    border: 1px solid;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-view {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
}

.btn-view:hover {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
    opacity: 0.9;
}

.btn-edit {
    background-color: var(--light-accent);
    border-color: var(--light-accent);
    color: var(--dark-text);
}

.btn-edit:hover {
    background-color: var(--light-accent);
    border-color: var(--light-accent);
    color: var(--dark-text);
    opacity: 0.9;
}

.btn-delete {
    background-color: var(--alert);
    border-color: var(--alert);
    color: white;
}

.btn-delete:hover {
    background-color: var(--alert);
    border-color: var(--alert);
    color: white;
    opacity: 0.9;
}

/* تصميم زر الحالة */
.status-btn {
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.status-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* ألوان الحالات - أغمق وأوضح للعين */
.status-pending_approval { 
    background-color: #0056b3; 
    color: white; 
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}
.status-pending { 
    background-color: #e6a700; 
    color: #1a1a1a; 
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(255,255,255,0.3);
}
.status-in_progress { 
    background-color: #138496; 
    color: white; 
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}
.status-completed { 
    background-color: #1e7e34; 
    color: white; 
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}
.status-rejected { 
    background-color: #c82333; 
    color: white; 
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}
.status-cancelled { 
    background-color: #545b62; 
    color: white; 
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}
.status-delivered {
    background-color: #20c997;
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.status-delivered:hover {
    background-color: #1aa085;
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.2);
}

/* تصميم التنقل بين الصفحات */
.pagination {
    justify-content: center;
    margin-top: 2rem;
}

.page-link {
    color: var(--primary);
    border-color: var(--neutral);
    padding: 0.5rem 0.75rem;
}

.page-link:hover {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
}

/* إصلاح مشكلة تداخل المودال مع SweetAlert */
.swal2-container {
    z-index: 10000 !important;
}

.modal-backdrop {
    z-index: 1040;
}

.modal {
    z-index: 1050;
}

/* منع ظهور الشاشة السوداء بعد إغلاق المودال - حل قوي */
body.modal-open {
    overflow: auto !important;
    padding-right: 0 !important;
}

/* إزالة backdrop فوراً */
.modal-backdrop.fade {
    opacity: 0 !important;
    transition: none !important;
}

.modal-backdrop {
    display: none !important;
    opacity: 0 !important;
}

/* حل إضافي لمنع الشاشة السوداء */
body:not(.modal-open) .modal-backdrop {
    display: none !important;
}

/* التأكد من عدم تعارض SweetAlert مع المودال */
.swal2-shown {
    overflow: auto !important;
    padding-right: 0 !important;
}

.swal2-height-auto {
    height: auto !important;
}

/* تحسين مظهر شارة الرد على الرفض */
.reply-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.reply-badge i {
    font-size: 0.8em;
}

.reply-badge.bg-info {
    background-color: #17a2b8 !important;
    color: white !important;
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
}

/* ألوان أنواع الطلبات */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 600;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.badge.bg-success {
    background-color: #198754 !important;
    color: white !important;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
    color: white !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #212529 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
    color: white !important;
}

.badge i {
    font-size: 0.8em;
}

/* تحسينات المؤشرات البصرية للموافقة */
.approval-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.approval-indicator i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.approval-indicator i:hover {
    transform: scale(1.1);
}

.approval-indicator .text-success {
    color: #28a745 !important;
}

.approval-indicator .text-danger {
    color: #dc3545 !important;
}

/* تحسين مظهر الأيقونات */
.approval-indicator .fa-check-circle {
    color: #28a745 !important;
    font-size: 1.4rem;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    cursor: pointer;
}

.approval-indicator .fa-times-circle {
    color: #dc3545 !important;
    font-size: 1.4rem;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    cursor: pointer;
}

/* تأثيرات hover */
.approval-indicator .fa-check-circle:hover {
    color: #198754 !important;
    transform: scale(1.2);
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.approval-indicator .fa-times-circle:hover {
    color: #bb2d3b !important;
    transform: scale(1.2);
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* تحسين مظهر عمود الموافقة */
.approval-column {
    text-align: center;
    vertical-align: middle;
    padding: 0.5rem;
}

/* تحسين مظهر المؤشرات في الجدول */
.approval-column .approval-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    min-height: 2rem;
}

/* تحسين مظهر الأزرار في عمود الموافقة */
.approval-column .action-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

/* تحسين مظهر شارة الرد على الرفض */
.reply-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.re-approve-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات responsive */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 0.125rem;
    }
    
    .action-btn {
        width: 100%;
        padding: 0.25rem;
        font-size: 0.75rem;
    }
    
    .status-btn {
        min-width: 100px;
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }
    
    .table {
        font-size: 0.875rem;
    }
    
    .badge {
        font-size: 0.65rem;
        padding: 0.25rem 0.375rem;
    }
}
    .filter-card .form-label {
        font-weight: 600;
        color: #3c2415;
        margin-bottom: 0.25rem;
    }
    .filter-card .form-select, .filter-card .form-control {
        font-size: 1rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        min-width: 100px;
    }
    .filter-card .btn, .filter-card .btn-group .btn {
        font-size: 1rem;
        padding: 0.375rem 1rem;
        border-radius: 0.375rem;
    }
.orders-table th, .orders-table td, .manufacturing-table th, .manufacturing-table td {
    padding: 0.2rem 0.3rem !important;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
}
.manufacturing-table th {
    white-space: normal !important;
    word-break: break-word !important;
    line-height: 1.3;
    font-size: 0.95em;
}
.manufacturing-table {
    border-spacing: 0 1px;
}
/* تحسين مظهر الروابط */
.table a {
    transition: all 0.2s ease;
}
.table a:hover {
    text-decoration: underline !important;
    opacity: 0.8;
}

/* تحسين شكل المحادثة في مودال الرفض */
.rejection-conversation {
    max-height: 400px;
    overflow-y: auto;
}

.rejection-conversation .alert {
    border: none;
    border-left: 4px solid;
    margin-bottom: 1rem;
}

.rejection-conversation .alert-danger {
    border-left-color: #dc3545;
    background-color: #f8d7da;
}

.rejection-conversation .alert-info {
    border-left-color: #0dcaf0;
    background-color: #d1ecf1;
}

.rejection-conversation .fas {
    color: inherit;
    opacity: 0.8;
}

/* تحسين أزرار المودال */
#hasReplyButtons .btn {
    margin: 0 0.25rem;
}

#hasReplyButtons .btn i {
    margin-right: 0.5rem;
}

/* تحسين شكل badge الرد */
.reply-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.reply-badge i {
    margin-right: 0.25rem;
}

/* تحسين زر الموافقة مرة أخرى */
.re-approve-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.re-approve-btn i {
    margin-right: 0.25rem;
}
</style>
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="container-fluid">
    <!-- ملاحظة توضيحية -->
    <div class="alert alert-info mb-4" role="alert">
        <i class="fas fa-info-circle me-2"></i>
        <strong>ملاحظة:</strong> جميع الإحصائيات والبيانات المعروضة مفلترة حسب السنة الافتراضية: <strong>{{ default_year }}</strong>
        <small class="d-block mt-1">يمكن تغيير السنة الافتراضية من لوحة إدارة النظام</small>
    </div>

    <!-- البطاقات الإحصائية -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase mb-1">إجمالي الطلبات</h6>
                            <h2 class="mb-0">{{ total_orders|default:0|intcomma }}</h2>
                        </div>
                        <div class="icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                    </div>
                    <div class="mt-3 small">
                        <span class="text-white-50">
                            إجمالي أوامر التصنيع - {{ default_year }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase mb-1">مكتمل</h6>
                            <h2 class="mb-0">{{ completed_orders|default:0|intcomma }}</h2>
                        </div>
                        <div class="icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="mt-3 small">
                        <span class="text-white-50">تم الانتهاء من التصنيع</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase mb-1">قيد التنفيذ</h6>
                            <h2 class="mb-0">{{ in_progress_orders|default:0|intcomma }}</h2>
                        </div>
                        <div class="icon">
                            <i class="fas fa-cog fa-spin"></i>
                        </div>
                    </div>
                    <div class="mt-3 small">
                        <span class="text-muted">قيد التصنيع حالياً</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase mb-1">قيد الانتظار</h6>
                            <h2 class="mb-0">{{ pending_orders|default:0|intcomma }}</h2>
                        </div>
                        <div class="icon">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="mt-3 small">
                        <span class="text-white-50">في انتظار المعالجة</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم البحث والتصفية -->
    <div class="card filter-card">
        <div class="card-body">
            <form id="filterForm" method="get">
                <div class="row g-3 align-items-end">
                    <div class="col-md-2">
                        <label for="status" class="form-label">حالة الطلب:</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">الكل</option>
                            <option value="pending_approval" {% if request.GET.status == 'pending_approval' %}selected{% endif %}>قيد الموافقة</option>
                            <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                            <option value="in_progress" {% if request.GET.status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                            <option value="ready_install" {% if request.GET.status == 'ready_install' %}selected{% endif %}>جاهز للتركيب</option>
                            <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>مكتمل</option>
                            <option value="delivered" {% if request.GET.status == 'delivered' %}selected{% endif %}>تم التسليم</option>
                            <option value="rejected" {% if request.GET.status == 'rejected' %}selected{% endif %}>مرفوض</option>
                            <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="branch" class="form-label">الفرع:</label>
                        <select name="branch" id="branch" class="form-select">
                            <option value="">الكل</option>
                            {% for branch in branches %}
                            <option value="{{ branch.id }}" {% if request.GET.branch == branch.id|stringformat:'s' %}selected{% endif %}>
                                {{ branch.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date_from" class="form-label">من تاريخ:</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request.GET.date_from }}">
                    </div>
                    <div class="col-md-2">
                        <label for="date_to" class="form-label">إلى تاريخ:</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request.GET.date_to }}">
                    </div>
                    <div class="col-md-2">
                        <label for="search" class="form-label">بحث:</label>
                        <input type="text" name="search" id="search" class="form-control" placeholder="ابحث..." value="{{ request.GET.search }}">
                    </div>
                    <div class="col-md-1">
                        <label for="page_size" class="form-label">عدد الصفوف:</label>
                        <select name="page_size" id="page_size" class="form-select">
                            <option value="10" {% if request.GET.page_size == '10' %}selected{% endif %}>10</option>
                            <option value="25" {% if request.GET.page_size == '25' or not request.GET.page_size %}selected{% endif %}>25</option>
                            <option value="50" {% if request.GET.page_size == '50' %}selected{% endif %}>50</option>
                            <option value="100" {% if request.GET.page_size == '100' %}selected{% endif %}>100</option>
                        </select>
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i> تصفية
                            </button>
                            <a href="{% url 'manufacturing:order_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-redo"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- الجدول الرئيسي -->
    <div class="card" style="border-color: var(--neutral);">
        <div class="card-header" style="background-color: var(--primary); color: white;">
            <h5 class="mb-0">
                <i class="fas fa-industry"></i> أوامر التصنيع
                <small class="ms-2 opacity-75">
                    <i class="fas fa-calendar-alt"></i> السنة: {{ default_year }}
                </small>
            </h5>
        </div>
        <div class="card-body">
            {% if manufacturing_orders %}
            <div class="table-responsive">
                <table class="table table-striped table-hover manufacturing-table">
                    <thead>
                        <tr>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 40px; word-break: break-word; white-space: normal;">#</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 90px; word-break: break-word; white-space: normal;">رقم الطلب</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 90px; word-break: break-word; white-space: normal;">النوع</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 80px; word-break: break-word; white-space: normal;">رقم العقد</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 80px; word-break: break-word; white-space: normal;">رقم الفاتورة</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 90px; word-break: break-word; white-space: normal;">العميل</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 80px; word-break: break-word; white-space: normal;">البائع</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 80px; word-break: break-word; white-space: normal;">الفرع</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 100px; word-break: break-word; white-space: normal;">تاريخ الطلب</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 100px; word-break: break-word; white-space: normal;">تاريخ التسليم</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 90px; word-break: break-word; white-space: normal;">الحالة</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 100px; word-break: break-word; white-space: normal;">معلومات التسليم</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 90px; word-break: break-word; white-space: normal;">موافقة العقد</th>
                            <th class="text-center" style="padding: 0.3rem 0.4rem; min-width: 70px; word-break: break-word; white-space: normal;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in manufacturing_orders %}
                        <tr data-order-id="{{ order.id }}" data-order-type="{% if 'installation' in order.order.get_selected_types_list %}installation{% elif 'tailoring' in order.order.get_selected_types_list %}tailoring{% elif 'accessory' in order.order.get_selected_types_list %}accessory{% else %}manufacturing{% endif %}">
                            <td>{{ forloop.counter }}</td>
                            <td>
                                <a href="{% url 'orders:order_detail' order.order.id %}" style="color: var(--primary); text-decoration: none;" title="تفاصيل الطلب الأساسي">
                                    {{ order.order.order_number|default:order.order.id }}
                                </a>
                            </td>
                            <td>
                                {% with selected_types=order.order.get_selected_types_list %}
                                    {% if selected_types %}
                                        {% for type in selected_types %}
                                            {% if type == 'installation' %}
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-tools me-1"></i> تركيب
                                                </span>
                                            {% elif type == 'tailoring' %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-cut me-1"></i> تسليم
                                                </span>
                                            {% elif type == 'accessory' %}
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-gem me-1"></i> إكسسوار
                                                </span>
                                            {% elif type == 'inspection' %}
                                                <span class="badge bg-info">
                                                    <i class="fas fa-search me-1"></i> معاينة
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ type }}</span>
                                            {% endif %}
                                            {% if not forloop.last %}<br>{% endif %}
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                {% endwith %}
                            </td>
                            <td>
                                {{ order.contract_number|default:'-' }}
                                {% if order.order.contract_number_2 %}<br><small class="text-muted">{{ order.order.contract_number_2 }}</small>{% endif %}
                                {% if order.order.contract_number_3 %}<br><small class="text-muted">{{ order.order.contract_number_3 }}</small>{% endif %}
                            </td>
                            <td>
                                {{ order.invoice_number|default:'-' }}
                                {% if order.order.invoice_number_2 %}<br><small class="text-muted">{{ order.order.invoice_number_2 }}</small>{% endif %}
                                {% if order.order.invoice_number_3 %}<br><small class="text-muted">{{ order.order.invoice_number_3 }}</small>{% endif %}
                            </td>
                            <td>
                                {% if order.order.customer %}
                                    <a href="{% url 'customers:customer_detail' order.order.customer.id %}" style="color: var(--primary); text-decoration: none; font-weight: bold;">
                                        <a href="{% url 'customers:customer_detail' order.order.customer.id %}" class="font-weight-bold text-info" title="بطاقة العميل">
                                            {{ order.order.customer.name|default:'-' }}
                                        </a>
                                    </a>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if order.order.salesperson %}
                                    {{ order.order.salesperson.name }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td data-branch-id="{{ order.order.branch.id }}">
                                {% if order.order.branch %}
                                    {{ order.order.branch.name|default:'-' }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if order.order_date %}
                                    {{ order.order_date|date:'Y-m-d' }}
                                {% elif order.order.order_date %}
                                    {{ order.order.order_date|date:'Y-m-d' }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="{% if order.is_delivery_delayed %}text-danger{% endif %}">
                                    {{ order.expected_delivery_date|date:'Y-m-d'|default:'-' }}
                                    {% if order.is_delivery_delayed %}
                                        <i class="fas fa-exclamation-triangle text-warning" title="متأخر"></i>
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                {% if order.status == 'pending_approval' %}
                                    {% get_status_badge order.status "manufacturing" %}
                                {% else %}
                                    {% if order.status == 'delivered' %}
                                        {% get_status_badge order.status "manufacturing" %}
                                    {% else %}
                                        <button class="status-btn status-{{ order.status }} unified-badge status-{{ order.status }}" 
                                                data-order-id="{{ order.id }}" 
                                                data-current-status="{{ order.status }}"
                                                onclick="showStatusModal({{ order.id }}, '{{ order.status }}', '{{ order.get_status_display }}', '{% if 'installation' in order.order.get_selected_types_list %}installation{% elif 'tailoring' in order.order.get_selected_types_list %}tailoring{% elif 'accessory' in order.order.get_selected_types_list %}accessory{% else %}manufacturing{% endif %}')">
                                            {{ order.get_status_display }}
                                        </button>
                                    {% endif %}
                                {% endif %}
                            </td>
                            <td>
                                {% if order.status == 'delivered' %}
                                    {% if order.delivery_permit_number or order.delivery_recipient_name %}
                                        <div style="font-size: 0.85em;">
                                            {% if order.delivery_permit_number %}
                                                <div class="text-success">
                                                    <i class="fas fa-file-alt me-1"></i>
                                                    <strong>إذن:</strong> {{ order.delivery_permit_number }}
                                                </div>
                                            {% endif %}
                                            {% if order.delivery_recipient_name %}
                                                <div class="text-primary">
                                                    <i class="fas fa-user me-1"></i>
                                                    <strong>المستلم:</strong> {{ order.delivery_recipient_name }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="approval-column">
                                {% if order.status == 'pending_approval' %}
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-success approve-btn" data-order-id="{{ order.id }}" title="موافقة">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger reject-btn" data-order-id="{{ order.id }}" title="رفض">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                {% elif order.status == 'rejected' %}
                                    <div class="approval-indicator">
                                        <i class="fas fa-times-circle" title="مرفوض - {{ order.get_status_display }}"></i>
                                        {% if order.rejection_reason %}
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="showRejectionDetails('{{ order.rejection_reason|escapejs }}', {{ order.id }})"
                                                    title="عرض تفاصيل الرفض{% if order.has_rejection_reply %} والرد{% endif %}">
                                                <i class="fas fa-{% if order.has_rejection_reply %}comments{% else %}info-circle{% endif %}"></i>
                                                {% if order.has_rejection_reply %}محادثة{% else %}تفاصيل{% endif %}
                                            </button>
                                        {% endif %}
                                        {% if order.has_rejection_reply %}
                                            <span class="badge reply-badge bg-info ms-1" title="تم الرد على الرفض">
                                                <i class="fas fa-reply"></i> تم الرد
                                            </span>
                                            {% if user.is_superuser or perms.manufacturing.can_approve_orders %}
                                                <button class="btn btn-sm btn-success ms-1 re-approve-btn" 
                                                        data-order-id="{{ order.id }}" 
                                                        title="الموافقة بعد المراجعة">
                                                    <i class="fas fa-check-double"></i> موافقة
                                                </button>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                {% elif order.status in 'pending,in_progress,ready_install,completed,delivered' %}
                                    <div class="approval-indicator">
                                        <i class="fas fa-check-circle" title="تمت الموافقة - {{ order.get_status_display }}"></i>
                                    </div>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm action-buttons">
                                    <a href="{% url 'manufacturing:order_detail' order.id %}" class="btn btn-info" title="عرض" style="font-size: 0.7em; padding: 0.15rem 0.3rem;">
                                        <i class="fas fa-eye" style="font-size: 0.7em;"></i>
                                    </a>
                                    {% if perms.manufacturing.change_manufacturingorder %}
                                        <a href="{% url 'manufacturing:order_update' order.id %}" class="btn btn-primary" title="تعديل" style="font-size: 0.7em; padding: 0.15rem 0.3rem;">
                                            <i class="fas fa-edit" style="font-size: 0.7em;"></i>
                                        </a>
                                    {% endif %}
                                    {% if perms.manufacturing.delete_manufacturingorder %}
                                        <a href="{% url 'manufacturing:order_delete' order.id %}" class="btn btn-danger" title="حذف" style="font-size: 0.7em; padding: 0.15rem 0.3rem;">
                                            <i class="fas fa-trash" style="font-size: 0.7em;"></i>
                                        </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Django Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="d-flex justify-content-center mt-4">
                <nav>
                    <ul class="pagination">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.branch %}&branch={{ request.GET.branch }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}{% if request.GET.page_size %}&page_size={{ request.GET.page_size }}{% endif %}">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.branch %}&branch={{ request.GET.branch }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}{% if request.GET.page_size %}&page_size={{ request.GET.page_size }}{% endif %}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.branch %}&branch={{ request.GET.branch }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}{% if request.GET.page_size %}&page_size={{ request.GET.page_size }}{% endif %}">
                                    {{ num }}
                                </a>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.branch %}&branch={{ request.GET.branch }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}{% if request.GET.page_size %}&page_size={{ request.GET.page_size }}{% endif %}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.branch %}&branch={{ request.GET.branch }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}{% if request.GET.page_size %}&page_size={{ request.GET.page_size }}{% endif %}">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-industry fa-4x mb-3" style="color: var(--neutral);"></i>
                <h4>لا توجد أوامر تصنيع</h4>
                <p class="text-muted">لم يتم العثور على أي أوامر تصنيع. يمكنك إضافة أمر جديد من خلال الزر أعلاه.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal for Status Change -->
<div class="modal fade" id="statusModal" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalLabel">تغيير حالة الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="statusForm">
                    <input type="hidden" id="statusOrderId" name="order_id">
                    <div class="mb-3">
                        <label for="new_status" class="form-label">الحالة الجديدة:</label>
                        <select class="form-select" id="new_status" name="new_status" required>
                            <!-- Will be populated by JavaScript -->
                        </select>
                    </div>
                    <div id="deliveryFields" style="display: none;">
                        <div class="mb-3">
                            <label for="delivery_permit_number" class="form-label">رقم إذن التسليم:</label>
                            <input type="text" class="form-control" id="delivery_permit_number" name="delivery_permit_number">
                        </div>
                        <div class="mb-3">
                            <label for="delivery_recipient_name" class="form-label">اسم المستلم:</label>
                            <input type="text" class="form-control" id="delivery_recipient_name" name="delivery_recipient_name">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitStatusBtn">تحديث الحالة</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Rejection -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">سبب الرفض</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="rejectForm">
                    <input type="hidden" id="rejectOrderId" name="order_id">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">سبب الرفض:</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="submitRejectBtn">رفض الطلب</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Rejection Details -->
<div class="modal fade" id="rejectionDetailsModal" tabindex="-1" aria-labelledby="rejectionDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectionDetailsModalLabel">
                    <span id="modalTitleText">تفاصيل الرفض</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="rejection-conversation">
                    <!-- سبب الرفض الأصلي -->
                    <div class="alert alert-danger">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-times-circle me-2 mt-1"></i>
                            <div>
                                <strong>سبب الرفض:</strong>
                                <div id="rejectionReasonText" class="mt-1"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الرد على الرفض (إن وجد) -->
                    <div id="replySection" class="alert alert-info" style="display: none;">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-reply me-2 mt-1"></i>
                            <div>
                                <strong>رد العميل:</strong>
                                <div id="customerReplyText" class="mt-1"></div>
                                <small class="text-muted d-block mt-1">
                                    <i class="fas fa-clock me-1"></i>
                                    <span id="replyDate"></span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إرسال رد جديد (للطلبات المرفوضة بدون رد) -->
                <div id="newReplySection" class="mt-3">
                    <label for="reply_message" class="form-label">رد على الرفض:</label>
                    <textarea class="form-control" id="reply_message" name="reply_message" rows="3" placeholder="اكتب ردك هنا..."></textarea>
                </div>
                
                <!-- أزرار الموافقة مرة أخرى (للطلبات التي تم الرد عليها) -->
                <div id="reApprovalSection" class="mt-3" style="display: none;">
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>تم استلام رد العميل. يمكنك الآن الموافقة على الطلب مرة أخرى أو رفضه نهائياً.</strong>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                
                <!-- أزرار للطلبات بدون رد -->
                <div id="noReplyButtons">
                    <button type="button" class="btn btn-primary" id="submitReplyBtn">إرسال الرد</button>
                </div>
                
                <!-- أزرار للطلبات التي تم الرد عليها -->
                <div id="hasReplyButtons" style="display: none;">
                    <button type="button" class="btn btn-success" id="reApproveBtn">
                        <i class="fas fa-check"></i> الموافقة مرة أخرى
                    </button>
                    <button type="button" class="btn btn-danger" id="finalRejectBtn">
                        <i class="fas fa-times"></i> رفض نهائي
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// التأكد من تحميل الجافاسكريبت
console.log('Manufacturing JavaScript loaded');

// دالة مساعدة لتنظيف المودال والخلفية السوداء
function forceCleanModal() {
    // إزالة جميع المودالات والخلفيات
    $('.modal').modal('hide');
    $('.modal-backdrop').remove();
    $('.modal-backdrop.show').remove();
    $('.modal-backdrop.fade').remove();
    $('.modal-backdrop.in').remove();
    
    // تنظيف الـ body
    $('body').removeClass('modal-open');
    $('html').removeClass('modal-open');
    $('body').css({
        'overflow': 'auto',
        'padding-right': '0',
        'margin-right': '0'
    });
}

$(document).ready(function() {
    // معالجة الموافقة
    $('.approve-btn').on('click', function() {
        const orderId = $(this).data('order-id');
        handleApproval(orderId, 'approve');
    });

    // معالجة الموافقة مرة أخرى بعد الرد
    $('.re-approve-btn').on('click', function() {
        const orderId = $(this).data('order-id');
        handleReApproval(orderId);
    });

    // معالجة الرفض
    $('.reject-btn').on('click', function() {
        const orderId = $(this).data('order-id');
        console.log('Reject button clicked for order:', orderId);
        
        // إزالة أي backdrop موجود قبل فتح المودال
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        
        $('#rejectOrderId').val(orderId);
        $('#rejection_reason').val(''); // Clear previous reason
        $('#rejectModal').modal('show');
    });

    // إرسال الر��ض
    $('#submitRejectBtn').on('click', function() {
        const orderId = $('#rejectOrderId').val();
        const reason = $('#rejection_reason').val();
        
        console.log('Submit reject button clicked for order:', orderId, 'reason:', reason);
        
        if (!reason.trim()) {
            Swal.fire('خطأ', 'سبب الرفض مطلوب.', 'error');
            return;
        }
        
        // إخفاء المودال فوراً قبل إرسال الطلب
        forceCleanModal();
        
        handleApproval(orderId, 'reject', reason);
    });

    // تصفية الجدول
    $('#filterForm').on('submit', function(e) {
        e.preventDefault();
        window.location.href = '?' + $(this).serialize();
    });

    // إزالة backdrop عند إغلاق المودال - حل قوي
    $('#rejectModal').on('hidden.bs.modal', function () {
        // إزالة جميع آثار المودال
        $('.modal-backdrop').remove();
        $('.modal-backdrop.show').remove();
        $('.modal-backdrop.fade').remove();
        $('body').removeClass('modal-open');
        $('html').removeClass('modal-open');
        $('body').css({
            'overflow': 'auto',
            'padding-right': '0'
        });
        
        // تنظيف النموذج
        $('#rejection_reason').val('');
        $('#rejectOrderId').val('');
    });
    
    // معالجة إضافية عند النقر على الخلفية أو الإغلاق
    $('#rejectModal').on('hide.bs.modal', function () {
        setTimeout(() => {
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            $('body').css('overflow', 'auto');
        }, 100);
    });
    
    // معالجة النقر على زر X أو خارج المودال
    $('#rejectModal .btn-close, #rejectModal [data-bs-dismiss="modal"]').on('click', function() {
        forceCleanModal();
    });
    
    // معالجة النقر على الخلفية (backdrop)
    $('#rejectModal').on('click', function(e) {
        if (e.target === this) {
            forceCleanModal();
        }
    });
    
    // تنظيف مودال تفاصيل الرفض عند إغلاقه
    $('#rejectionDetailsModal').on('hidden.bs.modal', function () {
        forceCleanModal();
        $('#reply_message').val('');
    });
    
    // معالجة النقر على زر X أو خارج مودال تفاصيل الرفض
    $('#rejectionDetailsModal .btn-close, #rejectionDetailsModal [data-bs-dismiss="modal"]').on('click', function() {
        forceCleanModal();
    });
    
    // معالجة النقر على الخلفية (backdrop) لمودال تفاصيل الرفض
    $('#rejectionDetailsModal').on('click', function(e) {
        if (e.target === this) {
            forceCleanModal();
        }
    });
});

// عرض modal لتغيير الحالة
window.showStatusModal = function(orderId, currentStatus, currentStatusDisplay, orderType) {
    console.log('showStatusModal called with:', {orderId, currentStatus, currentStatusDisplay, orderType});
    const statusOptions = {
        'pending_approval': 'قيد الموافقة',
        'pending': 'قيد الانتظار',
        'in_progress': 'قيد التنفيذ',
        'ready_install': 'جاهز للتركيب',
        'completed': 'مكتمل',
        'delivered': 'تم التسليم',
        'rejected': 'مرفوض',
        'cancelled': 'ملغي'
    };

    const statusColors = {
        'pending_approval': '#007bff',
        'pending': '#ffc107',
        'in_progress': '#17a2b8',
        'ready_install': '#6f42c1',
        'completed': '#28a745',
        'delivered': '#20c997',
        'rejected': '#dc3545',
        'cancelled': '#6c757d'
    };

    // Define status hierarchy
    const statusHierarchy = {
        'pending_approval': 0,
        'pending': 1,
        'in_progress': 2,
        'ready_install': 3,
        'completed': 4,
        'delivered': 5,
        'rejected': -1,
        'cancelled': -2
    };

    const currentLevel = statusHierarchy[currentStatus] || 0;
    const isAdmin = {{ request.user.is_superuser|yesno:"true,false" }};
    const hasChangePermission = {{ request.user|yesno:"true,false" }}; // This will be handled server-side
    
    let optionsHtml = '';
    
    // Define available statuses based on current status and order type
    let availableStatuses = [];
    
    // Use the orderType parameter passed to the function
    const orderTypeValue = orderType || null;
    
    if (currentStatus === 'pending_approval') {
        // Only admin/approval users can change from pending_approval
        if (isAdmin) {
            availableStatuses = ['pending', 'rejected', 'cancelled'];
        } else {
            availableStatuses = []; // No status changes allowed for non-admin users
        }
    } else if (currentStatus === 'pending') {
        availableStatuses = ['in_progress', 'cancelled'];
    } else if (currentStatus === 'in_progress') {
        // Apply new logic based on order type
        if (orderTypeValue === 'installation') {
            // For installation orders: only show 'ready_install' after 'in_progress'
            availableStatuses = ['ready_install'];
        } else {
            // For manufacturing/accessory orders: only show 'completed' after 'in_progress'
            availableStatuses = ['completed'];
        }
    } else if (currentStatus === 'ready_install') {
        // After 'ready_install', only show 'delivered'
        availableStatuses = ['delivered'];
    } else if (currentStatus === 'completed') {
        // After 'completed', only show 'delivered'
        availableStatuses = ['delivered'];
    } else if (currentStatus === 'delivered') {
        // منع تغيير الحالة بعد الوصول لحالة "تم التسليم"
        availableStatuses = [];
    } else if (currentStatus === 'rejected' || currentStatus === 'cancelled') {
        availableStatuses = isAdmin ? ['pending', 'in_progress', 'completed', 'ready_install', 'delivered'] : [];
    }
    
    // Admin can access all statuses (but still follow the new logic)
    if (isAdmin && currentStatus !== 'pending_approval') {
        // Add rejected and cancelled for all statuses (except themselves)
        // لكن لا نضيفهم بعد "قيد التنفيذ" حسب الشروط الجديدة
        if (currentStatus !== 'rejected' && currentStatus !== 'in_progress') availableStatuses.push('rejected');
        if (currentStatus !== 'cancelled' && currentStatus !== 'in_progress') availableStatuses.push('cancelled');
    }
    
    // Generate options HTML
    for (const [value, label] of Object.entries(statusOptions)) {
        if (value !== currentStatus && availableStatuses.includes(value)) {
            // Don't show pending_approval unless admin
            if (value !== 'pending_approval' || isAdmin) {
                optionsHtml += `<option value="${value}" style="color: ${statusColors[value]}">${label}</option>`;
            }
        }
    }

    if (optionsHtml === '') {
        Swal.fire({
            title: 'تنبيه',
            text: 'لا توجد حالات متاحة للتغيير',
            icon: 'info',
            confirmButtonText: 'موافق'
        });
        return;
    }

    Swal.fire({
        title: 'تغيير حالة الطلب',
        html: `
            <p class="mb-3">الحالة الحالية: <span class="badge" style="background-color: ${statusColors[currentStatus]}; color: white;">${currentStatusDisplay}</span></p>
            <select id="newStatus" class="form-select">
                <option value="">اختر الحالة الجديدة</option>
                ${optionsHtml}
            </select>
            ${!isAdmin ? '<p class="text-muted small mt-2">ملاحظة: لا يمكن العودة للحالات السابقة إلا من قبل مدير النظام</p>' : ''}
        `,
        showCancelButton: true,
        confirmButtonText: 'متابعة',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#007bff',
        cancelButtonColor: '#6c757d',
        preConfirm: () => {
            const newStatus = document.getElementById('newStatus').value;
            if (!newStatus) {
                Swal.showValidationMessage('يرجى اختيار الحالة الجديدة');
                return false;
            }
            return newStatus;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const newStatus = result.value;
            
            // إذا كانت الحالة الجديدة هي "تم التسليم"، اطلب حقول التسليم
            if (newStatus === 'delivered') {
                showDeliveryModal(orderId, newStatus);
            } else if (newStatus === 'rejected') {
                // إذا كانت الحالة الجديدة هي "مرفوض"، اطلب سبب الرفض
                showRejectionModal(orderId, newStatus);
            } else {
                updateOrderStatus(orderId, newStatus);
            }
        }
    });
}

// عرض modal لسبب الرفض
function showRejectionModal(orderId, newStatus) {
    Swal.fire({
        title: 'سبب الرفض',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label for="rejectionReason" class="form-label">سبب الرفض <span class="text-danger">*</span></label>
                    <textarea id="rejectionReason" class="form-control" rows="4" placeholder="أدخل سبب الرفض..." required></textarea>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'رفض الطلب',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        focusConfirm: false,
        preConfirm: () => {
            const rejectionReason = document.getElementById('rejectionReason').value.trim();
            
            if (!rejectionReason) {
                Swal.showValidationMessage('سبب الرفض مطلوب');
                return false;
            }
            
            return rejectionReason;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            updateOrderStatusWithRejection(orderId, newStatus, result.value);
        }
    });
}

// عرض modal لحقول التسليم
function showDeliveryModal(orderId, newStatus) {
    Swal.fire({
        title: 'بيانات التسليم',
        html: `
            <div class="text-start">
                <div class="mb-3">
                    <label for="deliveryPermitNumber" class="form-label">رقم إذن التسليم <span class="text-danger">*</span></label>
                    <input type="text" id="deliveryPermitNumber" class="form-control" placeholder="أدخل رقم إذن التسليم" required>
                </div>
                <div class="mb-3">
                    <label for="deliveryRecipientName" class="form-label">اسم المستلم <span class="text-danger">*</span></label>
                    <input type="text" id="deliveryRecipientName" class="form-control" placeholder="أدخل اسم المستلم" required>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'تسليم',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        focusConfirm: false,
        preConfirm: () => {
            const deliveryPermitNumber = document.getElementById('deliveryPermitNumber').value.trim();
            const deliveryRecipientName = document.getElementById('deliveryRecipientName').value.trim();
            
            if (!deliveryPermitNumber) {
                Swal.showValidationMessage('رقم إذن التسليم مطلوب');
                return false;
            }
            
            if (!deliveryRecipientName) {
                Swal.showValidationMessage('اسم المستلم مطلوب');
                return false;
            }
            
            return {
                deliveryPermitNumber: deliveryPermitNumber,
                deliveryRecipientName: deliveryRecipientName
            };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            updateOrderStatusWithDelivery(orderId, newStatus, result.value);
        }
    });
}

// تحديث حالة الطلب
function updateOrderStatus(orderId, newStatus) {
    Swal.fire({
        title: 'جاري التحديث...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: `/manufacturing/api/update_status/${orderId}/`,
        method: 'POST',
        data: {
            'status': newStatus,
            'csrfmiddlewaretoken': $('input[name=csrfmiddlewaretoken]').val() || $('meta[name=csrf-token]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'تم بنجاح!',
                    text: 'تم تحديث حالة الطلب بنجاح',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire('خطأ!', response.error || 'حدث خطأ أثناء تحديث الحالة', 'error');
            }
        },
        error: function(xhr) {
            let errorMessage = 'حدث خطأ غير متوقع';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            Swal.fire('خطأ!', errorMessage, 'error');
        }
    });
}

// تحديث حالة الطلب مع سبب الرفض
function updateOrderStatusWithRejection(orderId, newStatus, rejectionReason) {
    Swal.fire({
        title: 'جاري الرفض...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // استخدام API الموافقة/الرفض الموجود
    const csrfToken = $('input[name=csrfmiddlewaretoken]').val() || $('meta[name=csrf-token]').attr('content');
    
    $.ajax({
        url: `/manufacturing/approval/${orderId}/`,
        method: 'POST',
        data: JSON.stringify({
            'action': 'reject',
            'reason': rejectionReason
        }),
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'تم الرفض بنجاح!',
                    text: 'تم رفض الطلب وإرسال إشعار للمستخدم',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire('خطأ!', response.error || 'حدث خطأ أثناء رفض الطلب', 'error');
            }
        },
        error: function(xhr) {
            let errorMessage = 'حدث خطأ غير متوقع';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            Swal.fire('خطأ!', errorMessage, 'error');
        }
    });
}

// تحديث حالة الطلب مع بيانات التسليم
function updateOrderStatusWithDelivery(orderId, newStatus, deliveryData) {
    Swal.fire({
        title: 'جاري التحديث...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    const formData = new FormData();
    formData.append('status', newStatus);
    formData.append('delivery_permit_number', deliveryData.deliveryPermitNumber);
    formData.append('delivery_recipient_name', deliveryData.deliveryRecipientName);
    formData.append('csrfmiddlewaretoken', $('input[name=csrfmiddlewaretoken]').val() || $('meta[name=csrf-token]').attr('content'));

    $.ajax({
        url: `/manufacturing/api/update_status/${orderId}/`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'تم التسليم بنجاح!',
                    text: `تم تسليم الطلب إلى: ${deliveryData.deliveryRecipientName}`,
                    icon: 'success',
                    timer: 3000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire('خطأ!', response.error || 'حدث خطأ أثناء تحديث الحالة', 'error');
            }
        },
        error: function(xhr) {
            let errorMessage = 'حدث خطأ غير متوقع';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            Swal.fire('خطأ!', errorMessage, 'error');
        }
    });
}

// معالجة الموافقة والرفض
function handleApproval(orderId, action, reason = '') {
    // إظهار loading
    Swal.fire({
        title: action === 'approve' ? 'جاري الموافقة...' : 'جاري الرفض...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // إخفاء المودال فوراً إذا كان الإجراء رفض
    if (action === 'reject') {
        forceCleanModal();
    }

    // الحصول على CSRF token
    const csrfToken = $('input[name=csrfmiddlewaretoken]').val() || $('meta[name=csrf-token]').attr('content');
    
    if (!csrfToken) {
        Swal.fire('خطأ!', 'لم يتم العثور على رمز الأمان. يرجى إعادة تحميل الصفحة.', 'error');
        return;
    }

    $.ajax({
        url: `/manufacturing/approval/${orderId}/`,
        method: 'POST',
        data: JSON.stringify({
            'action': action,
            'reason': reason
        }),
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        success: function(response) {
            if (response.success) {
                if (action === 'approve') {
                    Swal.fire({
                        title: 'تم بنجاح!',
                        text: response.message || 'تم قبول الطلب بنجاح',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire({
                        title: 'تم بنجاح!',
                        text: response.message || 'تم رفض الطلب وإرسال إشعار للمستخدم',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }
                setTimeout(() => location.reload(), 2000);
            } else {
                Swal.fire('خطأ!', response.error || 'حدث خطأ أثناء المعالجة', 'error');
            }
        },
        error: function(xhr) {
            let errorMessage = 'حدث خطأ غير متوقع';
            
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            } else if (xhr.status === 403) {
                errorMessage = 'ليس لديك صلاحية لتنفيذ هذا الإجراء';
            } else if (xhr.status === 404) {
                errorMessage = 'لم يتم العثور على الطلب';
            } else if (xhr.status === 400) {
                errorMessage = 'بيانات غير صالحة';
            }
            
            // إزالة قوية للمودال والـ backdrop في حالة الخطأ
            forceCleanModal();
            
            Swal.fire('خطأ!', errorMessage, 'error');
        }
    });
}

// عرض تفاصيل الرفض
function showRejectionDetails(rejectionReason, orderId) {
    $('#rejectionReasonText').text(rejectionReason);
    
    // حفظ معرف الطلب للاستخدام في الرد
    $('#rejectionDetailsModal').data('order-id', orderId);
    
    // جلب معلومات الطلب لعرض الرد إن وجد
    $.ajax({
        url: `/manufacturing/order/${orderId}/details/`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const order = response.order;
                
                // إذا كان هناك رد على الرفض
                if (order.has_rejection_reply && order.rejection_reply) {
                    $('#modalTitleText').text('محادثة الرفض والرد');
                    $('#customerReplyText').text(order.rejection_reply);
                    
                    // تنسيق التاريخ
                    if (order.rejection_reply_date) {
                        const replyDate = new Date(order.rejection_reply_date);
                        $('#replyDate').text(replyDate.toLocaleDateString('ar-SA') + ' ' + replyDate.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'}));
                    }
                    
                    $('#replySection').show();
                    $('#reApprovalSection').show();
                    $('#newReplySection').hide();
                    $('#noReplyButtons').hide();
                    $('#hasReplyButtons').show();
                } else {
                    // لا يوجد رد بعد
                    $('#modalTitleText').text('تفاصيل الرفض');
                    $('#replySection').hide();
                    $('#reApprovalSection').hide();
                    $('#newReplySection').show();
                    $('#noReplyButtons').show();
                    $('#hasReplyButtons').hide();
                }
            }
        },
        error: function() {
            // في حالة فشل جلب التفاصيل، اعرض النموذج العادي
            $('#modalTitleText').text('تفاصيل الرفض');
            $('#replySection').hide();
            $('#reApprovalSection').hide();
            $('#newReplySection').show();
            $('#noReplyButtons').show();
            $('#hasReplyButtons').hide();
        }
    });
    
    $('#rejectionDetailsModal').modal('show');
}

// معالجة الموافقة مرة أخرى بعد الرد على الرفض
function handleReApproval(orderId) {
    Swal.fire({
        title: 'تأكيد الموافقة',
        text: 'هل أنت متأكد من الموافقة على هذا الطلب بعد مراجعة الرد؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، وافق',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d'
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار loading
            Swal.fire({
                title: 'جاري الموافقة...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            const csrfToken = $('input[name=csrfmiddlewaretoken]').val() || $('meta[name=csrf-token]').attr('content');
            
            $.ajax({
                url: `/manufacturing/re_approve/${orderId}/`,
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'تم بنجاح!',
                            text: response.message || 'تمت الموافقة على الطلب بعد المراجعة',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        Swal.fire('خطأ!', response.error || 'حدث خطأ أثناء الموافقة', 'error');
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'حدث خطأ غير متوقع';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    Swal.fire('خطأ!', errorMessage, 'error');
                }
            });
        }
    });
}

// معالجة الرد على الرفض
$('#submitReplyBtn').on('click', function() {
    const orderId = $('#rejectionDetailsModal').data('order-id');
    const replyMessage = $('#reply_message').val().trim();
    
    if (!replyMessage) {
        Swal.fire('خطأ', 'يرجى كتابة رد قبل الإرسال.', 'error');
        return;
    }
    
    // إخفاء المودال فوراً قبل إرسال الطلب
    $('#rejectionDetailsModal').modal('hide');
    // تنظيف قوي للمودال وإزالة backdrop
    setTimeout(() => {
        forceCleanModal();
    }, 100);
    
    // إظهار loading
    Swal.fire({
        title: 'جاري إرسال الرد...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    const csrfToken = $('input[name=csrfmiddlewaretoken]').val() || $('meta[name=csrf-token]').attr('content');
    
    $.ajax({
        url: `/manufacturing/send_reply/${orderId}/`,
        method: 'POST',
        data: JSON.stringify({
            'reply_message': replyMessage
        }),
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'تم بنجاح!',
                    text: 'تم إرسال ردك للإدارة بنجاح',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
                $('#reply_message').val('');
                // إعادة تحميل الصفحة لإظهار التحديثات
                setTimeout(() => location.reload(), 2000);
            } else {
                Swal.fire('خطأ!', response.error || 'حدث خطأ أثناء إرسال الرد', 'error');
            }
        },
        error: function(xhr) {
            let errorMessage = 'حدث خطأ غير متوقع';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            Swal.fire('خطأ!', errorMessage, 'error');
        }
    });
});

// معالجة الموافقة مرة أخرى من المودال
$('#reApproveBtn').on('click', function() {
    const orderId = $('#rejectionDetailsModal').data('order-id');
    
    // إخفاء المودال
    $('#rejectionDetailsModal').modal('hide');
    setTimeout(() => {
        forceCleanModal();
    }, 100);
    
    // استدعاء دالة الموافقة مرة أخرى
    handleReApproval(orderId);
});

// معالجة الرفض النهائي من المودال
$('#finalRejectBtn').on('click', function() {
    const orderId = $('#rejectionDetailsModal').data('order-id');
    
    // إخفاء المودال
    $('#rejectionDetailsModal').modal('hide');
    setTimeout(() => {
        forceCleanModal();
    }, 100);
    
    // طلب سبب الرفض النهائي
    Swal.fire({
        title: 'رفض نهائي',
        text: 'يرجى إدخال سبب الرفض النهائي:',
        input: 'textarea',
        inputPlaceholder: 'اكتب سبب الرفض النهائي...',
        showCancelButton: true,
        confirmButtonText: 'رفض نهائي',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        inputValidator: (value) => {
            if (!value.trim()) {
                return 'يرجى إدخال سبب الرفض';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار loading
            Swal.fire({
                title: 'جاري الرفض...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            const csrfToken = $('input[name=csrfmiddlewaretoken]').val() || $('meta[name=csrf-token]').attr('content');
            
            $.ajax({
                url: `/manufacturing/approval/${orderId}/`,
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    action: 'reject',
                    reason: result.value
                }),
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم الرفض',
                            text: response.message,
                            confirmButtonText: 'موافق'
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ', response.error, 'error');
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'حدث خطأ غير متوقع';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    Swal.fire('خطأ', errorMessage, 'error');
                }
            });
        }
    });
});

// تأكيد الحذف
function confirmDelete(orderId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'لن تتمكن من التراجع عن هذا الإجراء!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = `/manufacturing/orders/${orderId}/delete/`;
        }
    });
}
</script>
{% endblock %}
