{% extends 'base.html' %}
{% load static %}

{% block title %}تأكيد حذف أمر التصنيع{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/enhanced-forms.css' %}">
<style>
    .delete-warning {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin: 20px 0;
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        text-align: center;
    }
    
    .delete-warning .icon {
        font-size: 4rem;
        margin-bottom: 20px;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .order-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .order-info h5 {
        color: #495057;
        margin-bottom: 15px;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #6c757d;
    }
    
    .info-value {
        color: #212529;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border: none;
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-danger:hover {
        background: linear-gradient(135deg, #c82333, #bd2130);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
    }
    
    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-pending { background: #fff3cd; color: #856404; }
    .status-approved { background: #d4edda; color: #155724; }
    .status-in-progress { background: #cce5ff; color: #004085; }
    .status-completed { background: #d1ecf1; color: #0c5460; }
    .status-rejected { background: #f8d7da; color: #721c24; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-trash-alt text-danger me-2"></i>
                        تأكيد حذف أمر التصنيع
                    </h2>
                    <p class="text-muted mb-0">تأكيد حذف أمر التصنيع نهائياً من النظام</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'manufacturing:dashboard' %}">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{% url 'manufacturing:order_list' %}">أوامر التصنيع</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{% url 'manufacturing:order_detail' object.pk %}">
                                أمر #{{ object.pk }}
                            </a>
                        </li>
                        <li class="breadcrumb-item active">حذف</li>
                    </ol>
                </nav>
            </div>
            
            <!-- Delete Warning -->
            <div class="delete-warning">
                <div class="icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3>تحذير: عملية حذف نهائية!</h3>
                <p class="mb-0">
                    هل أنت متأكد من رغبتك في حذف أمر التصنيع هذا؟ 
                    <br>
                    <strong>لا يمكن التراجع عن هذه العملية!</strong>
                </p>
            </div>
            
            <!-- Order Information -->
            <div class="order-info">
                <h5>
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات أمر التصنيع المراد حذفه
                </h5>
                
                <div class="info-row">
                    <span class="info-label">رقم أمر التصنيع:</span>
                    <span class="info-value">#{{ object.pk }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">رقم الطلب الأصلي:</span>
                    <span class="info-value">
                        {% if object.order %}
                            {{ object.order.order_number|default:object.order.pk }}
                        {% else %}
                            غير محدد
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">العميل:</span>
                    <span class="info-value">
                        {% if object.order and object.order.customer %}
                            {{ object.order.customer.name }}
                        {% else %}
                            غير محدد
                        {% endif %}
                    </span>
                </div>
                

                
                <div class="info-row">
                    <span class="info-label">حالة أمر التصنيع:</span>
                    <span class="info-value">
                        {% if object.status == 'pending_approval' %}
                            <span class="badge bg-warning text-dark">قيد الموافقة</span>
                        {% elif object.status == 'pending' %}
                            <span class="badge bg-info text-white">قيد الانتظار</span>
                        {% elif object.status == 'in_progress' %}
                            <span class="badge bg-primary text-white">قيد التصنيع</span>
                        {% elif object.status == 'ready_install' %}
                            <span class="badge bg-success text-white">جاهز للتركيب</span>
                        {% elif object.status == 'completed' %}
                            <span class="badge bg-success text-white">مكتمل</span>
                        {% elif object.status == 'delivered' %}
                            <span class="badge bg-dark text-white">تم التسليم</span>
                        {% elif object.status == 'rejected' %}
                            <span class="badge bg-danger text-white">مرفوض</span>
                        {% elif object.status == 'cancelled' %}
                            <span class="badge bg-secondary text-white">ملغي</span>
                        {% else %}
                            <span class="badge bg-secondary text-white">{{ object.get_status_display }}</span>
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">رقم العقد:</span>
                    <span class="info-value">{{ object.contract_number|default:'غير محدد' }}</span>
                </div>
                
                {% if object.order.contract_number_2 %}
                <div class="info-row">
                    <span class="info-label">رقم العقد الإضافي 2:</span>
                    <span class="info-value">{{ object.order.contract_number_2 }}</span>
                </div>
                {% endif %}
                
                {% if object.order.contract_number_3 %}
                <div class="info-row">
                    <span class="info-label">رقم العقد الإضافي 3:</span>
                    <span class="info-value">{{ object.order.contract_number_3 }}</span>
                </div>
                {% endif %}
                
                <div class="info-row">
                    <span class="info-label">تاريخ الإنشاء:</span>
                    <span class="info-value">{{ object.created_at|date:"Y-m-d H:i" }}</span>
                </div>
                
                {% if object.expected_delivery_date %}
                <div class="info-row">
                    <span class="info-label">تاريخ التسليم المتوقع:</span>
                    <span class="info-value">{{ object.expected_delivery_date|date:"Y-m-d" }}</span>
                </div>
                {% endif %}
                
                {% if object.notes %}
                <div class="info-row">
                    <span class="info-label">الملاحظات:</span>
                    <span class="info-value">{{ object.notes|truncatewords:10 }}</span>
                </div>
                {% endif %}
            </div>
            
            <!-- Confirmation Form -->
            <div class="card">
                <div class="card-body">
                    <form method="post" class="d-flex justify-content-center gap-3">
                        {% csrf_token %}
                        
                        <button type="submit" class="btn btn-danger btn-lg">
                            <i class="fas fa-trash-alt me-2"></i>
                            نعم، احذف أمر التصنيع
                        </button>
                        
                        <a href="{% url 'manufacturing:order_detail' object.pk %}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-arrow-right me-2"></i>
                            إلغاء والعودة
                        </a>
                    </form>
                </div>
            </div>
            
            <!-- Additional Warning -->
            <div class="alert alert-warning mt-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> 
                حذف أمر التصنيع سيؤثر على:
                <ul class="mb-0 mt-2">
                    <li>جميع عناصر أمر التصنيع المرتبطة</li>
                    <li>حالة الطلب الأصلي (ستعود إلى الحالة السابقة)</li>
                    <li>التقارير والإحصائيات المرتبطة</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأكيد إضافي قبل الحذف
    const deleteForm = document.querySelector('form[method="post"]');
    if (deleteForm) {
        deleteForm.addEventListener('submit', function(e) {
            const confirmed = confirm('هل أنت متأكد من رغبتك في حذف أمر التصنيع؟\n\nهذه العملية لا يمكن التراجع عنها!');
            if (!confirmed) {
                e.preventDefault();
            }
        });
    }
    
    // تأثير بصري للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% endblock %} 