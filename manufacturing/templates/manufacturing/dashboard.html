{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}لوحة تحكم المصنع{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'manufacturing/css/dashboard.css' %}">
<style>
/* تحسينات لوحة التحكم */
.dashboard-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.stat-card {
    background: linear-gradient(135deg, var(--primary) 0%, #8b735a 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

.stat-card .icon {
    font-size: 2.5rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.stat-card .number {
    font-size: 2rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.stat-card .label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.recent-orders-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 500px;
    overflow-y: auto;
}

.order-item {
    border-bottom: 1px solid #eee;
    padding: 1rem 0;
    transition: background-color 0.3s ease;
}

.order-item:hover {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin: 0 -1rem;
}

.order-item:last-child {
    border-bottom: none;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.metric-card {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.metric-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary);
}

.metric-label {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

.change-indicator {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.change-positive {
    color: #28a745;
}

.change-negative {
    color: #dc3545;
}

.alert-card {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.info-card {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* تحسينات responsive */
@media (max-width: 768px) {
    .stat-card {
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .chart-container {
        padding: 1rem;
    }
    
    .recent-orders-card {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0" style="color: var(--primary); font-weight: bold;">
            <i class="fas fa-industry me-2"></i>لوحة تحكم المصنع
        </h1>
        <div class="btn-group">
            <a href="{% url 'manufacturing:order_list' %}" class="btn btn-primary">
                <i class="fas fa-list me-2"></i>عرض كل الطلبات
            </a>
            <a href="{% url 'manufacturing:order_create' %}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>طلب جديد
            </a>
        </div>
    </div>

    <!-- Alert Cards -->
    <div class="row mb-4">
        {% if pending_approval_count > 0 %}
        <div class="col-md-6">
            <div class="alert-card">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-1">{{ pending_approval_count }} طلب يحتاج موافقة</h5>
                        <small>يرجى مراجعة الطلبات المعلقة</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        {% if overdue_orders > 0 %}
        <div class="col-md-6">
            <div class="alert-card">
                <div class="d-flex align-items-center">
                    <i class="fas fa-clock fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-1">{{ overdue_orders }} طلب متأخر</h5>
                        <small>تجاوز موعد التسليم المتوقع</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Main Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stat-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="number">{{ total_orders|intcomma }}</div>
                        <div class="label">إجمالي الطلبات</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="number">{{ delivered_orders|intcomma }}</div>
                        <div class="label">تم التسليم</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="icon">
                            <i class="fas fa-cog fa-spin"></i>
                        </div>
                        <div class="number">{{ in_progress_orders|intcomma }}</div>
                        <div class="label">قيد التنفيذ</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="icon">
                            <i class="fas fa-hourglass-half"></i>
                        </div>
                        <div class="number">{{ pending_orders|intcomma }}</div>
                        <div class="label">قيد الانتظار</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Metrics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-number">{{ this_month_orders }}</div>
                <div class="metric-label">طلبات هذا الشهر</div>
                <div class="change-indicator {% if month_change_percent >= 0 %}change-positive{% else %}change-negative{% endif %}">
                    <i class="fas fa-arrow-{% if month_change_percent >= 0 %}up{% else %}down{% endif %}"></i>
                    {{ month_change_percent|floatformat:1 }}% عن الشهر الماضي
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-number">{{ completed_orders }}</div>
                <div class="metric-label">طلبات مكتملة</div>
                <div class="change-indicator" style="color: #28a745;">
                    <i class="fas fa-check"></i>
                    جاهزة للتسليم
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-number">{{ ready_install_orders }}</div>
                <div class="metric-label">جاهز للتركيب</div>
                <div class="change-indicator" style="color: #6f42c1;">
                    <i class="fas fa-tools"></i>
                    يحتاج تركيب
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-number">{{ avg_completion_days }}</div>
                <div class="metric-label">متوسط أيام الإنجاز</div>
                <div class="change-indicator" style="color: #17a2b8;">
                    <i class="fas fa-calendar-alt"></i>
                    يوم
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Status Distribution Chart -->
        <div class="col-xl-4 col-lg-5">
            <div class="chart-container">
                <h5 class="mb-3" style="color: var(--primary); font-weight: bold;">
                    <i class="fas fa-chart-pie me-2"></i>توزيع الطلبات حسب الحالة
                </h5>
                <div class="chart-pie">
                    <canvas id="statusPieChart" style="max-height: 300px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Monthly Trend Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="chart-container">
                <h5 class="mb-3" style="color: var(--primary); font-weight: bold;">
                    <i class="fas fa-chart-line me-2"></i>اتجاه الطلبات الشهرية ({{ chart_period }})
                </h5>
                <div class="chart-area">
                    <canvas id="monthlyOrdersChart" style="max-height: 300px;"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Orders and Order Types -->
    <div class="row">
        <!-- Recent Orders -->
        <div class="col-lg-8">
            <div class="recent-orders-card">
                <h5 class="mb-3" style="color: var(--primary); font-weight: bold;">
                    <i class="fas fa-history me-2"></i>أحدث الطلبات
                </h5>
                {% if recent_orders %}
                    {% for order in recent_orders %}
                    <div class="order-item">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <strong>طلب #{{ order.id }}</strong>
                                <br>
                                <small class="text-muted">{{ order.created_at|date:"Y-m-d H:i" }}</small>
                            </div>
                            <div class="col-md-3">
                                {% if order.order.customer %}
                                    {{ order.order.customer.name }}
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </div>
                            <div class="col-md-3">
                                <span class="status-badge status-{{ order.status }}">
                                    {{ order.get_status_display }}
                                </span>
                            </div>
                            <div class="col-md-3 text-end">
                                <a href="{% url 'manufacturing:order_detail' order.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد طلبات حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Order Types Summary -->
        <div class="col-lg-4">
            <div class="recent-orders-card">
                <h5 class="mb-3" style="color: var(--primary); font-weight: bold;">
                    <i class="fas fa-tags me-2"></i>أنواع الطلبات
                </h5>
                {% if orders_by_type %}
                    {% for type_data in orders_by_type %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <strong>
                                {% if type_data.order_type == 'installation' %}
                                    <i class="fas fa-tools me-2"></i>تركيب
                                {% elif type_data.order_type == 'custom' %}
                                    <i class="fas fa-cut me-2"></i>تفصيل
                                {% elif type_data.order_type == 'accessory' %}
                                    <i class="fas fa-puzzle-piece me-2"></i>اكسسوار
                                {% else %}
                                    <i class="fas fa-cog me-2"></i>{{ type_data.order_type }}
                                {% endif %}
                            </strong>
                        </div>
                        <span class="badge bg-primary">{{ type_data.count }}</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد بيانات</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener("DOMContentLoaded", function() {
    // Status Pie Chart
    const statusData = {{ status_data|safe }};
    if (statusData && statusData.labels.length > 0) {
        const ctxPie = document.getElementById("statusPieChart");
        new Chart(ctxPie, {
            type: 'doughnut',
            data: {
                labels: statusData.labels,
                datasets: [{
                    data: statusData.data,
                    backgroundColor: statusData.colors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });
    }

    // Monthly Orders Line Chart
    const monthlyData = {{ monthly_data|safe }};
    if (monthlyData && monthlyData.labels.length > 0) {
        const ctxLine = document.getElementById("monthlyOrdersChart");
        new Chart(ctxLine, {
            type: 'line',
            data: {
                labels: monthlyData.labels,
                datasets: [{
                    label: "عدد الطلبات",
                    data: monthlyData.data,
                    borderColor: 'rgba(139, 115, 90, 1)',
                    backgroundColor: 'rgba(139, 115, 90, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(139, 115, 90, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }
});
</script>
{% endblock %}