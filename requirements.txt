# ======================================
# إطار العمل الأساسي والإضافات
# ======================================
Django==4.2.21
django-environ==0.12.0
django-debug-toolbar==4.2.0
django-extensions==3.2.3
django-apscheduler==0.7.0
django-dbbackup==4.2.1
python-dotenv==1.0.1
gunicorn==21.2.0
whitenoise==6.5.0
asgiref==3.8.1

# ======================================
# REST Framework & API
# ======================================
djangorestframework==3.14.0
djangorestframework-simplejwt==5.2.2
django-cors-headers==4.3.1
coreapi==2.3.3
uritemplate==4.2.0

# ======================================
# Forms & UI
# ======================================
django-widget-tweaks==1.5.0
django-crispy-forms==2.4
django-filter==25.1
django-js-asset==3.1.2
django-mptt==0.17.0

# ======================================
# Authentication & Users
# ======================================
django-allauth==65.9.0
PyJWT==2.9.0
oauthlib==3.2.2

# ======================================
# Database & Models
# ======================================
psycopg2-binary==2.9.10
dj-database-url==3.0.0
django-model-utils==5.0.0
sqlparse==0.5.3

# ======================================
# File Handling & Media
# ======================================
Pillow==11.2.1
python-magic==0.4.27
reportlab==4.4.1
WeasyPrint==65.1

# ======================================
# Import/Export Support
# ======================================
django-import-export==3.3.1
openpyxl==3.1.5
xlrd==2.0.1
xlwt==1.3.0
odfpy==1.4.1
tablib==3.5.0

# ======================================
# Task Processing & Caching
# ======================================
celery==5.5.3
redis==6.2.0
APScheduler==3.11.0
django-celery-beat==2.8.1
django-celery-results==2.6.0
amqp==5.3.1
billiard==4.2.1
kombu==5.5.4
vine==5.1.0

# ======================================
# Security
# ======================================
cryptography==41.0.1
defusedxml==0.7.1
python-decouple==3.8
django-csp==3.7

# ======================================
# Google Integration
# ======================================
google-auth==2.39.0
google-auth-oauthlib==1.2.2
google-api-core==2.25.0
google-api-python-client==2.156.0
googleapis-common-protos==1.70.0

# ======================================
# Data Processing & Visualization
# ======================================
numpy==2.2.6
pandas==2.2.3
scikit-learn==1.6.1
matplotlib==3.10.3
seaborn==0.13.2
scipy==1.15.3

# ======================================
# التطوير والاختبار
# ======================================
pytest==7.4.3
pytest-django==4.5.2
pytest-cov==4.1.0
factory-boy==3.3.0
ipython==8.25.0

# ======================================
# System Monitoring & Logging
# ======================================
psutil==7.0.0
python-json-logger==2.0.7

# ======================================
# Dependencies & Utilities
# ======================================
python-dateutil==2.9.0.post0
diff-match-patch==20241021
text-unidecode==1.3
setuptools==80.0.0
six==1.17.0
Markdown==3.8
python-slugify==8.0.4
requests==2.32.3
requests-oauthlib==2.0.0
urllib3==2.4.0
httplib2==0.22.0
certifi==2025.4.26
chardet==5.2.0
idna==3.10
click==8.2.1
click-didyoumean==0.3.0
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
Jinja2==3.1.6
MarkupPy==1.18
MarkupSafe==3.0.2
Pygments==2.19.1
wcwidth==0.2.13
cachetools==5.5.2
cffi==1.17.1
charset-normalizer==3.4.2
contourpy==1.3.2
et_xmlfile==2.0.0
fonttools==4.58.1
itypes==1.2.0
joblib==1.5.1
kiwisolver==1.4.8
packaging==25.0
prompt_toolkit==3.0.51
proto-plus==1.26.1
protobuf==6.31.1
pyasn1==0.6.1
pyasn1_modules==0.4.0
pycparser==2.22
pyparsing==3.2.3
rsa==4.9.1
threadpoolctl==3.6.0
typing_extensions==4.14.0
tzdata==2025.2
tzlocal==5.3.1
coreschema==0.0.4
cycler==0.12.1
iniconfig==2.1.0
pluggy==1.6.0
pytz==2025.2
PyYAML==6.0.2
