#!/bin/bash
# إعطاء صلاحيات التنفيذ لجميع الملفات التشغيلية
# Fix permissions for all executable files

echo "========================================"
echo "   إصلاح صلاحيات الملفات التشغيلية"
echo "   Fixing Executable File Permissions"
echo "========================================"

# إعطاء صلاحيات التنفيذ لجميع الملفات في مجلد لينكس
echo "[INFO] إعطاء صلاحيات التنفيذ للملفات..."

chmod +x *.sh
chmod +x ../cloudflared

echo "✅ تم إعطاء صلاحيات التنفيذ للملفات التالية:"
echo "----------------------------------------"
ls -la *.sh | awk '{print "  " $1 " " $9}'
echo "  " $(ls -la ../cloudflared | awk '{print $1 " cloudflared"}')

echo "========================================"
echo "✅ تم إصلاح جميع الصلاحيات بنجاح!"
echo "========================================" 