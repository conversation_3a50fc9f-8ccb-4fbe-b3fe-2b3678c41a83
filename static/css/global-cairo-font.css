/* تطبيق خط Cairo على كامل المشروع */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* تطبيق الخط العربي على العناصر الأساسية - استثناء الأيقونات */
body, 
div, 
span, 
p, 
h1, h2, h3, h4, h5, h6,
a, 
button, 
input, 
textarea, 
select, 
label,
table, 
th, 
td,
ul, 
ol, 
li {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على العناصر الأساسية */
html, body {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 400;
}

/* تطبيق الخط على البطاقات */
.card,
.card-header,
.card-body,
.card-footer,
.card-title,
.card-text {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على الجداول */
.table,
.table thead th,
.table tbody td,
.table-responsive {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على النماذج */
.form-control,
.form-select,
.form-label,
.form-text,
.form-check,
.form-check-input,
.form-check-label {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على الأزرار */
.btn,
.btn-sm,
.btn-lg,
.btn-group,
.btn-group-sm,
.btn-group-lg {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على البادجات */
.badge,
.badge-primary,
.badge-secondary,
.badge-success,
.badge-danger,
.badge-warning,
.badge-info {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على التنبيهات */
.alert,
.alert-heading,
.alert-primary,
.alert-secondary,
.alert-success,
.alert-danger,
.alert-warning,
.alert-info {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على القوائم المنسدلة */
.dropdown-menu,
.dropdown-item,
.dropdown-header {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على النافذة المنبثقة */
.modal,
.modal-header,
.modal-body,
.modal-footer,
.modal-title {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على شريط التنقل */
.navbar,
.navbar-brand,
.navbar-nav,
.nav-link,
.nav-item,
.navbar-toggler {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على الشريط الجانبي */
.sidebar,
.sidebar-nav,
.sidebar-item {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على العناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600;
}

/* تطبيق الخط على النصوص */
p, span, div {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على الروابط */
a {
    font-family: 'Cairo', sans-serif !important;
}

/* الحفاظ على خط Font Awesome للأيقونات */
.fas, .far, .fab, .fa {
    font-family: 'Font Awesome 5 Free' !important;
}

/* تطبيق الخط على النصوص العربية */
[dir="rtl"] *:not(.fas):not(.far):not(.fab):not(.fa) {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على العناصر المخصصة */
.custom-card,
.custom-button,
.custom-text,
.custom-title {
    font-family: 'Cairo', sans-serif !important;
}

/* تطبيق الخط على جميع الأقسام - استثناء الأيقونات */
.customers *:not(.fas):not(.far):not(.fab):not(.fa),
.orders *:not(.fas):not(.far):not(.fab):not(.fa),
.inventory *:not(.fas):not(.far):not(.fab):not(.fa),
.inspections *:not(.fas):not(.far):not(.fab):not(.fa),
.installations *:not(.fas):not(.far):not(.fab):not(.fa),
.manufacturing *:not(.fas):not(.far):not(.fab):not(.fa),
.reports *:not(.fas):not(.far):not(.fab):not(.fa),
.database *:not(.fas):not(.far):not(.fab):not(.fa) {
    font-family: 'Cairo', sans-serif !important;
}

/* تحسين مظهر النصوص العربية */
.text-right {
    text-align: right !important;
}

.text-center {
    text-align: center !important;
}

/* تحسين مظهر الأيقونات مع النصوص العربية */
.fas, .far, .fab {
    margin-left: 5px;
    margin-right: 5px;
}

/* تطبيق الخط على جميع العناصر المتبقية - استثناء الأيقونات */
*:not(.fas):not(.far):not(.fab):not(.fa) {
    font-family: 'Cairo', sans-serif !important;
} 