/**
 * Modern Black Theme - ثيم أسود حديث
 * تصميم بسيط وأنيق مع أيقونات ملونة جميلة
 * 
 * ملاحظة: تم توحيد جميع متغيرات الثيم في ملف style.css
 * هذا الملف يحتوي فقط على تنسيقات خاصة بمكونات معينة للثيم الأسود العصري
 */

/* ===== الخلفية الأساسية ===== */
[data-theme="modern-black"] {
    background: var(--background) !important;
    color: var(--text-primary) !important;
    font-family: 'Cairo', sans-serif !important;
}

[data-theme="modern-black"] body {
    background: var(--background) !important;
    color: var(--text-primary) !important;
    line-height: 1.6 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* ===== شريط التنقل ===== */
[data-theme="modern-black"] .navbar {
    background: rgba(26, 26, 26, 0.95) !important;
    backdrop-filter: var(--blur) !important;
    -webkit-backdrop-filter: var(--blur) !important;
    border-bottom: 1px solid var(--border) !important;
    box-shadow: var(--shadow-light) !important;
}

[data-theme="modern-black"] .navbar-brand {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
}

[data-theme="modern-black"] .navbar-brand:hover {
    color: var(--primary) !important;
}

[data-theme="modern-black"] .logo-img {
    filter: brightness(1.2) !important;
}

/* ===== روابط التنقل مع الأيقونات الملونة ===== */
[data-theme="modern-black"] .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
    padding: 8px 15px !important;
    border-radius: var(--radius) !important;
    transition: var(--transition) !important;
    margin: 0 2px !important;
}

[data-theme="modern-black"] .nav-link:hover {
    color: var(--text-primary) !important;
    background: rgba(255, 255, 255, 0.05) !important;
    transform: translateY(-1px) !important;
}

/* أيقونات ملونة حديثة للأقسام */
[data-theme="modern-black"] .nav-link[href*="home"] i,
[data-theme="modern-black"] .nav-link[href="/"] i {
    color: #00FF88 !important; /* أخضر الرئيسية */
}

[data-theme="modern-black"] .nav-link[href*="customers"] i {
    color: #00D2FF !important; /* أزرق العملاء */
}

[data-theme="modern-black"] .nav-link[href*="orders"] i {
    color: #FF6B00 !important; /* برتقالي الطلبات */
}

[data-theme="modern-black"] .nav-link[href*="inventory"] i {
    color: #B537F2 !important; /* بنفسجي المخزون */
}

[data-theme="modern-black"] .nav-link[href*="inspections"] i {
    color: #00F5D4 !important; /* تركوازي المعاينات */
}

[data-theme="modern-black"] .nav-link[href*="installations"] i {
    color: #FFD700 !important; /* ذهبي التركيبات */
}

[data-theme="modern-black"] .nav-link[href*="factory"] i {
    color: #FF3366 !important; /* وردي المصنع */
}

[data-theme="modern-black"] .nav-link[href*="reports"] i {
    color: #3A86FF !important; /* سماوي التقارير */
}

[data-theme="modern-black"] .nav-link[href*="database"] i,
[data-theme="modern-black"] .nav-link[href*="data"] i {
    color: #9C27B0 !important; /* بنفسجي البيانات */
}

/* ===== البطاقات ===== */
[data-theme="modern-black"] .card {
    background: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius) !important;
    box-shadow: var(--shadow-light) !important;
    transition: var(--transition) !important;
    margin-bottom: 20px !important;
}

[data-theme="modern-black"] .card:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow) !important;
}

[data-theme="modern-black"] .card-header {
    background: var(--elevated-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border) !important;
    font-weight: 600 !important;
}

[data-theme="modern-black"] .card-body {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .card-title {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
}

[data-theme="modern-black"] .card-text {
    color: var(--text-secondary) !important;
}

/* ===== الجداول المحسنة ===== */
[data-theme="modern-black"] .table,
[data-theme="modern-black"] table,
[data-theme="modern-black"] .table-responsive table,
[data-theme="modern-black"] .data-table table,
[data-theme="modern-black"] .table-container table {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-radius: var(--radius) !important;
    overflow: hidden !important;
    box-shadow: var(--shadow-light) !important;
    border: 1px solid var(--border) !important;
}

[data-theme="modern-black"] .table th,
[data-theme="modern-black"] table th,
[data-theme="modern-black"] .table-responsive table th,
[data-theme="modern-black"] .data-table table th,
[data-theme="modern-black"] .table-container table th {
    background: var(--elevated-bg) !important;
    color: var(--text-primary) !important;
    border-color: var(--border) !important;
    font-weight: 600 !important;
    border-bottom: 2px solid var(--border) !important;
    padding: 12px 8px !important;
}

[data-theme="modern-black"] .table td,
[data-theme="modern-black"] table td,
[data-theme="modern-black"] .table-responsive table td,
[data-theme="modern-black"] .data-table table td,
[data-theme="modern-black"] .table-container table td {
    background: var(--card-bg) !important;
    border-color: var(--border) !important;
    color: var(--text-primary) !important;
    padding: 10px 8px !important;
    border-bottom: 1px solid var(--border) !important;
}

[data-theme="modern-black"] .table tbody tr:hover,
[data-theme="modern-black"] table tbody tr:hover,
[data-theme="modern-black"] .table-responsive table tbody tr:hover,
[data-theme="modern-black"] .data-table table tbody tr:hover,
[data-theme="modern-black"] .table-container table tbody tr:hover {
    background: var(--elevated-bg) !important;
    color: var(--text-primary) !important;
}

/* جداول Bootstrap المخططة */
[data-theme="modern-black"] .table-striped tbody tr:nth-of-type(odd) td,
[data-theme="modern-black"] table.table-striped tbody tr:nth-of-type(odd) td {
    background: rgba(255, 255, 255, 0.02) !important;
}

[data-theme="modern-black"] .table-striped tbody tr:nth-of-type(even) td,
[data-theme="modern-black"] table.table-striped tbody tr:nth-of-type(even) td {
    background: var(--card-bg) !important;
}

/* جداول Django Admin */
[data-theme="modern-black"] #result_list,
[data-theme="modern-black"] .results table,
[data-theme="modern-black"] .module table {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border) !important;
}

[data-theme="modern-black"] #result_list th,
[data-theme="modern-black"] .results table th,
[data-theme="modern-black"] .module table th {
    background: var(--elevated-bg) !important;
    color: var(--text-primary) !important;
    border-color: var(--border) !important;
}

[data-theme="modern-black"] #result_list td,
[data-theme="modern-black"] .results table td,
[data-theme="modern-black"] .module table td {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-color: var(--border) !important;
}

/* تحسين جداول المخزون */
[data-theme="modern-black"] .inventory-table,
[data-theme="modern-black"] .product-table,
[data-theme="modern-black"] .stock-table {
    background: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius) !important;
}

[data-theme="modern-black"] .inventory-table th,
[data-theme="modern-black"] .product-table th,
[data-theme="modern-black"] .stock-table th {
    background: var(--elevated-bg) !important;
    color: var(--text-primary) !important;
    border-color: var(--border) !important;
}

[data-theme="modern-black"] .inventory-table td,
[data-theme="modern-black"] .product-table td,
[data-theme="modern-black"] .stock-table td {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-color: var(--border) !important;
}

/* تحسين الجداول المتجاوبة */
[data-theme="modern-black"] .table-responsive {
    background: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius) !important;
    box-shadow: var(--shadow-light) !important;
}

/* تحسين الفورمز في الجداول */
[data-theme="modern-black"] .table .form-control,
[data-theme="modern-black"] table .form-control {
    background: var(--elevated-bg) !important;
    border: 1px solid var(--border) !important;
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .table .form-control:focus,
[data-theme="modern-black"] table .form-control:focus {
    background: var(--card-bg) !important;
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 2px rgba(0, 210, 255, 0.2) !important;
}

/* ===== الأزرار ===== */
[data-theme="modern-black"] .btn {
    border-radius: var(--radius) !important;
    font-weight: 500 !important;
    transition: var(--transition) !important;
    border: none !important;
}

[data-theme="modern-black"] .btn:active {
    transform: scale(0.98) !important;
}

[data-theme="modern-black"] .btn-primary {
    background: linear-gradient(135deg, var(--primary), #0099CC) !important;
    color: #ffffff !important;
    box-shadow: 0 4px 15px rgba(0, 210, 255, 0.3) !important;
}

[data-theme="modern-black"] .btn-primary:hover {
    background: linear-gradient(135deg, #00B8E6, var(--primary)) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 20px rgba(0, 210, 255, 0.4) !important;
    color: #ffffff !important;
}

[data-theme="modern-black"] .btn-success {
    background: linear-gradient(135deg, var(--success), #00CC6A) !important;
    color: #ffffff !important;
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3) !important;
}

[data-theme="modern-black"] .btn-warning {
    background: linear-gradient(135deg, var(--warning), #E6C200) !important;
    color: #ffffff !important;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3) !important;
}

[data-theme="modern-black"] .btn-danger {
    background: linear-gradient(135deg, var(--error), #E6004D) !important;
    color: #ffffff !important;
    box-shadow: 0 4px 15px rgba(255, 51, 102, 0.3) !important;
}

[data-theme="modern-black"] .btn-info {
    background: linear-gradient(135deg, var(--info), #0099CC) !important;
    color: #ffffff !important;
    box-shadow: 0 4px 15px rgba(0, 210, 255, 0.3) !important;
}

/* ===== أزرار التنقل العلوي ===== */
[data-theme="modern-black"] .btn-outline-light {
    border: 1px solid var(--border) !important;
    color: var(--text-primary) !important;
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: var(--blur) !important;
}

[data-theme="modern-black"] .btn-outline-light:hover {
    background: var(--primary) !important;
    color: #000000 !important;
    border-color: var(--primary) !important;
    transform: translateY(-1px) !important;
}

/* ===== النماذج ===== */
[data-theme="modern-black"] .form-control {
    background: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius) !important;
    color: var(--text-primary) !important;
    transition: var(--transition) !important;
}

[data-theme="modern-black"] .form-control:focus {
    background: var(--elevated-bg) !important;
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 2px rgba(0, 210, 255, 0.2) !important;
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .form-control::placeholder {
    color: var(--text-tertiary) !important;
}

[data-theme="modern-black"] .form-label {
    color: var(--text-primary) !important;
    font-weight: 500 !important;
}

[data-theme="modern-black"] .form-select {
    background: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .form-select:focus {
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 2px rgba(0, 210, 255, 0.2) !important;
}

/* ===== القوائم المنسدلة المحسنة ===== */
[data-theme="modern-black"] .dropdown-menu {
    background: var(--card-bg) !important;
    backdrop-filter: var(--blur) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius-large) !important;
    box-shadow: var(--shadow) !important;
    z-index: 9999 !important;
    position: absolute !important;
}

[data-theme="modern-black"] .dropdown-item {
    color: var(--text-primary) !important;
    transition: var(--transition) !important;
    background: transparent !important;
}

[data-theme="modern-black"] .dropdown-item:hover,
[data-theme="modern-black"] .dropdown-item:focus {
    background: var(--elevated-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .dropdown-item:active {
    background: var(--primary) !important;
    color: #000000 !important;
}

/* قائمة المستخدم المحسنة */
[data-theme="modern-black"] #userDropdown {
    background: var(--elevated-bg) !important;
    border-color: var(--border) !important;
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] #userDropdown:hover {
    background: var(--primary) !important;
    color: #000000 !important;
    border-color: var(--primary) !important;
}

/* منتقي الثيم في القائمة */
[data-theme="modern-black"] #themeSelector {
    background: var(--elevated-bg) !important;
    border: 1px solid var(--border) !important;
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] #themeSelector:focus {
    background: var(--card-bg) !important;
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 2px rgba(0, 210, 255, 0.2) !important;
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] #themeSelector option {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

/* أزرار الإشعارات */
[data-theme="modern-black"] #notificationsDropdown {
    background: var(--elevated-bg) !important;
    border-color: var(--border) !important;
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] #notificationsDropdown:hover {
    background: var(--primary) !important;
    color: #000000 !important;
    border-color: var(--primary) !important;
}

/* قائمة الإشعارات */
[data-theme="modern-black"] .notification-dropdown {
    background: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
}

[data-theme="modern-black"] .dropdown-header {
    color: var(--text-primary) !important;
    background: var(--elevated-bg) !important;
}

[data-theme="modern-black"] .dropdown-divider {
    border-color: var(--border) !important;
}

/* زر تسجيل الدخول */
[data-theme="modern-black"] .btn-outline-light {
    border: 1px solid var(--border) !important;
    color: var(--text-primary) !important;
    background: var(--elevated-bg) !important;
}

[data-theme="modern-black"] .btn-outline-light:hover {
    background: var(--primary) !important;
    color: #000000 !important;
    border-color: var(--primary) !important;
}

/* ===== الشارات ===== */
[data-theme="modern-black"] .badge {
    border-radius: var(--radius) !important;
    font-weight: 500 !important;
}

[data-theme="modern-black"] .badge.bg-success {
    background: var(--success) !important;
    color: #000000 !important;
}

[data-theme="modern-black"] .badge.bg-danger {
    background: var(--error) !important;
    color: #ffffff !important;
}

[data-theme="modern-black"] .badge.bg-warning {
    background: var(--warning) !important;
    color: #000000 !important;
}

[data-theme="modern-black"] .badge.bg-info {
    background: var(--info) !important;
    color: #000000 !important;
}

[data-theme="modern-black"] .badge.bg-primary {
    background: var(--primary) !important;
    color: #000000 !important;
}

/* ===== الإشعارات ===== */
[data-theme="modern-black"] .alert {
    border: none !important;
    border-radius: var(--radius-large) !important;
    backdrop-filter: var(--blur) !important;
}

[data-theme="modern-black"] .alert-success {
    background: rgba(0, 255, 136, 0.15) !important;
    color: var(--success) !important;
    border-left: 4px solid var(--success) !important;
}

[data-theme="modern-black"] .alert-danger {
    background: rgba(255, 51, 102, 0.15) !important;
    color: var(--error) !important;
    border-left: 4px solid var(--error) !important;
}

[data-theme="modern-black"] .alert-warning {
    background: rgba(255, 215, 0, 0.15) !important;
    color: var(--warning) !important;
    border-left: 4px solid var(--warning) !important;
}

[data-theme="modern-black"] .alert-info {
    background: rgba(0, 210, 255, 0.15) !important;
    color: var(--info) !important;
    border-left: 4px solid var(--info) !important;
}

/* ===== التذييل المُحسَّن ===== */
[data-theme="modern-black"] .footer-area {
    background: linear-gradient(135deg, var(--surface), var(--card-bg)) !important;
    color: var(--text-primary) !important;
    border-top: 1px solid var(--border) !important;
    padding: 20px 0 10px 0 !important; /* نصف الحجم الأصلي */
    backdrop-filter: var(--blur) !important;
}

[data-theme="modern-black"] .footer-title {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
    font-size: 1rem !important; /* أصغر */
    margin-bottom: 10px !important; /* أقل */
}

[data-theme="modern-black"] .footer-link {
    color: var(--text-secondary) !important;
    transition: var(--transition) !important;
    font-size: 0.85rem !important; /* أصغر */
    padding: 2px 0 !important; /* أقل */
}

[data-theme="modern-black"] .footer-link:hover {
    color: var(--primary) !important;
    transform: translateX(-2px) !important; /* حركة أقل */
}

[data-theme="modern-black"] .footer-contact-item {
    color: var(--text-secondary) !important;
    font-size: 0.85rem !important; /* أصغر */
    margin-bottom: 4px !important; /* أقل */
}

[data-theme="modern-black"] .icon-circle {
    width: 24px !important;
    height: 24px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid var(--border) !important;
}

[data-theme="modern-black"] .social-icon {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid var(--border) !important;
    width: 32px !important; /* أصغر */
    height: 32px !important;
    transition: var(--transition) !important;
}

[data-theme="modern-black"] .social-icon:hover {
    background: var(--primary) !important;
    color: #000000 !important;
    transform: translateY(-1px) !important; /* حركة أقل */
}

[data-theme="modern-black"] .copyright-text {
    color: var(--text-tertiary) !important;
    font-size: 0.8rem !important; /* أصغر */
    margin-top: 15px !important; /* أقل */
    padding-top: 15px !important; /* أقل */
    border-top: 1px solid var(--border) !important;
}

/* ===== ألوان النصوص ===== */
[data-theme="modern-black"] h1,
[data-theme="modern-black"] h2,
[data-theme="modern-black"] h3,
[data-theme="modern-black"] h4,
[data-theme="modern-black"] h5,
[data-theme="modern-black"] h6 {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
}

[data-theme="modern-black"] p,
[data-theme="modern-black"] span,
[data-theme="modern-black"] div {
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .text-muted {
    color: var(--text-secondary) !important;
}

[data-theme="modern-black"] .text-info {
    color: var(--info) !important;
}

[data-theme="modern-black"] .text-success {
    color: var(--success) !important;
}

[data-theme="modern-black"] .text-warning {
    color: var(--warning) !important;
}

[data-theme="modern-black"] .text-danger {
    color: var(--error) !important;
}

[data-theme="modern-black"] .text-primary {
    color: var(--primary) !important;
}

/* ===== تأثيرات الحركة ===== */
[data-theme="modern-black"] * {
    transition: var(--transition) !important;
}

/* ===== تحسينات خاصة بالاستجابة ===== */
@media (max-width: 768px) {
    [data-theme="modern-black"] .footer-area {
        padding: 15px 0 8px 0 !important;
    }
    
    [data-theme="modern-black"] .footer-title {
        font-size: 0.9rem !important;
        margin-bottom: 8px !important;
    }
    
    [data-theme="modern-black"] .footer-link,
    [data-theme="modern-black"] .footer-contact-item {
        font-size: 0.8rem !important;
    }
    
    [data-theme="modern-black"] .social-icon {
        width: 30px !important;
        height: 30px !important;
    }
    
    [data-theme="modern-black"] .icon-circle {
        width: 26px !important;
        height: 26px !important;
    }
}

/* ===== تأثيرات خاصة للتفاعل ===== */
[data-theme="modern-black"] .card:hover .card-title {
    color: var(--primary) !important;
}

[data-theme="modern-black"] .nav-link:hover i {
    filter: brightness(1.2) drop-shadow(0 0 8px currentColor) !important;
}

[data-theme="modern-black"] .btn:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
}

/* ===== تحسينات الوضوح ===== */
[data-theme="modern-black"] .table-striped tbody tr:nth-of-type(odd) {
    background: rgba(255, 255, 255, 0.02) !important;
}

[data-theme="modern-black"] .list-group-item {
    background: var(--card-bg) !important;
    border-color: var(--border) !important;
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .list-group-item:hover {
    background: rgba(255, 255, 255, 0.05) !important;
}
