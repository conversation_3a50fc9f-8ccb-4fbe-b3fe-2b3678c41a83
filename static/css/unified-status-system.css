/* ========================================
   نظام موحد للحالات والألوان
   Unified Status and Color System
   ======================================== */

/* ========================================
   ألوان الحالات الموحدة
   Unified Status Colors
   ======================================== */

/* الحالات الأساسية - Basic Statuses */
.status-pending {
    background-color: #ffc107 !important;
    color: #212529 !important;
    border: 1px solid #ffc107;
}

.status-pending_approval {
    background-color: #ffc107 !important;
    color: #212529 !important;
    border: 1px solid #ffc107;
}

.status-processing {
    background-color: #17a2b8 !important;
    color: white !important;
    border: 1px solid #17a2b8;
}

.status-in_progress {
    background-color: #17a2b8 !important;
    color: white !important;
    border: 1px solid #17a2b8;
}

.status-ready_install {
    background-color: #6f42c1 !important;
    color: white !important;
    border: 1px solid #6f42c1;
}

.status-completed {
    background-color: #28a745 !important;
    color: white !important;
    border: 1px solid #28a745;
}

.status-delivered {
    background-color: #20c997 !important;
    color: white !important;
    border: 1px solid #20c997;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-rejected {
    background-color: #dc3545 !important;
    color: white !important;
    border: 1px solid #dc3545;
}

.status-cancelled {
    background-color: #6c757d !important;
    color: white !important;
    border: 1px solid #6c757d;
}

.status-scheduled {
    background-color: #17a2b8 !important;
    color: white !important;
    border: 1px solid #17a2b8;
}

.status-not_scheduled {
    background-color: #f8f9fa !important;
    color: #6c757d !important;
    border: 1px solid #dee2e6;
}

.status-modification_required {
    background-color: #fd7e14 !important;
    color: white !important;
    border: 1px solid #fd7e14;
}

.status-modification_in_progress {
    background-color: #17a2b8 !important;
    color: white !important;
    border: 1px solid #17a2b8;
}

.status-modification_completed {
    background-color: #28a745 !important;
    color: white !important;
    border: 1px solid #28a745;
}

/* ========================================
   ألوان أنواع الطلبات الموحدة
   Unified Order Type Colors
   ======================================== */

.order-type-installation {
    background-color: #ffc107 !important;
    color: #212529 !important;
    border: 1px solid #ffc107;
}

.order-type-tailoring {
    background-color: #28a745 !important;
    color: white !important;
    border: 1px solid #28a745;
}

.order-type-accessory {
    background-color: #007bff !important;
    color: white !important;
    border: 1px solid #007bff;
}

.order-type-inspection {
    background-color: #17a2b8 !important;
    color: white !important;
    border: 1px solid #17a2b8;
}

/* ========================================
   أنواع الطلبات النصية
   Text Order Types
   ======================================== */

.order-type-text {
    color: #007bff !important;
    font-weight: 600;
    text-decoration: none;
    background: none !important;
    border: none !important;
    padding: 0 !important;
}

.order-type-text:hover {
    color: #0056b3 !important;
    text-decoration: underline;
}

/* ========================================
   أنواع الطلبات VIP
   VIP Order Types
   ======================================== */

.order-type-vip {
    background: linear-gradient(45deg, #ffd700, #ffed4e) !important;
    color: #212529 !important;
    border: 2px solid #ffd700 !important;
    font-weight: bold !important;
    position: relative;
    padding: 0.375rem 0.75rem !important;
}

.order-type-vip::before {
    content: "⭐ ";
    color: #ffd700;
    font-size: 0.8em;
}

.order-type-vip::after {
    content: " ⭐";
    color: #ffd700;
    font-size: 0.8em;
}

/* ========================================
   إشارات الحالة (أيقونات)
   Status Indicators (Icons)
   ======================================== */

.status-indicator {
    display: inline-block;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    line-height: 1;
}

.status-indicator.success {
    color: #28a745;
    background: none;
}

.status-indicator.error {
    color: #dc3545;
    background: none;
}

.status-indicator.warning {
    color: #ffc107;
    background: none;
}

.status-indicator.info {
    color: #17a2b8;
    background: none;
}

.status-indicator.secondary {
    color: #6c757d;
    background: none;
}

/* ========================================
   Badges موحدة
   Unified Badges
   ======================================== */

.unified-badge {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.unified-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* ========================================
   تأثيرات إضافية
   Additional Effects
   ======================================== */

.status-badge {
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.status-badge:hover::before {
    left: 100%;
}

/* ========================================
   تحسينات للأجهزة المحمولة
   Mobile Optimizations
   ======================================== */

@media (max-width: 768px) {
    .unified-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    
    .order-type-vip {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem !important;
    }
    
    .status-indicator {
        font-size: 12px;
        line-height: 1;
    }
}

/* ========================================
   تأثيرات التحميل
   Loading Effects
   ======================================== */

.status-loading {
    position: relative;
    overflow: hidden;
}

.status-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* ========================================
   ألوان مخصصة للحالات الخاصة
   Custom Colors for Special Statuses
   ======================================== */

.status-vip {
    background: linear-gradient(45deg, #ffd700, #ffed4e) !important;
    color: #212529 !important;
    border: 2px solid #ffd700 !important;
    font-weight: bold !important;
}

.status-urgent {
    background-color: #dc3545 !important;
    color: white !important;
    border: 1px solid #dc3545;
    animation: urgent-pulse 2s infinite;
}

@keyframes urgent-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

/* ========================================
   تحسينات الأداء
   Performance Optimizations
   ======================================== */

.status-badge,
.unified-badge,
.order-type-badge {
    will-change: transform;
    backface-visibility: hidden;
}

/* ========================================
   دعم الوضع المظلم
   Dark Mode Support
   ======================================== */

@media (prefers-color-scheme: dark) {
    .order-type-text {
        color: #4dabf7 !important;
    }
    
    .order-type-text:hover {
        color: #74c0fc !important;
    }
    
    .status-not_scheduled {
        background-color: #495057 !important;
        color: #adb5bd !important;
        border: 1px solid #6c757d;
    }
} 