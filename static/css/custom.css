/**
 * أنماط مخصصة للنظام
 */

/* ===== المتغيرات ===== */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --body-bg: #f8f9fa;
    --card-bg: #ffffff;
    --sidebar-bg: #343a40;
    --header-bg: #ffffff;
    --footer-bg: #ffffff;
    
    --text-color: #212529;
    --text-muted: #6c757d;
    --link-color: #007bff;
    --link-hover-color: #0056b3;
    
    --border-color: #dee2e6;
    --border-radius: 0.25rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    
    --font-family-sans-serif: 'Cairo', sans-serif;
    --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
}

/* ===== الأنماط الأساسية ===== */
body {
    font-family: var(--font-family-sans-serif);
    background-color: var(--body-bg);
    color: var(--text-color);
    direction: rtl;
    text-align: right;
}

a {
    color: var(--link-color);
    text-decoration: none;
}

a:hover {
    color: var(--link-hover-color);
    text-decoration: underline;
}

/* ===== الشريط الجانبي ===== */
.sidebar {
    background-color: var(--sidebar-bg);
    color: #fff;
    min-height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    width: 250px;
    z-index: 1000;
    transition: all 0.3s;
}

.sidebar-header {
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.1);
}

.sidebar-brand {
    color: #fff;
    font-size: 1.5rem;
    font-weight: bold;
}

.sidebar-menu {
    padding: 0;
    list-style: none;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu a {
    color: rgba(255, 255, 255, 0.8);
    padding: 10px 20px;
    display: block;
    transition: all 0.3s;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    text-decoration: none;
}

.sidebar-menu .icon {
    margin-left: 10px;
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    margin-right: 250px;
    padding: 20px;
    transition: all 0.3s;
}

.content-wrapper {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.page-header {
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.page-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0;
}

/* ===== البطاقات ===== */
.card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem 1.25rem;
}

.card-title {
    margin-bottom: 0;
    font-size: 1.25rem;
    font-weight: bold;
}

.card-body {
    padding: 1.25rem;
}

/* ===== النماذج ===== */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-text {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: 0.875rem;
}

/* ===== الجداول ===== */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid var(--border-color);
}

/* ===== رؤوس الجداول في النوافذ المنبثقة ===== */
.modal .table thead th,
.modal-table thead th {
    background-color: #8B4513 !important; /* اللون الترابي */
    color: white !important;
    border-color: #8B4513 !important;
    font-weight: bold;
}

.modal .table thead th:hover,
.modal-table thead th:hover {
    background-color: #A0522D !important; /* لون ترابي أفتح عند التمرير */
}

/* ===== رؤوس الجداول في صفحة العملاء ===== */
.customers-table thead th {
    background-color: #8B4513 !important;
    color: white !important;
    border-color: #8B4513 !important;
    font-weight: bold;
}

.customers-table thead th:hover {
    background-color: #A0522D !important;
}

/* ===== هيد جدول الطلبات - نفس ألوان جدول العملاء ===== */
.orders-table thead th {
    background-color: var(--primary) !important;
    color: #fff !important;
    border-bottom: 1px solid var(--border) !important;
    font-weight: 600 !important;
    text-align: center !important;
    padding: 12px 8px !important;
    font-size: 0.9rem !important;
}

/* ===== تنسيق هيد الجدول للثيم الافتراضي ===== */
.orders-table thead th {
    background-color: #8B735A !important;
    color: #fff !important;
    border-bottom: 1px solid #DED5CE !important;
}

/* ===== تنسيق هيد الجدول للثيم المخصص ===== */
[data-theme="custom-theme"] .orders-table thead th {
    background-color: #6A5743 !important;
    color: #fff !important;
    border-bottom: 1px solid #C9BEB4 !important;
}

/* ===== تنسيق هيد الجدول للثيم الأسود ===== */
[data-theme="modern-black"] .orders-table thead th {
    background-color: #00D2FF !important;
    color: #000 !important;
    border-bottom: 1px solid #333333 !important;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid var(--border-color);
    background-color: rgba(0, 0, 0, 0.03);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

/* ===== الأزرار ===== */
.btn {
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
    color: #fff;
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: #fff;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
    color: #fff;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: #fff;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
    color: #fff;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: #fff;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: #fff;
}

/* ===== التنبيهات ===== */
.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* ===== الشارات ===== */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--border-radius);
}

.badge-primary {
    background-color: var(--primary-color);
    color: #fff;
}

.badge-secondary {
    background-color: var(--secondary-color);
    color: #fff;
}

.badge-success {
    background-color: var(--success-color);
    color: #fff;
}

.badge-danger {
    background-color: var(--danger-color);
    color: #fff;
}

.badge-warning {
    background-color: var(--warning-color);
    color: #212529;
}

.badge-info {
    background-color: var(--info-color);
    color: #fff;
}

/* ===== الوضع الليلي ===== */
body.dark-mode {
    --body-bg: #121212;
    --card-bg: #1e1e1e;
    --sidebar-bg: #1e1e1e;
    --header-bg: #1e1e1e;
    --footer-bg: #1e1e1e;
    
    --text-color: #e0e0e0;
    --text-muted: #a0a0a0;
    --link-color: #4da3ff;
    --link-hover-color: #80bdff;
    
    --border-color: #444444;
}

body.dark-mode .table {
    color: var(--text-color);
}

body.dark-mode .table thead th {
    background-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.075);
}

body.dark-mode .card-header {
    background-color: rgba(255, 255, 255, 0.05);
}

/* ===== الاستجابة للشاشات المختلفة ===== */
@media (max-width: 768px) {
    .sidebar {
        width: 0;
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .sidebar.active {
        width: 250px;
    }
    
    .main-content.active {
        margin-right: 250px;
    }
}
