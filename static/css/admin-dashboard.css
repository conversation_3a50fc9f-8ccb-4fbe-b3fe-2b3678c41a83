/* Admin Dashboard Custom Styles */

.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    color: white;
    overflow: hidden;
    position: relative;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.dashboard-card.customers {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dashboard-card.orders {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.dashboard-card.manufacturing {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.dashboard-card.inspections {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.dashboard-card.installations {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.dashboard-card.inventory {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
}

.stat-icon {
    font-size: 3rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: 1px solid rgba(0,0,0,0.05);
}

.chart-container h5 {
    color: #333;
    font-weight: 600;
    margin-bottom: 20px;
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 10px;
}

.filter-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    border: 1px solid rgba(0,0,0,0.05);
}

.filter-section h4 {
    color: #333;
    font-weight: 600;
    margin-bottom: 20px;
}

.comparison-card {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0,0,0,0.05);
}

.comparison-card h4 {
    color: #333;
    font-weight: 600;
    margin-bottom: 20px;
}

.metric-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-item {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
}

.metric-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}

.metric-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.trend-indicator {
    font-size: 0.8rem;
    padding: 4px 10px;
    border-radius: 12px;
    margin-left: 10px;
    font-weight: bold;
}

.trend-up {
    background: #d4edda;
    color: #155724;
}

.trend-down {
    background: #f8d7da;
    color: #721c24;
}

.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 500;
}

.filter-form {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 180px;
}

.filter-label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
    font-size: 0.9rem;
}

.filter-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 15px;
    background: white;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-select:focus {
    border-color: #667eea;
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.apply-filters-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.apply-filters-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    color: white;
}

.apply-filters-btn:active {
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-title {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .filter-form {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: 100%;
    }
    
    .metric-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .chart-container {
        padding: 20px;
    }
    
    .filter-section {
        padding: 20px;
    }
}

@media (max-width: 576px) {
    .dashboard-header {
        padding: 30px 20px;
    }
    
    .dashboard-title {
        font-size: 1.8rem;
    }
    
    .dashboard-subtitle {
        font-size: 1rem;
    }
    
    .stat-number {
        font-size: 1.8rem;
    }
    
    .stat-icon {
        font-size: 2.5rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar */
.chart-container::-webkit-scrollbar {
    width: 8px;
}

.chart-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.chart-container::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 4px;
}

.chart-container::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
} 