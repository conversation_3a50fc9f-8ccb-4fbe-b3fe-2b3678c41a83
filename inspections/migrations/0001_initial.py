# Generated by Django 5.2.4 on 2025-07-15 15:33

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        ('customers', '0001_initial'),
        ('orders', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Inspection',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'contract_number',
                    models.CharField(
                        blank=True,
                        max_length=50,
                        null=True,
                        unique=True,
                        verbose_name='رقم العقد',
                    ),
                ),
                (
                    'is_from_orders',
                    models.BooleanField(default=False, verbose_name='من قسم الطلبات'),
                ),
                (
                    'windows_count',
                    models.IntegerField(
                        blank=True, null=True, verbose_name='عدد الشبابيك'
                    ),
                ),
                (
                    'inspection_file',
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to='inspections/files/',
                        verbose_name='ملف المعاينة',
                    ),
                ),
                (
                    'google_drive_file_id',
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name='معرف ملف Google Drive',
                    ),
                ),
                (
                    'google_drive_file_url',
                    models.URLField(
                        blank=True, null=True, verbose_name='رابط ملف Google Drive'
                    ),
                ),
                (
                    'google_drive_file_name',
                    models.CharField(
                        blank=True,
                        max_length=500,
                        null=True,
                        verbose_name='اسم الملف في Google Drive',
                    ),
                ),
                (
                    'is_uploaded_to_drive',
                    models.BooleanField(
                        default=False, verbose_name='تم الرفع إلى Google Drive'
                    ),
                ),
                ('request_date', models.DateField(verbose_name='تاريخ طلب المعاينة')),
                (
                    'scheduled_date',
                    models.DateField(verbose_name='تاريخ تنفيذ المعاينة'),
                ),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('pending', 'قيد الانتظار'),
                            ('scheduled', 'مجدول'),
                            ('completed', 'مكتملة'),
                            ('cancelled', 'ملغية'),
                        ],
                        default='pending',
                        max_length=10,
                        verbose_name='الحالة',
                    ),
                ),
                (
                    'result',
                    models.CharField(
                        blank=True,
                        choices=[('passed', 'ناجحة'), ('failed', 'غير مجدية')],
                        max_length=10,
                        null=True,
                        verbose_name='النتيجة',
                    ),
                ),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                (
                    'order_notes',
                    models.TextField(
                        blank=True,
                        help_text='نسخة ثابتة من ملاحظات الطلب',
                        verbose_name='ملاحظات الطلب',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
                ),
                (
                    'completed_at',
                    models.DateTimeField(
                        blank=True, null=True, verbose_name='تاريخ الإكتمال'
                    ),
                ),
                (
                    'expected_delivery_date',
                    models.DateField(
                        blank=True,
                        help_text='يتم حسابه تلقائياً بناءً على نوع الطلب',
                        null=True,
                        verbose_name='تاريخ التسليم المتوقع',
                    ),
                ),
                (
                    'branch',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='inspections',
                        to='accounts.branch',
                        verbose_name='الفرع',
                    ),
                ),
                (
                    'created_by',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='created_inspections',
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='تم الإنشاء بواسطة',
                    ),
                ),
                (
                    'customer',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='inspections',
                        to='customers.customer',
                        verbose_name='العميل',
                    ),
                ),
                (
                    'inspector',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='assigned_inspections',
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='المعاين',
                    ),
                ),
                (
                    'order',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='inspections',
                        to='orders.order',
                        verbose_name='الطلب المرتبط',
                    ),
                ),
                (
                    'responsible_employee',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='inspections',
                        to='accounts.salesperson',
                        verbose_name='البائع',
                    ),
                ),
            ],
            options={
                'verbose_name': 'معاينة',
                'verbose_name_plural': 'المعاينات',
                'ordering': ['-request_date'],
            },
        ),
        migrations.CreateModel(
            name='InspectionEvaluation',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'criteria',
                    models.CharField(
                        choices=[
                            ('location', 'الموقع'),
                            ('condition', 'الحالة'),
                            ('suitability', 'الملاءمة'),
                            ('safety', 'السلامة'),
                            ('accessibility', 'سهولة الوصول'),
                        ],
                        max_length=20,
                        verbose_name='معيار التقييم',
                    ),
                ),
                (
                    'rating',
                    models.IntegerField(
                        choices=[
                            (1, 'ضعيف'),
                            (2, 'مقبول'),
                            (3, 'جيد'),
                            (4, 'جيد جداً'),
                            (5, 'ممتاز'),
                        ],
                        verbose_name='التقييم',
                    ),
                ),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات التقييم')),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ التقييم'
                    ),
                ),
                (
                    'created_by',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='evaluations_created',
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='تم التقييم بواسطة',
                    ),
                ),
                (
                    'inspection',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='evaluations',
                        to='inspections.inspection',
                        verbose_name='المعاينة',
                    ),
                ),
            ],
            options={
                'verbose_name': 'تقييم المعاينة',
                'verbose_name_plural': 'تقييمات المعاينات',
            },
        ),
        migrations.CreateModel(
            name='InspectionNotification',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'type',
                    models.CharField(
                        choices=[
                            ('scheduled', 'موعد معاينة'),
                            ('reminder', 'تذكير'),
                            ('status_change', 'تغيير الحالة'),
                            ('evaluation', 'تقييم جديد'),
                        ],
                        max_length=20,
                        verbose_name='نوع التنبيه',
                    ),
                ),
                ('message', models.TextField(verbose_name='نص التنبيه')),
                (
                    'is_read',
                    models.BooleanField(default=False, verbose_name='تم القراءة'),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ التنبيه'
                    ),
                ),
                (
                    'scheduled_for',
                    models.DateTimeField(
                        blank=True, null=True, verbose_name='موعد التنبيه'
                    ),
                ),
                (
                    'inspection',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='notifications',
                        to='inspections.inspection',
                        verbose_name='المعاينة',
                    ),
                ),
            ],
            options={
                'verbose_name': 'تنبيه معاينة',
                'verbose_name_plural': 'تنبيهات المعاينات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InspectionReport',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'title',
                    models.CharField(max_length=200, verbose_name='عنوان التقرير'),
                ),
                (
                    'report_type',
                    models.CharField(
                        choices=[
                            ('daily', 'يومي'),
                            ('weekly', 'أسبوعي'),
                            ('monthly', 'شهري'),
                            ('custom', 'مخصص'),
                        ],
                        max_length=10,
                        verbose_name='نوع التقرير',
                    ),
                ),
                ('date_from', models.DateField(verbose_name='من تاريخ')),
                ('date_to', models.DateField(verbose_name='إلى تاريخ')),
                (
                    'total_inspections',
                    models.IntegerField(default=0, verbose_name='إجمالي المعاينات'),
                ),
                (
                    'successful_inspections',
                    models.IntegerField(default=0, verbose_name='المعاينات الناجحة'),
                ),
                (
                    'pending_inspections',
                    models.IntegerField(default=0, verbose_name='المعاينات المعلقة'),
                ),
                (
                    'cancelled_inspections',
                    models.IntegerField(default=0, verbose_name='المعاينات الملغاة'),
                ),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ إنشاء التقرير'
                    ),
                ),
                (
                    'branch',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='inspection_reports',
                        to='accounts.branch',
                        verbose_name='الفرع',
                    ),
                ),
                (
                    'created_by',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='inspection_reports_created',
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='تم الإنشاء بواسطة',
                    ),
                ),
            ],
            options={
                'verbose_name': 'تقرير معاينات',
                'verbose_name_plural': 'تقارير المعاينات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(
                fields=['contract_number'], name='inspection_contract_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['customer'], name='inspection_customer_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['branch'], name='inspection_branch_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['inspector'], name='inspection_inspector_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['status'], name='inspection_status_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['result'], name='inspection_result_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['request_date'], name='inspection_req_date_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(
                fields=['scheduled_date'], name='inspection_sched_date_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['order'], name='inspection_order_idx'),
        ),
        migrations.AddIndex(
            model_name='inspection',
            index=models.Index(fields=['created_at'], name='inspection_created_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionevaluation',
            index=models.Index(fields=['inspection'], name='inspection_eval_insp_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionevaluation',
            index=models.Index(
                fields=['criteria'], name='inspection_eval_criteria_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='inspectionevaluation',
            index=models.Index(fields=['rating'], name='inspection_eval_rating_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionevaluation',
            index=models.Index(
                fields=['created_by'], name='inspection_eval_creator_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='inspectionevaluation',
            index=models.Index(
                fields=['created_at'], name='inspection_eval_created_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='inspectionnotification',
            index=models.Index(fields=['inspection'], name='inspection_notif_insp_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionnotification',
            index=models.Index(fields=['type'], name='inspection_notif_type_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionnotification',
            index=models.Index(fields=['is_read'], name='inspection_notif_read_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionnotification',
            index=models.Index(
                fields=['created_at'], name='inspection_notif_created_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='inspectionnotification',
            index=models.Index(
                fields=['scheduled_for'], name='inspection_notif_scheduled_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(
                fields=['report_type'], name='inspection_report_type_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(fields=['branch'], name='inspection_report_branch_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(fields=['date_from'], name='inspection_report_from_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(fields=['date_to'], name='inspection_report_to_idx'),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(
                fields=['created_at'], name='inspection_report_created_idx'
            ),
        ),
        migrations.AddIndex(
            model_name='inspectionreport',
            index=models.Index(
                fields=['created_by'], name='inspection_report_creator_idx'
            ),
        ),
    ]
