# دليل استخدام ملفات الإكسل البسيطة

## 📋 نظرة عامة

تم إنشاء ملفات إكسل بسيطة بدون تنسيقات معقدة لتجنب مشاكل `PatternFill.__init__() got an unexpected keyword argument 'extLst'`.

## 📁 الملفات المتاحة

### 1. `products_template_simple.xlsx` - قالب المنتجات البسيط
- **الوصف**: قالب لإضافة منتجات جديدة
- **الحجم**: 6077 بايت
- **الصفحات**: 
  - صفحة "المنتجات" - لإضافة منتجات جديدة
  - صفحة "تحديث المخزون" - لتحديث كميات المخزون

### 2. `simple_products.xlsx` - ملف المنتجات التجريبي
- **الوصف**: ملف تجريبي مع بيانات جاهزة
- **الحجم**: 5375 بايت
- **الاستخدام**: للاختبار والتعلم

### 3. `simple_stock_update.xlsx` - ملف تحديث المخزون
- **الوصف**: ملف لتحديث كميات المخزون
- **الحجم**: 5104 بايت
- **الاستخدام**: لتحديث الكميات الموجودة

## 🚀 كيفية الاستخدام

### الخطوة 1: تحميل القالب
1. استخدم الملف `products_template_simple.xlsx`
2. افتح الملف في Excel أو LibreOffice
3. ستجد صفحتين:
   - **المنتجات**: لإضافة منتجات جديدة
   - **تحديث المخزون**: لتحديث الكميات

### الخطوة 2: ملء البيانات

#### صفحة المنتجات:
| العمود | الوصف | مثال | مطلوب |
|--------|--------|------|--------|
| اسم المنتج | اسم المنتج | لابتوب HP | ✅ |
| الكود | رمز المنتج | LAP001 | ❌ |
| الفئة | فئة المنتج | أجهزة كمبيوتر | ❌ |
| السعر | سعر المنتج | 15000 | ✅ |
| الكمية | الكمية الأولية | 10 | ❌ |
| الوصف | وصف المنتج | لابتوب HP بروسيسور i5 | ❌ |
| الحد الأدنى | الحد الأدنى للمخزون | 5 | ❌ |
| العملة | عملة السعر | EGP | ❌ |
| الوحدة | وحدة القياس | قطعة | ❌ |

#### صفحة تحديث المخزون:
| العمود | الوصف | مثال | مطلوب |
|--------|--------|------|--------|
| كود المنتج | رمز المنتج الموجود | LAP001 | ✅ |
| الكمية | الكمية الجديدة | 25 | ✅ |
| ملاحظات | ملاحظات التحديث | تحديث بعد الجرد | ❌ |

### الخطوة 3: حفظ الملف
1. احفظ الملف بنفس التنسيق `.xlsx`
2. تأكد من عدم إضافة تنسيقات معقدة
3. تجنب استخدام الألوان والتنسيقات المتقدمة

### الخطوة 4: رفع الملف
1. اذهب إلى واجهة النظام
2. اختر "رفع ملف" أو "إضافة منتجات بالجملة"
3. اختر المستودع المناسب
4. ارفع الملف
5. اضغط "رفع" أو "إضافة"

## ⚠️ ملاحظات مهمة

### ✅ ما يجب فعله:
- استخدم الملفات البسيطة المقدمة
- املأ البيانات بدقة
- تأكد من صحة الأكواد للمنتجات الموجودة
- استخدم الأرقام فقط في حقول الكميات والأسعار

### ❌ ما يجب تجنبه:
- لا تستخدم ملفات معقدة التنسيق
- لا تضيف ألوان أو تنسيقات متقدمة
- لا تستخدم ملفات من مصادر غير موثوقة
- لا تغير أسماء الأعمدة

## 🔧 حل المشاكل

### مشكلة: "فشل في قراءة الملف"
**الحل**: استخدم الملفات البسيطة المقدمة فقط

### مشكلة: "الأعمدة مفقودة"
**الحل**: تأكد من وجود الأعمدة المطلوبة:
- `اسم المنتج` و `السعر` للمنتجات الجديدة
- `كود المنتج` و `الكمية` لتحديث المخزون

### مشكلة: "لا يوجد مستودع"
**الحل**: تأكد من وجود مستودع نشط في النظام

### مشكلة: "البيانات غير صحيحة"
**الحل**: 
- تأكد من أن الأسعار أرقام موجبة
- تأكد من أن الكميات أرقام صحيحة
- تأكد من صحة الأكواد للمنتجات الموجودة

## 📊 أمثلة على البيانات

### مثال لإضافة منتجات جديدة:
```
اسم المنتج    | الكود    | الفئة           | السعر | الكمية | الوصف
لابتوب HP     | LAP001   | أجهزة كمبيوتر  | 15000 | 10     | لابتوب HP بروسيسور i5
طابعة Canon   | PRN001   | طابعات         | 2500  | 5      | طابعة ليزر ملونة
```

### مثال لتحديث المخزون:
```
كود المنتج | الكمية | ملاحظات
LAP001     | 25     | تحديث بعد الجرد
PRN001     | 15     | إضافة مخزون جديد
```

## 🎯 أفضل الممارسات

1. **استخدم القوالب المقدمة**: تجنب إنشاء ملفات جديدة
2. **اختبر الملفات**: تأكد من صحة البيانات قبل الرفع
3. **احتفظ بنسخ احتياطية**: احتفظ بنسخة من البيانات الأصلية
4. **راجع النتائج**: تحقق من النتائج بعد الرفع
5. **استخدم أسماء واضحة**: استخدم أسماء وأكواد واضحة للمنتجات

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من استخدام الملفات البسيطة المقدمة
2. تحقق من صحة البيانات
3. تأكد من وجود مستودع نشط
4. راجع رسائل الخطأ بعناية

---
*تم إنشاء هذا الدليل لضمان استخدام آمن وموثوق لملفات الإكسل في النظام* 