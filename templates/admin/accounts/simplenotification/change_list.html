{% extends "admin/change_list.html" %}
{% load i18n admin_urls static admin_list %}

{% block content_title %}
    <h1>{{ title }}</h1>
    
    <!-- إحصائيات الإشعارات -->
    {% if notifications_stats %}
    <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
        <h3 style="margin: 0 0 15px 0; color: #495057;">
            <i class="fas fa-chart-bar" style="margin-left: 8px;"></i>
            إحصائيات الإشعارات
        </h3>
        
        <div style="display: flex; gap: 20px; flex-wrap: wrap;">
            <!-- إجمالي الإشعارات -->
            <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #dee2e6; min-width: 150px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #007bff; margin-bottom: 5px;">
                    {{ notifications_stats.total }}
                </div>
                <div style="color: #6c757d; font-size: 14px;">إجمالي الإشعارات</div>
            </div>
            
            <!-- الإشعارات غير المقروءة -->
            <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #dee2e6; min-width: 150px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #dc3545; margin-bottom: 5px;">
                    {{ notifications_stats.unread }}
                </div>
                <div style="color: #6c757d; font-size: 14px;">غير مقروءة</div>
            </div>
            
            <!-- الإشعارات العاجلة -->
            <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #dee2e6; min-width: 150px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #dc3545; margin-bottom: 5px;">
                    {{ notifications_stats.urgent }}
                </div>
                <div style="color: #6c757d; font-size: 14px;">عاجلة</div>
            </div>
            
            <!-- الإشعارات عالية الأولوية -->
            <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #dee2e6; min-width: 150px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #ffc107; margin-bottom: 5px;">
                    {{ notifications_stats.high }}
                </div>
                <div style="color: #6c757d; font-size: 14px;">عالية الأولوية</div>
            </div>
        </div>
        
        <!-- أزرار سريعة -->
        <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 10px 0; color: #495057; font-size: 16px;">إجراءات سريعة:</h4>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button onclick="markAllAsRead()" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                    ✅ تحديد الكل كمقروء
                </button>
                <button onclick="deleteOldNotifications()" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                    🗑️ حذف القديمة (30+ يوم)
                </button>
                <button onclick="deleteAllNotifications()" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px; border: 2px solid #fff;">
                    🗑️ حذف جميع الإشعارات
                </button>
                <button onclick="selectAllNotifications()" style="background: #6f42c1; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                    ☑️ تحديد الكل
                </button>
                <button onclick="showAll()" style="background: #fd7e14; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                    👁️ عرض الكل ({{ notifications_stats.total }})
                </button>
                <button onclick="refreshPage()" style="background: #17a2b8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                    🔄 تحديث
                </button>
            </div>
        </div>

        <!-- تنبيه مهم -->
        <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; color: #856404;">
            <h4 style="margin: 0 0 10px 0; color: #856404;">
                <i class="fas fa-exclamation-triangle me-2"></i>
                تنبيه مهم: الإشعارات الظاهرة في الواجهة 
            </h4>
            <p style="margin: 0 0 10px 0;">
                الإشعارات الـ 118 الظاهرة في الواجهة هي <strong>إشعارات جماعية</strong> وليست إشعارات بسيطة.
                لحذف أو إدارة هذه الإشعارات، يرجى الانتقال إلى:
            </p>
            <a href="/admin/accounts/groupnotification/" style="background: #28a745; color: white; padding: 10px 20px; border-radius: 4px; text-decoration: none; font-weight: bold;">
                <i class="fas fa-users me-2"></i>
                إدارة الإشعارات الجماعية 
            </a>
        </div>

        <!-- مؤشر عرض الكل -->
        {% if showing_all %}
        <div style="margin-top: 15px; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; color: #0c5460;">
            <i class="fas fa-info-circle me-2"></i>
            <strong>يتم عرض جميع الإشعارات ({{ notifications_stats.total }}) في صفحة واحدة</strong>
            <a href="?" style="margin-right: 10px; color: #0c5460;">العودة للعرض العادي</a>
        </div>
        {% endif %}
    </div>
    {% endif %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* تحسين مظهر الجدول */
        .result_list th, .result_list td {
            padding: 12px 8px;
            vertical-align: middle;
        }
        
        /* تحسين الأزرار */
        .button, input[type=submit], input[type=button], .submit-row input, a.button {
            transition: all 0.2s ease;
        }
        
        .button:hover, input[type=submit]:hover, input[type=button]:hover, .submit-row input:hover, a.button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* تحسين الفلاتر */
        #changelist-filter {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }
        
        #changelist-filter h2 {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }
        
        /* تحسين شريط البحث */
        #changelist-search {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
    </style>
{% endblock %}

{% block extrajs %}
    {{ block.super }}
    <script>
        function markAllAsRead() {
            if (confirm('هل أنت متأكد من تحديد جميع الإشعارات كمقروءة؟')) {
                // استخدام Django admin action
                const form = document.getElementById('changelist-form');
                const actionSelect = document.querySelector('select[name="action"]');
                
                // تحديد جميع الصفوف
                const checkboxes = document.querySelectorAll('input[name="_selected_action"]');
                checkboxes.forEach(cb => cb.checked = true);
                
                // تحديد الإجراء
                actionSelect.value = 'mark_all_as_read';
                
                // تنفيذ الإجراء
                form.submit();
            }
        }
        
        function deleteOldNotifications() {
            if (confirm('هل أنت متأكد من حذف الإشعارات القديمة (أكثر من 30 يوم)؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                // استخدام Django admin action
                const form = document.getElementById('changelist-form');
                const actionSelect = document.querySelector('select[name="action"]');
                
                // تحديد جميع الصفوف
                const checkboxes = document.querySelectorAll('input[name="_selected_action"]');
                checkboxes.forEach(cb => cb.checked = true);
                
                // تحديد الإجراء
                actionSelect.value = 'delete_old_notifications';
                
                // تنفيذ الإجراء
                form.submit();
            }
        }
        
        function deleteAllNotifications() {
            if (confirm('⚠️ تحذير: هل أنت متأكد من حذف جميع الإشعارات البسيطة؟\n\nسيتم حذف جميع الإشعارات ({{ notifications_stats.total }}) نهائياً.\nهذا الإجراء لا يمكن التراجع عنه!\n\nاكتب "نعم" للتأكيد:')) {
                const confirmation = prompt('اكتب "حذف جميع الإشعارات" للتأكيد النهائي:');
                if (confirmation === 'حذف جميع الإشعارات') {
                    // استخدام Django admin action
                    const form = document.getElementById('changelist-form');
                    const actionSelect = document.querySelector('select[name="action"]');

                    // تحديد جميع الصفوف
                    const checkboxes = document.querySelectorAll('input[name="_selected_action"]');
                    checkboxes.forEach(cb => cb.checked = true);

                    // تحديد الإجراء
                    actionSelect.value = 'delete_all_notifications';

                    // تنفيذ الإجراء
                    form.submit();
                } else {
                    alert('تم إلغاء العملية - النص المدخل غير صحيح');
                }
            }
        }

        function selectAllNotifications() {
            const checkboxes = document.querySelectorAll('input[name="_selected_action"]');
            const selectAllCheckbox = document.querySelector('#action-toggle');

            // تحديد جميع الصفوف
            checkboxes.forEach(cb => cb.checked = true);
            if (selectAllCheckbox) selectAllCheckbox.checked = true;

            // تحديث عداد المحدد
            const selectedCount = checkboxes.length;
            alert(`تم تحديد ${selectedCount} إشعار`);
        }

        function showAll() {
            // إضافة معامل لعرض جميع الإشعارات
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('all', '1');
            window.location.href = currentUrl.toString();
        }

        function refreshPage() {
            window.location.reload();
        }
        
        // تحديث تلقائي كل دقيقة
        setInterval(function() {
            // تحديث العدادات فقط بدون إعادة تحميل الصفحة
            fetch(window.location.href)
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newStats = doc.querySelector('.notifications-stats');
                    const currentStats = document.querySelector('.notifications-stats');
                    if (newStats && currentStats) {
                        currentStats.innerHTML = newStats.innerHTML;
                    }
                })
                .catch(error => console.log('خطأ في تحديث الإحصائيات:', error));
        }, 60000); // كل دقيقة
        
        // إضافة تأثيرات بصرية للصفوف
        document.addEventListener('DOMContentLoaded', function() {
            const rows = document.querySelectorAll('.result_list tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f8f9fa';
                    this.style.transform = 'scale(1.01)';
                    this.style.transition = 'all 0.2s ease';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
{% endblock %}
