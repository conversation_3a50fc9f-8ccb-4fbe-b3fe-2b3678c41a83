{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة الإشعارات{% endblock %}

{% block extra_css %}
<style>
    .notifications-page {
        padding: 20px 0;
    }
    
    .notification-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;
        padding: 20px;
        transition: all 0.3s ease;
        border-left: 4px solid #0d6efd;
        cursor: pointer;
    }
    
    .notification-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
    
    .notification-card.unread {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border-left-color: #0d6efd;
    }
    
    .notification-card.read {
        opacity: 0.8;
        border-left-color: #6c757d;
    }
    
    .notification-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .notification-icon {
        font-size: 24px;
        margin-right: 15px;
    }
    
    .notification-title {
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }
    
    .notification-meta {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }
    
    .meta-badge {
        background: #007bff;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .notification-time {
        color: #6c757d;
        font-size: 0.875rem;
        margin-bottom: 5px;
    }
    
    .no-notifications {
        text-align: center;
        padding: 40px;
        color: #6c757d;
        font-size: 1.1rem;
    }
    
    .filter-tabs .nav-link {
        color: #6c757d;
        border: none;
        border-bottom: 2px solid transparent;
        background: none;
        padding: 10px 20px;
        font-weight: 500;
    }
    
    .filter-tabs .nav-link.active {
        color: #0d6efd;
        border-bottom-color: #0d6efd;
        background: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid notifications-page">
    {% csrf_token %}
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>🔔 قائمة الإشعارات</h2>
                <div class="d-flex gap-2">
                    <span class="badge bg-danger">{{ unread_count }} غير مقروء</span>
                    <span class="badge bg-secondary">{{ total_count }} إجمالي</span>
                </div>
            </div>
            
            <!-- تبويبات الفلترة -->
            <ul class="nav nav-tabs filter-tabs" id="notificationTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                        جميع الإشعارات ({{ total_count }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="unread-tab" data-bs-toggle="tab" data-bs-target="#unread" type="button" role="tab">
                        غير مقروءة ({{ unread_count }})
                    </button>
                </li>
            </ul>
            
            <!-- محتوى التبويبات -->
            <div class="tab-content" id="notificationTabsContent">
                <!-- جميع الإشعارات -->
                <div class="tab-pane fade show active" id="all" role="tabpanel">
                    {% if notifications %}
                        {% for item in notifications %}
                        {% with notification=item.notification %}
                        <div class="notification-card group-notification {% if not item.is_read %}unread{% else %}read{% endif %} priority-{{ notification.priority }}"
                             data-notification-id="{{ notification.id }}">
                            <div class="notification-header">
                                <div class="d-flex align-items-center">
                                    <span class="notification-icon">
                                        <i class="{{ notification.get_icon }} text-primary"></i>
                                    </span>
                                    <div>
                                        <div class="notification-title">
                                            {{ notification.title }}
                                        </div>
                                        <div class="notification-meta">
                                            {% if notification.order_number %}
                                                <span class="meta-badge">{{ notification.order_number }}</span>
                                            {% endif %}
                                            {% if notification.customer_name %}
                                                <span class="meta-badge" style="background: #28a745;">{{ notification.customer_name }}</span>
                                            {% endif %}
                                            <span class="meta-badge" style="background: #6c757d;">{{ notification.get_priority_display }}</span>
                                            {% if is_admin %}
                                                <span class="meta-badge" style="background: #17a2b8;">
                                                    {{ item.read_count }}/{{ item.total_users }} قرأوا
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="notification-time">{{ notification.created_at|timesince }} مضت</div>
                                    <a href="{% url 'accounts:group_notification_detail' notification.id %}"
                                       class="btn btn-sm btn-outline-primary details-btn">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endwith %}
                        {% endfor %}
                    {% else %}
                        <div class="no-notifications">
                            📭 لا توجد إشعارات
                        </div>
                    {% endif %}
                </div>

                <!-- الإشعارات غير المقروءة -->
                <div class="tab-pane fade" id="unread" role="tabpanel">
                    {% if notifications %}
                        {% for item in notifications %}
                        {% if not item.is_read %}
                        {% with notification=item.notification %}
                        <div class="notification-card group-notification unread priority-{{ notification.priority }}"
                             data-notification-id="{{ notification.id }}">
                            <div class="notification-header">
                                <div class="d-flex align-items-center">
                                    <span class="notification-icon">
                                        <i class="{{ notification.get_icon }} text-warning"></i>
                                    </span>
                                    <div>
                                        <div class="notification-title">
                                            {{ notification.title }}
                                        </div>
                                        <div class="notification-meta">
                                            {% if notification.order_number %}
                                                <span class="meta-badge">{{ notification.order_number }}</span>
                                            {% endif %}
                                            {% if notification.customer_name %}
                                                <span class="meta-badge" style="background: #28a745;">{{ notification.customer_name }}</span>
                                            {% endif %}
                                            <span class="meta-badge" style="background: #6c757d;">{{ notification.get_priority_display }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="notification-time">{{ notification.created_at|timesince }} مضت</div>
                                    <a href="{% url 'accounts:group_notification_detail' notification.id %}"
                                       class="btn btn-sm btn-outline-warning details-btn">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endwith %}
                        {% endif %}
                        {% endfor %}
                    {% else %}
                        <div class="no-notifications">
                            ✅ جميع الإشعارات مقروءة
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// دالة لفتح الإشعار وتحديده كمقروء
function openGroupNotification(notificationId) {
    console.log('تم النقر على الإشعار:', notificationId);

    // منع أي أحداث أخرى
    event.stopPropagation();
    event.preventDefault();

    // تحديد الإشعار كمقروء أولاً
    markGroupNotificationAsRead(notificationId);

    // ثم التوجه لصفحة التفاصيل
    console.log('التوجه لصفحة التفاصيل...');
    window.location.href = `/accounts/notifications/group/${notificationId}/`;
}

// دالة لتحديد الإشعار الجماعي كمقروء
function markGroupNotificationAsRead(notificationId) {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    const headers = {
        'Content-Type': 'application/json',
    };

    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken.value;
    }

    fetch(`/accounts/notifications/group/${notificationId}/mark-read/`, {
        method: 'POST',
        headers: headers,
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث البطاقة لتظهر كمقروءة
            const card = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (card) {
                card.classList.remove('unread');
                card.classList.add('read');
            }
        }
    })
    .catch(error => {
        console.log('تم تحديد الإشعار محلياً');
    });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة، إضافة معالجات الأحداث...');

    // إضافة معالج النقر لجميع بطاقات الإشعارات الجماعية
    document.querySelectorAll('.notification-card.group-notification').forEach(card => {
        console.log('إضافة معالج نقر للبطاقة:', card.dataset.notificationId);

        // إضافة مؤشر بصري
        card.style.cursor = 'pointer';

        // معالج النقر
        card.addEventListener('click', function(event) {
            event.stopPropagation();
            event.preventDefault();

            const notificationId = this.dataset.notificationId;
            console.log('تم النقر على البطاقة:', notificationId);

            if (notificationId) {
                openGroupNotification(notificationId);
            }
        });

        // تأثيرات بصرية
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
        });
    });

    console.log('تم إضافة معالجات الأحداث لـ', document.querySelectorAll('.notification-card.group-notification').length, 'بطاقة');

    // منع النقر على الأزرار من تفعيل النقر على البطاقة
    document.querySelectorAll('.details-btn').forEach(btn => {
        btn.addEventListener('click', function(event) {
            event.stopPropagation();
            console.log('تم النقر على زر التفاصيل');
        });
    });
});
</script>
{% endblock %}
