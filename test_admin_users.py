#!/usr/bin/env python
"""
اختبار المستخدمين المديرين وكلمات المرور
"""

import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'homeupdate.settings')
django.setup()

def test_admin_users():
    """اختبار المستخدمين المديرين"""
    try:
        from django.contrib.auth import get_user_model
        
        print("=== اختبار المستخدمين المديرين ===")
        
        # الحصول على نموذج المستخدم الصحيح
        User = get_user_model()
        print(f"✅ نموذج المستخدم: {User.__name__}")
        print(f"✅ مسار النموذج: {User._meta.app_label}.{User._meta.model_name}")
        
        # البحث عن المستخدمين المديرين
        admin_users = User.objects.filter(is_superuser=True)
        print(f"\n📊 عدد المستخدمين المديرين: {admin_users.count()}")
        
        if admin_users.exists():
            print("\n👥 المستخدمين المديرين:")
            for i, user in enumerate(admin_users, 1):
                print(f"   {i}. {user.username} - {user.email}")
                print(f"      الاسم: {user.first_name} {user.last_name}")
                print(f"      نشط: {'نعم' if user.is_active else 'لا'}")
                print(f"      موظف: {'نعم' if user.is_staff else 'لا'}")
                print(f"      مدير عام: {'نعم' if hasattr(user, 'is_general_manager') and user.is_general_manager else 'لا'}")
                print()
            
            return admin_users
        else:
            print("❌ لا يوجد مستخدمين مديرين في النظام")
            
            # البحث عن مستخدمين موظفين
            staff_users = User.objects.filter(is_staff=True)
            print(f"📊 عدد المستخدمين الموظفين: {staff_users.count()}")
            
            if staff_users.exists():
                print("\n👥 المستخدمين الموظفين:")
                for i, user in enumerate(staff_users[:5], 1):  # أول 5 فقط
                    print(f"   {i}. {user.username} - {user.email}")
            
            return None
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المستخدمين المديرين: {str(e)}")
        return None

def test_password_verification():
    """اختبار التحقق من كلمة المرور"""
    try:
        from django.contrib.auth import get_user_model
        
        print("\n=== اختبار التحقق من كلمة المرور ===")
        
        User = get_user_model()
        admin_users = User.objects.filter(is_superuser=True)
        
        if not admin_users.exists():
            print("❌ لا يوجد مستخدمين مديرين للاختبار")
            return False
        
        # اختبار كلمات مرور شائعة
        common_passwords = [
            'admin', 'password', '123456', 'admin123', 
            'password123', 'root', 'administrator', '1234'
        ]
        
        print("🔍 اختبار كلمات مرور شائعة...")
        
        for user in admin_users:
            print(f"\n👤 اختبار المستخدم: {user.username}")
            
            for password in common_passwords:
                if user.check_password(password):
                    print(f"   ✅ كلمة المرور الصحيحة: {password}")
                    return True
                else:
                    print(f"   ❌ كلمة مرور خاطئة: {password}")
        
        print("\n⚠️ لم يتم العثور على كلمة مرور صحيحة من الكلمات الشائعة")
        print("💡 نصائح:")
        print("   1. تحقق من كلمة المرور الصحيحة للمستخدم المدير")
        print("   2. يمكنك إنشاء مستخدم مدير جديد:")
        print("      python manage.py createsuperuser")
        print("   3. أو تغيير كلمة مرور مستخدم موجود:")
        print("      python manage.py changepassword <username>")
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحقق من كلمة المرور: {str(e)}")
        return False

def test_create_test_admin():
    """إنشاء مستخدم مدير للاختبار"""
    try:
        from django.contrib.auth import get_user_model
        
        print("\n=== إنشاء مستخدم مدير للاختبار ===")
        
        User = get_user_model()
        
        # التحقق من وجود مستخدم اختبار
        test_username = 'test_admin'
        test_password = 'test123456'
        
        if User.objects.filter(username=test_username).exists():
            print(f"⚠️ المستخدم {test_username} موجود بالفعل")
            user = User.objects.get(username=test_username)
        else:
            # إنشاء مستخدم مدير للاختبار
            user = User.objects.create_user(
                username=test_username,
                email='<EMAIL>',
                password=test_password,
                is_staff=True,
                is_superuser=True,
                first_name='مدير',
                last_name='اختبار'
            )
            print(f"✅ تم إنشاء مستخدم مدير للاختبار: {test_username}")
        
        # اختبار كلمة المرور
        if user.check_password(test_password):
            print(f"✅ كلمة المرور صحيحة: {test_password}")
            print(f"💡 يمكنك استخدام هذه البيانات في المزامنة العكسية:")
            print(f"   اسم المستخدم: {test_username}")
            print(f"   كلمة المرور: {test_password}")
            return True
        else:
            print(f"❌ كلمة المرور غير صحيحة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء مستخدم مدير للاختبار: {str(e)}")
        return False

def main():
    print("🔄 بدء اختبار المستخدمين المديرين...")
    print("="*60)
    
    # اختبار المستخدمين المديرين
    admin_users = test_admin_users()
    
    # اختبار التحقق من كلمة المرور
    password_test = test_password_verification()
    
    # إنشاء مستخدم مدير للاختبار إذا لم توجد كلمة مرور صحيحة
    if not password_test:
        test_admin_created = test_create_test_admin()
    else:
        test_admin_created = True
    
    # النتيجة النهائية
    print("\n" + "="*60)
    print("📊 ملخص نتائج الاختبار:")
    print("="*60)
    
    if admin_users and (password_test or test_admin_created):
        print("🎉 تم العثور على مستخدمين مديرين!")
        print("\n✅ النتائج:")
        if password_test:
            print("   ✅ تم العثور على كلمة مرور صحيحة")
        elif test_admin_created:
            print("   ✅ تم إنشاء مستخدم مدير للاختبار")
            print("   📝 بيانات الاختبار:")
            print("      اسم المستخدم: test_admin")
            print("      كلمة المرور: test123456")
        
        print("\n🚀 يمكنك الآن استخدام المزامنة العكسية!")
        print("   الرابط: /odoo-db-manager/google-sync/")
        print("   القسم: 'المزامنة العكسية' في أسفل الصفحة")
    else:
        print("⚠️ مشاكل في المستخدمين المديرين")
        print("\n💡 الحلول المقترحة:")
        print("   1. إنشاء مستخدم مدير جديد:")
        print("      python manage.py createsuperuser")
        print("   2. تغيير كلمة مرور مستخدم موجود:")
        print("      python manage.py changepassword <username>")

if __name__ == "__main__":
    main()
