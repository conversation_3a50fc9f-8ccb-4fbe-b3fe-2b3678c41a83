# ميزة رفع المنتجات بالجملة

تم إضافة ميزة جديدة لرفع المنتجات بكمياتها حسب تفاصيل المخزن دفعة واحدة عن طريق ملف إكسل.

## الميزات المضافة

### 1. رفع المنتجات بالجملة
- إضافة منتجات جديدة من ملف إكسل
- تحديث المنتجات الموجودة
- إضافة الكميات الابتدائية للمخزون
- إنشاء فئات جديدة تلقائياً

### 2. تحديث المخزون بالجملة
- تحديث كميات المنتجات الموجودة
- ثلاثة أنماط للتحديث: استبدال، إضافة، خصم
- تسجيل حركات المخزون تلقائياً

### 3. حقل الوحدة للمنتجات
- إضافة حقل الوحدة لكل منتج (قطعة، كيلوجرام، لتر، إلخ)
- عرض الوحدة في جداول المنتجات والتقارير

## كيفية الاستخدام

### رفع المنتجات الجديدة

1. انتقل إلى قائمة المنتجات
2. اضغط على "رفع بالجملة" > "إضافة منتجات جديدة"
3. قم بتحميل قالب إكسل من الرابط المخصص
4. املأ البيانات التالية:
   - **اسم المنتج** (مطلوب)
   - **الكود** (اختياري - سيتم توليده تلقائياً)
   - **الفئة** (سيتم إنشاؤها إذا لم تكن موجودة)
   - **السعر** (مطلوب)
   - **الكمية** (الكمية الابتدائية في المخزون)
   - **الوصف** (اختياري)
   - **الحد الأدنى** (للتنبيهات)
   - **العملة** (EGP, USD, EUR)
   - **الوحدة** (قطعة، كيلوجرام، إلخ)

5. اختر المستودع المناسب
6. اختر ما إذا كنت تريد استبدال المنتجات الموجودة
7. ارفع الملف واضغط "رفع وإضافة المنتجات"

### تحديث المخزون بالجملة

1. انتقل إلى "رفع بالجملة" > "تحديث كميات المخزون"
2. قم بإعداد ملف إكسل بعمودين:
   - **كود المنتج** (مطلوب)
   - **الكمية** (الكمية الجديدة أو المراد إضافتها/خصمها)
   - **ملاحظات** (اختياري)

3. اختر نوع التحديث:
   - **استبدال**: تعديل الكمية إلى القيمة المحددة
   - **إضافة**: زيادة الكمية الحالية
   - **خصم**: تقليل الكمية الحالية

4. أدخل سبب التحديث (جرد، تصحيح، إلخ)
5. ارفع الملف واضغط "تحديث المخزون"

## الملفات المضافة

```
inventory/
├── forms.py                           # نماذج رفع الملفات
├── views_bulk.py                      # معالجة العمليات المجمعة
├── templates/inventory/
│   ├── product_bulk_upload.html       # واجهة رفع المنتجات
│   └── bulk_stock_update.html         # واجهة تحديث المخزون
└── migrations/
    └── 0002_product_unit.py           # إضافة حقل الوحدة
```

## الروابط الجديدة

```python
# في inventory/urls.py
path('products/bulk-upload/', product_bulk_upload, name='product_bulk_upload'),
path('stock/bulk-update/', bulk_stock_update, name='bulk_stock_update'),
path('download-excel-template/', download_excel_template, name='download_excel_template'),
```

## المكتبات المستخدمة

- **pandas**: لقراءة ومعالجة ملفات إكسل
- **openpyxl**: لإنشاء وقراءة ملفات .xlsx
- **xlrd**: لقراءة ملفات .xls القديمة

## التحسينات المضافة

1. **التحقق من صحة البيانات**: فحص نوع الملف وحجمه والأعمدة المطلوبة
2. **معالجة الأخطاء**: عرض الأخطاء بوضوح مع رقم الصف
3. **الأمان**: تقييد أنواع الملفات وأحجامها
4. **الأداء**: استخدام المعاملات (transactions) لضمان سلامة البيانات
5. **ذاكرة التخزين المؤقت**: تحديث الكاش تلقائياً بعد التغييرات

## أمثلة على الاستخدام

### مثال 1: إضافة منتجات جديدة

| اسم المنتج | الكود | الفئة | السعر | الكمية | الوصف | الحد الأدنى | العملة | الوحدة |
|------------|-------|-------|--------|---------|--------|----------|---------|---------|
| لابتوب HP | LAP001 | أجهزة كمبيوتر | 15000 | 10 | لابتوب HP بروسيسور i5 | 5 | EGP | قطعة |
| ماوس لاسلكي | MOU001 | إكسسوارات | 150 | 50 | ماوس لاسلكي أزرق | 10 | EGP | قطعة |

### مثال 2: تحديث المخزون

| كود المنتج | الكمية | ملاحظات |
|------------|---------|----------|
| LAP001 | 25 | تحديث بعد الجرد |
| MOU001 | 30 | إضافة شحنة جديدة |

## ملاحظات مهمة

- **النسخ الاحتياطية**: يُنصح بأخذ نسخة احتياطية قبل العمليات المجمعة الكبيرة
- **الحد الأقصى**: حجم الملف محدود بـ 10 ميجابايت للمنتجات و5 ميجابايت لتحديث المخزون
- **التحقق**: يتم فحص البيانات قبل الحفظ وعرض الأخطاء إن وجدت
- **التتبع**: جميع العمليات يتم تسجيلها في حركات المخزون مع المستخدم والوقت

## الدعم والصيانة

للمزيد من المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
