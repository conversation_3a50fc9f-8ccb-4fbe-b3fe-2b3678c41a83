{% extends 'inventory/inventory_base_custom.html' %}
{% load static %}

{% block inventory_title %}تقرير المخزون المنخفض{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">لوحة التحكم</a></li>
<li class="breadcrumb-item"><a href="{% url 'inventory:report_list' %}">التقارير</a></li>
<li class="breadcrumb-item active" aria-current="page">تقرير المخزون المنخفض</li>
{% endblock %}

{% block quick_actions %}
<a href="{% url 'inventory:report_list' %}" class="btn btn-secondary btn-sm">
    <i class="fas fa-arrow-left"></i> العودة للتقارير
</a>
<a href="{% url 'inventory:stock_movement_report' %}" class="btn btn-primary btn-sm">
    <i class="fas fa-exchange-alt"></i> تقرير حركة المخزون
</a>
{% endblock %}

{% block inventory_content %}
<div class="low-stock-report-container">
    <!-- إحصائيات سريعة -->
    <div class="stats-cards-container">
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">منتجات منخفضة المخزون</div>
                <div class="stat-card-value">{{ low_stock_products|length }}</div>
                <div class="stat-card-change negative">
                    <i class="fas fa-arrow-up"></i> 8% منذ الأسبوع الماضي
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">منتجات نفذت من المخزون</div>
                <div class="stat-card-value">{{ out_of_stock_products|length }}</div>
                <div class="stat-card-change negative">
                    <i class="fas fa-arrow-up"></i> 12% منذ الأسبوع الماضي
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">نسبة المنتجات المنخفضة</div>
                <div class="stat-card-value">{{ low_stock_percentage|floatformat:1 }}%</div>
                <div class="stat-card-change negative">
                    <i class="fas fa-arrow-up"></i> 3% منذ الشهر الماضي
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">طلبات شراء معلقة</div>
                <div class="stat-card-value">{{ pending_purchase_orders }}</div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-down"></i> 5% منذ الأسبوع الماضي
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="filters-container mb-4">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="اسم المنتج، الكود، الوصف...">
                    </div>
                    <div class="col-md-4">
                        <label for="category" class="form-label">الفئة</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">جميع الفئات</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="sort" class="form-label">ترتيب</label>
                        <select class="form-select" id="sort" name="sort">
                            <option value="stock" {% if sort_by == 'stock' %}selected{% endif %}>المخزون (تصاعدي)</option>
                            <option value="-stock" {% if sort_by == '-stock' %}selected{% endif %}>المخزون (تنازلي)</option>
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>الاسم (أ-ي)</option>
                            <option value="-name" {% if sort_by == '-name' %}selected{% endif %}>الاسم (ي-أ)</option>
                            <option value="category" {% if sort_by == 'category' %}selected{% endif %}>الفئة</option>
                        </select>
                    </div>
                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="{% url 'inventory:low_stock_report' %}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                        <a href="#" class="btn btn-success float-end" id="exportBtn">
                            <i class="fas fa-file-excel"></i> تصدير إلى Excel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- المنتجات منخفضة المخزون -->
    <div class="data-table-container">
        <div class="data-table-header">
            <h4 class="data-table-title">
                المنتجات منخفضة المخزون
                {% if low_stock_products %}
                <span class="badge bg-primary">{{ low_stock_products|length }}</span>
                {% endif %}
            </h4>
            <div class="data-table-actions">
                <a href="{% url 'inventory:product_list' %}?filter=low_stock" class="btn btn-primary btn-sm">
                    <i class="fas fa-list"></i> عرض في قائمة المنتجات
                </a>
            </div>
        </div>
        <div class="data-table-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكود</th>
                            <th>الفئة</th>
                            <th>المخزون الحالي</th>
                            <th>الحد الأدنى</th>
                            <th>نسبة المخزون</th>
                            <th>السعر</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in low_stock_products %}
                        <tr>
                            <td>{{ product.name }}</td>
                            <td>{{ product.code }}</td>
                            <td>{{ product.category }}</td>
                            <td>
                                <span class="badge bg-warning">{{ product.current_stock_calc }}</span>
                            </td>
                            <td>{{ product.minimum_stock }}</td>
                            <td>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: {{ product.stock_percentage }}%;" aria-valuenow="{{ product.stock_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <small>{{ product.stock_percentage }}%</small>
                            </td>
                            <td>{{ product.price }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'inventory:product_detail' product.id %}" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'inventory:transaction_create' product.id %}" class="btn btn-success btn-sm" title="إضافة مخزون">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                    <a href="#" class="btn btn-primary btn-sm create-purchase-order" data-product-id="{{ product.id }}" title="إنشاء طلب شراء">
                                        <i class="fas fa-shopping-cart"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">لا توجد منتجات منخفضة المخزون</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- المنتجات التي نفذت من المخزون -->
    <div class="data-table-container mt-4">
        <div class="data-table-header">
            <h4 class="data-table-title">
                المنتجات التي نفذت من المخزون
                {% if out_of_stock_products %}
                <span class="badge bg-primary">{{ out_of_stock_products|length }}</span>
                {% endif %}
            </h4>
            <div class="data-table-actions">
                <a href="{% url 'inventory:product_list' %}?filter=out_of_stock" class="btn btn-primary btn-sm">
                    <i class="fas fa-list"></i> عرض في قائمة المنتجات
                </a>
            </div>
        </div>
        <div class="data-table-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكود</th>
                            <th>الفئة</th>
                            <th>المخزون الحالي</th>
                            <th>الحد الأدنى</th>
                            <th>السعر</th>
                            <th>آخر حركة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in out_of_stock_products %}
                        <tr>
                            <td>{{ product.name }}</td>
                            <td>{{ product.code }}</td>
                            <td>{{ product.category }}</td>
                            <td>
                                <span class="badge bg-danger">{{ product.current_stock_calc }}</span>
                            </td>
                            <td>{{ product.minimum_stock }}</td>
                            <td>{{ product.price }}</td>
                            <td>{{ product.last_transaction_date|date:"Y-m-d"|default:"-" }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'inventory:product_detail' product.id %}" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'inventory:transaction_create' product.id %}" class="btn btn-success btn-sm" title="إضافة مخزون">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                    <a href="#" class="btn btn-primary btn-sm create-purchase-order" data-product-id="{{ product.id }}" title="إنشاء طلب شراء">
                                        <i class="fas fa-shopping-cart"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">لا توجد منتجات نفذت من المخزون</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
    // السكربت التالي يعتمد الآن على التهيئة العالمية لـ DataTables الموجودة في data-tables-config.js
    document.addEventListener('DOMContentLoaded', function() {
        // أزرار إنشاء طلب شراء
        document.querySelectorAll('.create-purchase-order').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.getAttribute('data-product-id');
                Swal.fire({
                    title: 'إنشاء طلب شراء',
                    text: 'سيتم إنشاء طلب شراء للمنتج رقم ' + productId,
                    icon: 'info',
                    showCancelButton: true,
                    confirmButtonText: 'متابعة',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // TODO: استدعاء API لإنشاء طلب شراء
                        console.log('Creating purchase order for product', productId);
                    }
                });
            });
        });
    });
</script>
{% endblock %}
