{% extends 'inventory/inventory_base_custom.html' %}
{% load static %}

{% block inventory_title %}المستودعات{% endblock %}

{% block extra_css %}
{{ block.super }}
<!-- تم إزالة CSS القديم للـ Modal واستبداله بـ SweetAlert2 -->
{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">لوحة التحكم</a></li>
<li class="breadcrumb-item active" aria-current="page">المستودعات</li>
{% endblock %}

{% block quick_actions %}
<button type="button" class="btn btn-success btn-sm" onclick="openAddWarehouseModal()">
    <i class="fas fa-plus"></i> إضافة مستودع
</button>
<a href="{% url 'inventory:warehouse_location_list' %}" class="btn btn-primary btn-sm">
    <i class="fas fa-map-marker-alt"></i> مواقع التخزين
</a>
<button type="button" class="btn btn-warning btn-sm" onclick="testDeleteButton()">
    <i class="fas fa-test"></i> اختبار الحذف
</button>
{% endblock %}

{% block inventory_content %}
{% csrf_token %}
<div class="warehouse-list-container">
    <!-- قائمة المستودعات -->
    <div class="data-table-container">
        <div class="data-table-header">
            <h4 class="data-table-title">
                المستودعات
                {% if warehouses %}
                <span class="badge bg-primary">{{ warehouses|length }}</span>
                {% endif %}
            </h4>
            <div class="data-table-actions">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary active" data-filter="all">الكل</button>
                    <button type="button" class="btn btn-outline-success" data-filter="active">نشط</button>
                    <button type="button" class="btn btn-outline-danger" data-filter="inactive">غير نشط</button>
                </div>
            </div>
        </div>
        <div class="data-table-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>اسم المستودع</th>
                            <th>الرمز</th>
                            <th>الفرع</th>
                            <th>المدير</th>
                            <th>العنوان</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>تم الإنشاء بواسطة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for warehouse in warehouses %}
                        <tr class="warehouse-row {% if not warehouse.is_active %}inactive{% else %}active{% endif %}">
                            <td>
                                <strong>{{ warehouse.name }}</strong>
                            </td>
                            <td>{{ warehouse.code }}</td>
                            <td>{% if warehouse.branch %}{{ warehouse.branch.name }}{% else %}<span class="text-muted">جميع الفروع</span>{% endif %}</td>
                            <td>{{ warehouse.manager.get_full_name|default:"-" }}</td>
                            <td>{{ warehouse.address|default:"-" }}</td>
                            <td>
                                {% if warehouse.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ warehouse.created_date_display }}</small>
                            </td>
                            <td>
                                <small>{{ warehouse.created_by_name }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'inventory:warehouse_update' warehouse.id %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-danger btn-sm delete-warehouse-btn" 
                                            data-url="{% url 'inventory:warehouse_delete' warehouse.id %}" 
                                            data-name="{{ warehouse.name|escapejs }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <a href="{% url 'inventory:warehouse_detail' warehouse.id %}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center">لا توجد مستودعات مضافة</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- إحصائيات المستودعات -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="chart-container">
                <div class="chart-header">
                    <h4 class="chart-title">توزيع المنتجات حسب المستودعات</h4>
                </div>
                <div class="chart-body">
                    <canvas id="warehouseProductsChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <div class="chart-header">
                    <h4 class="chart-title">حالة المستودعات</h4>
                </div>
                <div class="chart-body">
                    <canvas id="warehouseStatusChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

{% include 'inventory/sweetalert2_utils.html' %}
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('SweetAlert2 loaded successfully');
        
        // تم استبدال معالجة أزرار الحذف بـ دالة confirmDelete من SweetAlert2
        
        // معالجة أزرار حذف المستودع
        const deleteButtons = document.querySelectorAll('.delete-warehouse-btn');
        deleteButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const url = this.getAttribute('data-url');
                const name = this.getAttribute('data-name');
                confirmDelete(url, name);
            });
        });
        
        // فلترة المستودعات
        const filterButtons = document.querySelectorAll('[data-filter]');
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                const rows = document.querySelectorAll('.warehouse-row');
                
                // تحديث حالة الأزرار
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // تطبيق الفلتر
                rows.forEach(row => {
                    if (filter === 'all') {
                        row.style.display = 'table-row';
                    } else if (filter === 'active' && row.classList.contains('active')) {
                        row.style.display = 'table-row';
                    } else if (filter === 'inactive' && row.classList.contains('inactive')) {
                        row.style.display = 'table-row';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        });
        
        // رسم بياني لتوزيع المنتجات حسب المستودعات
        const productsCtx = document.getElementById('warehouseProductsChart');
        if (productsCtx) {
            try {
                const warehouseProductsChart = new Chart(productsCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: [{% for warehouse in warehouses %}'{{ warehouse.name|escapejs }}',{% endfor %}],
                        datasets: [{
                            label: 'Products Count',
                            data: [{% for warehouse in warehouses %}{{ warehouse.product_count|default:0 }},{% endfor %}],
                            backgroundColor: '#4e73df',
                            borderColor: '#4e73df',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            } catch (chartError) {
                console.error('خطأ في رسم الجدول البياني للمنتجات:', chartError);
            }
        }
        
        // رسم بياني لحالة المستودعات
        const statusCtx = document.getElementById('warehouseStatusChart');
        if (statusCtx) {
            try {
                const warehouseStatusChart = new Chart(statusCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: ['Active', 'Inactive'],
                        datasets: [{
                            data: [
                                {{ active_warehouses_count|default:0 }},
                                {{ inactive_warehouses_count|default:0 }}
                            ],
                            backgroundColor: ['#1cc88a', '#e74a3b'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right'
                            }
                        }
                    }
                });
            } catch (chartError) {
                console.error('خطأ في رسم الجدول البياني للحالة:', chartError);
            }
        }
    });
</script>
{% endblock %}
