{% extends 'inventory/inventory_base.html' %}
{% load static %}

{% block inventory_title %}قائمة المنتجات{% endblock %}

{% block inventory_content %}
<div class="product-list-container">
    <!-- فلاتر البحث -->
    <div class="filters-container">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="اسم المنتج، الكود، الوصف...">
                    </div>
                    <div class="col-md-3">
                        <label for="category" class="form-label">الفئة</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">جميع الفئات</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="filter" class="form-label">فلتر</label>
                        <select class="form-select" id="filter" name="filter">
                            <option value="">بدون فلتر</option>
                            <option value="low_stock" {% if selected_filter == 'low_stock' %}selected{% endif %}>مخزون منخفض</option>
                            <option value="out_of_stock" {% if selected_filter == 'out_of_stock' %}selected{% endif %}>نفذ من المخزون</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="sort" class="form-label">ترتيب</label>
                        <select class="form-select" id="sort" name="sort">
                            <option value="-created_at" {% if sort_by == '-created_at' %}selected{% endif %}>الأحدث</option>
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>الاسم (أ-ي)</option>
                            <option value="-name" {% if sort_by == '-name' %}selected{% endif %}>الاسم (ي-أ)</option>
                            <option value="price" {% if sort_by == 'price' %}selected{% endif %}>السعر (تصاعدي)</option>
                            <option value="-price" {% if sort_by == '-price' %}selected{% endif %}>السعر (تنازلي)</option>
                        </select>
                    </div>
                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="{% url 'inventory:product_list' %}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- قائمة المنتجات -->
    <div class="products-container mt-4">
        <div class="data-table">
            <div class="data-table-header">
                <h4 class="data-table-title">
                    المنتجات
                    {% if page_obj.paginator.count %}
                    <span class="badge bg-primary">{{ page_obj.paginator.count }}</span>
                    {% endif %}
                </h4>
                <div class="data-table-actions">
                    <div class="btn-group" role="group">
                        <a href="{% url 'inventory:product_create' %}" class="btn btn-success">
                            <i class="fas fa-plus"></i> إضافة منتج
                        </a>
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-upload"></i> رفع بالجملة
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{% url 'inventory:product_bulk_upload' %}">
                                    <i class="fas fa-plus-circle text-success"></i> إضافة منتجات جديدة
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'inventory:bulk_stock_update' %}">
                                    <i class="fas fa-sync-alt text-warning"></i> تحديث كميات المخزون
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{% url 'inventory:download_excel_template' %}">
                                    <i class="fas fa-download text-info"></i> تحميل قالب إكسل
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="data-table-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الكود</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>المخزون</th>
                                <th>الحد الأدنى</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in page_obj %}
                            <tr>
                                <td>{{ product.name }}</td>
                                <td>{{ product.code }}</td>
                                <td>{{ product.category }}</td>
                                <td>{{ product.price }}</td>
                                <td>
                                    {% if product.current_stock_calc <= 0 %}
                                    <span class="badge bg-danger">نفذ من المخزون</span>
                                    {% elif product.current_stock_calc <= product.minimum_stock %}
                                    <span class="badge bg-warning">{{ product.current_stock_calc }}</span>
                                    {% else %}
                                    <span class="badge bg-success">{{ product.current_stock_calc }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ product.minimum_stock }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'inventory:product_detail' product.id %}" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'inventory:product_update' product.id %}" class="btn btn-primary btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'inventory:transaction_create' product.id %}" class="btn btn-success btn-sm" title="إضافة حركة">
                                            <i class="fas fa-exchange-alt"></i>
                                        </a>
                                        <a href="{% url 'inventory:product_delete' product.id %}" class="btn btn-danger btn-sm" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="text-center">لا توجد منتجات مطابقة للبحث</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- الصفحات -->
            {% if page_obj.has_other_pages %}
            <div class="data-table-footer">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_filter %}&filter={{ selected_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_filter %}&filter={{ selected_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for i in page_obj.paginator.page_range %}
                            {% if page_obj.number == i %}
                            <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                            {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                            <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_filter %}&filter={{ selected_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">{{ i }}</a></li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_filter %}&filter={{ selected_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_filter %}&filter={{ selected_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
