{% extends "base.html" %}
{% load static %}

{% block title %}{% if product %}تعديل المنتج{% else %}إضافة منتج جديد{% endif %}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title mb-0">
                        {% if product %}تعديل المنتج: {{ product.name }}{% else %}إضافة منتج جديد{% endif %}
                    </h2>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">اسم المنتج*</label>
                                <input type="text" id="name" name="name" class="form-control" required
                                    value="{{ product.name|default:'' }}">
                            </div>
                            <div class="col-md-6">
                                <label for="code" class="form-label">كود المنتج</label>
                                <input type="text" id="code" name="code" class="form-control"
                                    value="{{ product.code|default:'' }}">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="category" class="form-label">الصنف*</label>
                                <select id="category" name="category" class="form-select" required>
                                    <option value="">اختر الصنف...</option>
                                    {% for category in categories %}
                                        <option value="{{ category.id }}" {% if product.category_id == category.id %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="price" class="form-label">السعر*</label>
                                <input type="number" id="price" name="price" class="form-control" required
                                    min="0" step="0.01" value="{{ product.price|default:'' }}">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea id="description" name="description" class="form-control" rows="3">{{ product.description|default:'' }}</textarea>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="minimum_stock" class="form-label">الحد الأدنى للمخزون*</label>
                                <input type="number" id="minimum_stock" name="minimum_stock" class="form-control" required
                                    min="0" value="{{ product.minimum_stock|default:'' }}">
                            </div>
                            <div class="col-md-4">
                                <label for="currency" class="form-label">العملة</label>
                                <select id="currency" name="currency" class="form-select">
                                    <option value="EGP" {% if product.currency == "EGP" %}selected{% endif %}>جنيه مصري</option>
                                    <option value="USD" {% if product.currency == "USD" %}selected{% endif %}>دولار أمريكي</option>
                                    <option value="EUR" {% if product.currency == "EUR" %}selected{% endif %}>يورو</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="unit" class="form-label">الوحدة</label>
                                <select id="unit" name="unit" class="form-select">
                                    <option value="piece" {% if product.unit == "piece" %}selected{% endif %}>قطعة</option>
                                    <option value="kg" {% if product.unit == "kg" %}selected{% endif %}>كيلوجرام</option>
                                    <option value="gram" {% if product.unit == "gram" %}selected{% endif %}>جرام</option>
                                    <option value="liter" {% if product.unit == "liter" %}selected{% endif %}>لتر</option>
                                    <option value="meter" {% if product.unit == "meter" %}selected{% endif %}>متر</option>
                                    <option value="box" {% if product.unit == "box" %}selected{% endif %}>علبة</option>
                                    <option value="pack" {% if product.unit == "pack" %}selected{% endif %}>عبوة</option>
                                    <option value="dozen" {% if product.unit == "dozen" %}selected{% endif %}>دستة</option>
                                    <option value="roll" {% if product.unit == "roll" %}selected{% endif %}>لفة</option>
                                    <option value="sheet" {% if product.unit == "sheet" %}selected{% endif %}>ورقة</option>
                                </select>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if product %}حفظ التغييرات{% else %}إضافة المنتج{% endif %}
                            </button>
                            <a href="{% url 'inventory:product_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('is-invalid');
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            event.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });

    // Real-time validation
    const requiredInputs = form.querySelectorAll('input[required], select[required]');
    requiredInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (!this.value.trim()) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        });
    });
});
</script>
{% endblock %}
