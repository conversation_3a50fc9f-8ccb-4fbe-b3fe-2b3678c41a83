{% extends 'inventory/inventory_base_custom.html' %}
{% load static %}

{% block inventory_title %}
{% if warehouse %}تعديل المستودع: {{ warehouse.name }}{% else %}إضافة مستودع جديد{% endif %}
{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">لوحة التحكم</a></li>
<li class="breadcrumb-item"><a href="{% url 'inventory:warehouse_list' %}">المستودعات</a></li>
<li class="breadcrumb-item active" aria-current="page">
    {% if warehouse %}تعديل المستودع{% else %}إضافة مستودع{% endif %}
</li>
{% endblock %}

{% block quick_actions %}
<a href="{% url 'inventory:warehouse_list' %}" class="btn btn-secondary btn-sm">
    <i class="fas fa-arrow-left"></i> العودة للقائمة
</a>
{% endblock %}

{% block inventory_content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-warehouse"></i>
                    {% if warehouse %}تعديل المستودع: {{ warehouse.name }}{% else %}إضافة مستودع جديد{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم المستودع <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{% if warehouse %}{{ warehouse.name }}{% endif %}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="code" class="form-label">رمز المستودع <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="code" name="code" 
                                   value="{% if warehouse %}{{ warehouse.code }}{% endif %}" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="branch" class="form-label">الفرع</label>
                            <select class="form-select" id="branch" name="branch">
                                <option value="">اختر الفرع (اختياري)</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}" 
                                    {% if warehouse and warehouse.branch_id == branch.id %}selected{% endif %}>
                                    {{ branch.name }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">يمكن ترك هذا الحقل فارغاً إذا كان المستودع يخدم جميع الفروع</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="manager" class="form-label">مدير المستودع</label>
                            <select class="form-select" id="manager" name="manager">
                                <option value="">اختر المدير (اختياري)</option>
                                {% for user in users %}
                                <option value="{{ user.id }}" 
                                    {% if warehouse and warehouse.manager_id == user.id %}selected{% endif %}>
                                    {{ user.get_full_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3">{% if warehouse %}{{ warehouse.address }}{% endif %}</textarea>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{% if warehouse %}{{ warehouse.notes }}{% endif %}</textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                   {% if not warehouse or warehouse.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">نشط</label>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'inventory:warehouse_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            {% if warehouse %}حفظ التغييرات{% else %}إضافة المستودع{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li class="mb-2">اسم المستودع ورمزه مطلوبان</li>
                    <li class="mb-2">رمز المستودع يجب أن يكون فريداً</li>
                    <li class="mb-2">الفرع اختياري - يمكن ترك المستودع يخدم جميع الفروع</li>
                    <li class="mb-2">مدير المستودع اختياري</li>
                    <li>يمكن تعطيل المستودع مؤقتاً بإلغاء تحديد "نشط"</li>
                </ul>
            </div>
        </div>

        {% if warehouse %}
        <div class="card mt-3">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar"></i>
                    إحصائيات المستودع
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">0</h4>
                        <small>المنتجات</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">0</h4>
                        <small>المواقع</small>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        الإحصائيات ستظهر عند ربط المنتجات بالمستودع
                    </small>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    
    form.addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const code = document.getElementById('code').value.trim();
        
        if (!name || !code) {
            e.preventDefault();
            alert('يرجى إدخال اسم المستودع والرمز');
            return;
        }
        
        // تأكيد التحديث
        {% if warehouse %}
        const confirmed = confirm('هل أنت متأكد من حفظ التغييرات؟');
        if (!confirmed) {
            e.preventDefault();
        }
        {% endif %}
    });
});
</script>
{% endblock %}
