{% extends 'base.html' %}
{% load math_filters_inventory %}

{% block title %}إدارة المخزون - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">إدارة المخزون</h2>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <a href="{% url 'inventory:product_create' %}" class="btn" style="background-color: var(--primary); color: white;">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </a>
                <button type="button" class="btn dropdown-toggle" style="background-color: var(--secondary); color: white;" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-upload"></i> رفع بالجملة
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item" href="{% url 'inventory:product_bulk_upload' %}">
                            <i class="fas fa-plus-circle text-success"></i> إضافة منتجات جديدة
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{% url 'inventory:bulk_stock_update' %}">
                            <i class="fas fa-sync-alt text-warning"></i> تحديث كميات المخزون
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="{% url 'inventory:download_excel_template' %}">
                            <i class="fas fa-download text-info"></i> تحميل قالب إكسل
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Inventory Stats -->
    <div class="row mb-4">
        <div class="col-md-4 mb-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-boxes fa-3x mb-3" style="color: var(--primary);"></i>
                    <h5 class="card-title">إجمالي المنتجات</h5>
                    <p class="card-text display-6">{{ total_products }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3" style="color: var(--alert);"></i>
                    <h5 class="card-title">منتجات منخفضة</h5>
                    <p class="card-text display-6">{{ low_stock_count }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-truck fa-3x mb-3" style="color: var(--light-accent);"></i>
                    <h5 class="card-title">طلبات التوريد</h5>
                    <p class="card-text display-6">{{ purchase_orders_count }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4" style="border-color: var(--neutral);">
        <div class="card-body">
            <form method="get" action="{% url 'inventory:inventory_list' %}">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="البحث عن منتج..." value="{{ search_query }}">
                            <button type="submit" class="btn" style="background-color: var(--primary); color: white;">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2 mb-2">
                        <label for="page_size" class="form-label">عدد الصفوف:</label>
                        <select name="page_size" id="page_size" class="form-select">
                            <option value="10" {% if page_size|stringformat:'s' == '10' %}selected{% endif %}>10</option>
                            <option value="20" {% if page_size|stringformat:'s' == '20' or not page_size %}selected{% endif %}>20</option>
                            <option value="50" {% if page_size|stringformat:'s' == '50' %}selected{% endif %}>50</option>
                            <option value="100" {% if page_size|stringformat:'s' == '100' %}selected{% endif %}>100</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <select name="category" class="form-select">
                            <option value="">جميع الفئات</option>
                            {% for category in categories %}
                                <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"i" %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="in_stock" {% if status_filter == 'in_stock' %}selected{% endif %}>متوفر</option>
                            <option value="low_stock" {% if status_filter == 'low_stock' %}selected{% endif %}>منخفض</option>
                            <option value="out_of_stock" {% if status_filter == 'out_of_stock' %}selected{% endif %}>غير متوفر</option>
                        </select>
                    </div>
                    <div class="col-md-1 mb-2">
                        <button type="submit" class="btn w-100" style="background-color: var(--primary); color: white;">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Products List -->
    <div class="card" style="border-color: var(--neutral);">
        <div class="card-header" style="background-color: var(--primary); color: white;">
            <h5 class="mb-0"><i class="fas fa-boxes"></i> قائمة المنتجات</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>اسم المنتج</th>
                            <th>الفئة</th>
                            <th>المخزون الحالي</th>
                            <th>الحد الأدنى</th>
                            <th>سعر الوحدة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in page_obj %}
                        <tr>
                            <td>{{ product.code }}</td>
                            <td>{{ product.name }}</td>
                            <td>{% if product.category %}{{ product.category.name }}{% else %}-{% endif %}</td>
                            <td>{{ product.current_stock }}</td>
                            <td>{{ product.minimum_stock }}</td>
                            <td>{{ product.price }} {{ currency_symbol }}</td>
                            <td>
                                {% if product.current_stock <= 0 %}
                                    <span class="badge bg-secondary">غير متوفر</span>
                                {% elif product.needs_restock %}
                                    <span class="badge" style="background-color: var(--alert); color: white;">منخفض</span>
                                {% else %}
                                    <span class="badge bg-success">متوفر</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm action-buttons">
                                    <a href="{% url 'inventory:product_detail' product.pk %}" class="btn btn-info" title="عرض" style="font-size: 0.7em; padding: 0.15rem 0.3rem;"><i class="fas fa-eye" style="font-size: 0.7em;"></i></a>
                                    <a href="{% url 'inventory:product_update' product.pk %}" class="btn btn-primary" title="تعديل" style="font-size: 0.7em; padding: 0.15rem 0.3rem;"><i class="fas fa-edit" style="font-size: 0.7em;"></i></a>
                                    <a href="{% url 'inventory:product_delete' product.pk %}" class="btn btn-danger" title="حذف" style="font-size: 0.7em; padding: 0.15rem 0.3rem;"><i class="fas fa-trash" style="font-size: 0.7em;"></i></a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center py-4">لا توجد منتجات متاحة</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="تصفح الصفحات" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1&page_size={{ page_size }}&search={{ search_query }}&category={{ category_filter }}&status={{ status_filter }}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}&page_size={{ page_size }}&search={{ search_query }}&category={{ category_filter }}&status={{ status_filter }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item"><a class="page-link" href="?page={{ num }}&page_size={{ page_size }}&search={{ search_query }}&category={{ category_filter }}&status={{ status_filter }}">{{ num }}</a></li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}&page_size={{ page_size }}&search={{ search_query }}&category={{ category_filter }}&status={{ status_filter }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}&page_size={{ page_size }}&search={{ search_query }}&category={{ category_filter }}&status={{ status_filter }}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <form method="get" class="d-flex justify-content-center align-items-center mt-2">
                    <label for="page_select" class="me-2">انتقل إلى صفحة:</label>
                    <select id="page_select" name="page" class="form-select w-auto me-2" onchange="this.form.submit()">
                        {% for num in page_obj.paginator.page_range %}
                            <option value="{{ num }}" {% if page_obj.number == num %}selected{% endif %}>{{ num }}</option>
                        {% endfor %}
                    </select>
                    <input type="hidden" name="page_size" value="{{ page_size }}">
                    <input type="hidden" name="search" value="{{ search_query }}">
                    <input type="hidden" name="category" value="{{ category_filter }}">
                    <input type="hidden" name="status" value="{{ status_filter }}">
                </form>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

<style>
.action-buttons {
    display: flex;
    gap: 0.1rem;
    justify-content: center;
    align-items: center;
}
</style>
