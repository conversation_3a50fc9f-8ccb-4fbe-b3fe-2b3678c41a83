{% extends 'inventory/inventory_base_custom.html' %}
{% load static %}

{% block inventory_title %}تفاصيل المستودع: {{ warehouse.name }}{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">لوحة التحكم</a></li>
<li class="breadcrumb-item"><a href="{% url 'inventory:warehouse_list' %}">المستودعات</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ warehouse.name }}</li>
{% endblock %}

{% block quick_actions %}
<a href="{% url 'inventory:warehouse_list' %}" class="btn btn-secondary btn-sm">
    <i class="fas fa-arrow-left"></i> العودة للقائمة
</a>
<a href="{% url 'inventory:warehouse_update' warehouse.id %}" class="btn btn-primary btn-sm">
    <i class="fas fa-edit"></i> تعديل
</a>
{% endblock %}

{% block inventory_content %}
<div class="row">
    <div class="col-md-8">
        <!-- معلومات أساسية -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-warehouse"></i>
                    معلومات المستودع
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>اسم المستودع:</strong></td>
                                <td>{{ warehouse.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>الرمز:</strong></td>
                                <td><code>{{ warehouse.code }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>الفرع:</strong></td>
                                <td>{% if warehouse.branch %}{{ warehouse.branch.name }}{% else %}<span class="text-muted">جميع الفروع</span>{% endif %}</td>
                            </tr>
                            <tr>
                                <td><strong>المدير:</strong></td>
                                <td>{% if warehouse.manager %}{{ warehouse.manager.get_full_name }}{% else %}<span class="text-muted">غير محدد</span>{% endif %}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if warehouse.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>العنوان:</strong></td>
                                <td>{% if warehouse.address %}{{ warehouse.address }}{% else %}<span class="text-muted">غير محدد</span>{% endif %}</td>
                            </tr>
                            <tr>
                                <td><strong>ملاحظات:</strong></td>
                                <td>{% if warehouse.notes %}{{ warehouse.notes }}{% else %}<span class="text-muted">لا توجد ملاحظات</span>{% endif %}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- مواقع التخزين -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-map-marker-alt"></i>
                    مواقع التخزين
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم الموقع</th>
                                <th>الرمز</th>
                                <th>الوصف</th>
                                <th>المنتجات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for location in warehouse.locations.all %}
                            <tr>
                                <td>{{ location.name }}</td>
                                <td><code>{{ location.code }}</code></td>
                                <td>{% if location.description %}{{ location.description }}{% else %}<span class="text-muted">-</span>{% endif %}</td>
                                <td><span class="badge bg-primary">0</span></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'inventory:warehouse_location_detail' location.id %}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'inventory:warehouse_location_update' location.id %}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center">لا توجد مواقع تخزين في هذا المستودع</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <a href="{% url 'inventory:warehouse_location_create' %}" class="btn btn-success btn-sm">
                        <i class="fas fa-plus"></i> إضافة موقع تخزين جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- المنتجات المخزنة -->
        <div class="card mt-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-boxes"></i>
                    المنتجات المخزنة ({{ warehouse_products|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if warehouse_products %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الرمز</th>
                                <th>الكمية المتوفرة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in warehouse_products %}
                            <tr>
                                <td>
                                    <strong>{{ product.name }}</strong>
                                </td>
                                <td><code>{{ product.code|default:"-" }}</code></td>
                                <td>
                                    <span class="badge bg-success">{{ product.quantity|floatformat:0 }}</span>
                                </td>
                                <td>
                                    <a href="{% url 'inventory:product_detail' product.id %}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد منتجات مخزنة</h5>
                    <p class="text-muted">سيتم عرض المنتجات هنا عند ربطها بالمستودع</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- حركات المخزون الأخيرة -->
        <div class="card mt-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exchange-alt"></i>
                    حركات المخزون الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_transactions %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>النوع</th>
                                <th>الكمية</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in recent_transactions %}
                            <tr>
                                <td>
                                    <small>{{ transaction.product.name }}</small>
                                </td>
                                <td>
                                    {% if transaction.transaction_type == 'in' %}
                                        <span class="badge bg-success">وارد</span>
                                    {% elif transaction.transaction_type == 'out' %}
                                        <span class="badge bg-danger">صادر</span>
                                    {% else %}
                                        <span class="badge bg-info">{{ transaction.get_transaction_type_display }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ transaction.quantity }}</small>
                                </td>
                                <td>
                                    <small>{{ transaction.transaction_date|date:"d/m/Y" }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-history fa-2x text-muted mb-2"></i>
                    <p class="text-muted small">لا توجد حركات مخزون حديثة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- إحصائيات سريعة -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h3 class="text-primary">{{ warehouse.locations.count }}</h3>
                        <small>مواقع التخزين</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h3 class="text-success">{{ products_count }}</h3>
                        <small>المنتجات</small>
                    </div>
                    <div class="col-6">
                        <h3 class="text-info">{{ total_quantity|floatformat:0 }}</h3>
                        <small>الكمية الإجمالية</small>
                    </div>
                    <div class="col-6">
                        <h3 class="text-warning">{{ total_value|floatformat:2 }}</h3>
                        <small>القيمة الإجمالية</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="card mt-3">
            <div class="card-header bg-dark text-white">
                <h6 class="mb-0">
                    <i class="fas fa-cogs"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'inventory:warehouse_location_create' %}" class="btn btn-outline-primary">
                        <i class="fas fa-plus"></i> إضافة موقع تخزين
                    </a>
                    <a href="{% url 'inventory:warehouse_update' warehouse.id %}" class="btn btn-outline-warning">
                        <i class="fas fa-edit"></i> تعديل المستودع
                    </a>
                    <button type="button" class="btn btn-outline-info">
                        <i class="fas fa-print"></i> طباعة التقرير
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="confirmDelete('{% url 'inventory:warehouse_delete' warehouse.id %}', '{{ warehouse.name }}')">
                        <i class="fas fa-trash"></i> حذف المستودع
                    </button>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="card mt-3">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <strong>تاريخ الإنشاء:</strong> {{ warehouse.created_date_display }}<br>
                    <strong>آخر تعديل:</strong> {{ warehouse.updated_date_display }}<br>
                    <strong>تم إنشاؤه بواسطة:</strong> {{ warehouse.created_by_name }}
                </small>
            </div>
        </div>
    </div>
</div>

{% include 'inventory/sweetalert2_utils.html' %}
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
// تم استبدال confirmDelete بـ SweetAlert2 في ملف sweetalert2_utils.html
</script>
{% endblock %}
