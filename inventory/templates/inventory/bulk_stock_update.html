{% extends 'inventory/inventory_base_custom.html' %}
{% load static %}

{% block inventory_title %}تحديث المخزون بالجملة{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">لوحة التحكم</a></li>
<li class="breadcrumb-item"><a href="{% url 'inventory:product_list' %}">المنتجات</a></li>
<li class="breadcrumb-item active" aria-current="page">تحديث المخزون بالجملة</li>
{% endblock %}

{% block quick_actions %}
<a href="{% url 'inventory:download_excel_template' %}" class="btn btn-info btn-sm">
    <i class="fas fa-download"></i> تحميل القالب
</a>
<a href="{% url 'inventory:product_bulk_upload' %}" class="btn btn-primary btn-sm">
    <i class="fas fa-plus"></i> إضافة منتجات جديدة
</a>
<a href="{% url 'inventory:product_list' %}" class="btn btn-secondary btn-sm">
    <i class="fas fa-arrow-left"></i> العودة للقائمة
</a>
{% endblock %}

{% block inventory_content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-sync-alt"></i>
                    تحديث كميات المخزون
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> هذه الوظيفة لتحديث كميات المنتجات الموجودة فقط. 
                    لإضافة منتجات جديدة، استخدم صفحة "رفع المنتجات بالجملة".
                </div>

                <form method="post" enctype="multipart/form-data" id="updateForm">
                    {% csrf_token %}
                    
                    <div class="mb-4">
                        <label for="{{ form.excel_file.id_for_label }}" class="form-label">
                            {{ form.excel_file.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.excel_file }}
                        {% if form.excel_file.help_text %}
                            <div class="form-text">{{ form.excel_file.help_text }}</div>
                        {% endif %}
                        {% if form.excel_file.errors %}
                            <div class="text-danger">
                                {% for error in form.excel_file.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        <label for="{{ form.warehouse.id_for_label }}" class="form-label">
                            {{ form.warehouse.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.warehouse }}
                        {% if form.warehouse.errors %}
                            <div class="text-danger">
                                {% for error in form.warehouse.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        <label for="{{ form.update_type.id_for_label }}" class="form-label">
                            {{ form.update_type.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.update_type }}
                        <div class="form-text">
                            <ul class="mb-0">
                                <li><strong>استبدال:</strong> تعديل الكمية إلى القيمة المحددة</li>
                                <li><strong>إضافة:</strong> زيادة الكمية الحالية</li>
                                <li><strong>خصم:</strong> تقليل الكمية الحالية</li>
                            </ul>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="{{ form.reason.id_for_label }}" class="form-label">
                            {{ form.reason.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.reason }}
                        {% if form.reason.errors %}
                            <div class="text-danger">
                                {% for error in form.reason.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-warning btn-lg" id="submitBtn">
                            <i class="fas fa-sync-alt"></i>
                            تحديث المخزون
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- تعليمات الاستخدام -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    تعليمات الاستخدام
                </h6>
            </div>
            <div class="card-body">
                <ol class="mb-0">
                    <li class="mb-2">قم بإعداد ملف إكسل بعمودين فقط</li>
                    <li class="mb-2">العمود الأول: "كود المنتج"</li>
                    <li class="mb-2">العمود الثاني: "الكمية"</li>
                    <li class="mb-2">اختر نوع التحديث المناسب</li>
                    <li class="mb-2">أدخل سبب التحديث</li>
                    <li>اضغط على "تحديث المخزون"</li>
                </ol>
            </div>
        </div>

        <!-- الأعمدة المطلوبة -->
        <div class="card mt-3">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-table"></i>
                    الأعمدة المطلوبة
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>العمود</th>
                            <th>وصف</th>
                            <th>مطلوب</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>كود المنتج</strong></td>
                            <td>كود المنتج المراد تحديثه</td>
                            <td><span class="text-danger">نعم</span></td>
                        </tr>
                        <tr>
                            <td><strong>الكمية</strong></td>
                            <td>الكمية الجديدة أو المراد إضافتها/خصمها</td>
                            <td><span class="text-danger">نعم</span></td>
                        </tr>
                        <tr>
                            <td><strong>ملاحظات</strong></td>
                            <td>ملاحظات إضافية (اختياري)</td>
                            <td>لا</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- أمثلة -->
        <div class="card mt-3">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb"></i>
                    أمثلة
                </h6>
            </div>
            <div class="card-body">
                <h6>مثال للاستبدال:</h6>
                <table class="table table-sm table-bordered">
                    <tr><th>كود المنتج</th><th>الكمية</th></tr>
                    <tr><td>LAP001</td><td>50</td></tr>
                </table>
                <small class="text-muted">سيتم تعديل كمية LAP001 إلى 50</small>

                <h6 class="mt-3">مثال للإضافة:</h6>
                <table class="table table-sm table-bordered">
                    <tr><th>كود المنتج</th><th>الكمية</th></tr>
                    <tr><td>LAP001</td><td>10</td></tr>
                </table>
                <small class="text-muted">سيتم إضافة 10 قطع للكمية الحالية</small>
            </div>
        </div>

        <!-- تحذيرات -->
        <div class="card mt-3">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    تحذيرات مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li class="mb-1">تأكد من صحة أكواد المنتجات</li>
                    <li class="mb-1">عملية التحديث لا يمكن التراجع عنها</li>
                    <li class="mb-1">لن يتم قبول الكميات السالبة</li>
                    <li>سيتم إنشاء حركات مخزون لكل تغيير</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('updateForm');
    const submitBtn = document.getElementById('submitBtn');
    const fileInput = document.getElementById('{{ form.excel_file.id_for_label }}');
    const updateTypeSelect = document.getElementById('{{ form.update_type.id_for_label }}');
    
    // تغيير النص عند اختيار ملف
    fileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const fileName = this.files[0].name;
            const fileSize = (this.files[0].size / 1024 / 1024).toFixed(2);
            
            let fileInfo = document.getElementById('fileInfo');
            if (!fileInfo) {
                fileInfo = document.createElement('div');
                fileInfo.id = 'fileInfo';
                fileInfo.className = 'alert alert-info mt-2';
                this.parentNode.appendChild(fileInfo);
            }
            
            fileInfo.innerHTML = `
                <i class="fas fa-file-excel"></i>
                <strong>الملف المحدد:</strong> ${fileName} (${fileSize} ميجابايت)
            `;
        }
    });
    
    // تغيير لون الزر حسب نوع التحديث
    updateTypeSelect.addEventListener('change', function() {
        submitBtn.className = 'btn btn-lg d-grid gap-2';
        
        switch(this.value) {
            case 'replace':
                submitBtn.classList.add('btn-warning');
                break;
            case 'add':
                submitBtn.classList.add('btn-success');
                break;
            case 'subtract':
                submitBtn.classList.add('btn-danger');
                break;
            default:
                submitBtn.classList.add('btn-warning');
        }
    });
    
    // تأكيد قبل الإرسال
    form.addEventListener('submit', function(e) {
        if (!fileInput.files || !fileInput.files[0]) {
            e.preventDefault();
            alert('يرجى اختيار ملف إكسل أولاً');
            return;
        }
        
        const updateType = updateTypeSelect.value;
        let confirmMessage = 'هل أنت متأكد من تحديث المخزون؟';
        
        switch(updateType) {
            case 'replace':
                confirmMessage = 'هل أنت متأكد من استبدال الكميات الحالية بالقيم الجديدة؟';
                break;
            case 'add':
                confirmMessage = 'هل أنت متأكد من إضافة الكميات المحددة للمخزون الحالي؟';
                break;
            case 'subtract':
                confirmMessage = 'هل أنت متأكد من خصم الكميات المحددة من المخزون الحالي؟';
                break;
        }
        
        const confirmed = confirm(confirmMessage + ' هذه العملية لا يمكن التراجع عنها.');
        if (!confirmed) {
            e.preventDefault();
            return;
        }
        
        // تعطيل الزر وإظهار loader
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            جاري التحديث...
        `;
    });
});
</script>
{% endblock %}
