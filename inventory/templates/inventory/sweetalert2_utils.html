{% load static %}

<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">

<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.all.min.js"></script>

<script>
// دوال مساعدة لـ SweetAlert2 لاستبدال Bootstrap Modal

// دالة لفتح نموذج إضافة فئة
function openAddCategoryModal() {
    Swal.fire({
        title: 'إضافة فئة جديدة',
        html: `
            <form id="categoryForm" method="post" action="{% url 'inventory:category_create' %}">
                {% csrf_token %}
                <div class="mb-3">
                    <label for="name" class="form-label">اسم الفئة</label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                <div class="mb-3">
                    <label for="parent" class="form-label">الفئة الأب</label>
                    <select class="form-select" id="parent" name="parent">
                        <option value="">بدون فئة أب</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}">{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <label for="description" class="form-label">الوصف</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                </div>
            </form>
        `,
        showCancelButton: true,
        confirmButtonText: 'إضافة',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        reverseButtons: true,
        customClass: {
            popup: 'swal2-rtl',
            confirmButton: 'btn btn-success',
            cancelButton: 'btn btn-secondary'
        },
        preConfirm: () => {
            const form = document.getElementById('categoryForm');
            const formData = new FormData(form);
            
            return fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(response.statusText);
                }
                return response.json();
            })
            .catch(error => {
                Swal.showValidationMessage(`خطأ في الإرسال: ${error}`);
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'تم الإضافة بنجاح!',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false
            }).then(() => {
                window.location.reload();
            });
        }
    });
}

// دالة لفتح نموذج إضافة مستودع
function openAddWarehouseModal() {
    Swal.fire({
        title: 'إضافة مستودع جديد',
        html: `
            <form id="warehouseForm" method="post" action="{% url 'inventory:warehouse_create' %}">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم المستودع</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="code" class="form-label">رمز المستودع</label>
                            <input type="text" class="form-control" id="code" name="code" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="branch" class="form-label">الفرع</label>
                            <select class="form-select" id="branch" name="branch">
                                <option value="">اختر الفرع (اختياري)</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}">{{ branch.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="manager" class="form-label">المدير</label>
                            <select class="form-select" id="manager" name="manager">
                                <option value="">بدون مدير</option>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.get_full_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="1"></textarea>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                    <label class="form-check-label" for="is_active">نشط</label>
                </div>
            </form>
        `,
        showCancelButton: true,
        confirmButtonText: 'إضافة',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        reverseButtons: true,
        width: '600px',
        customClass: {
            popup: 'swal2-rtl',
            confirmButton: 'btn btn-success',
            cancelButton: 'btn btn-secondary'
        },
        preConfirm: () => {
            const form = document.getElementById('warehouseForm');
            const formData = new FormData(form);
            
            return fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(response.statusText);
                }
                return response.json();
            })
            .catch(error => {
                Swal.showValidationMessage(`خطأ في الإرسال: ${error}`);
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'تم الإضافة بنجاح!',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false
            }).then(() => {
                window.location.reload();
            });
        }
    });
}

// دالة لفتح نموذج إضافة مورد
function openAddSupplierModal() {
    Swal.fire({
        title: 'إضافة مورد جديد',
        html: `
            <form id="supplierForm" method="post" action="{% url 'inventory:supplier_create' %}">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم المورد</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_person" class="form-label">الشخص المسؤول</label>
                            <input type="text" class="form-control" id="contact_person" name="contact_person">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="address" class="form-label">العنوان</label>
                    <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                </div>
                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                </div>
            </form>
        `,
        showCancelButton: true,
        confirmButtonText: 'إضافة',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        reverseButtons: true,
        width: '600px',
        customClass: {
            popup: 'swal2-rtl',
            confirmButton: 'btn btn-success',
            cancelButton: 'btn btn-secondary'
        },
        preConfirm: () => {
            const form = document.getElementById('supplierForm');
            const formData = new FormData(form);
            
            return fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(response.statusText);
                }
                return response.json();
            })
            .catch(error => {
                Swal.showValidationMessage(`خطأ في الإرسال: ${error}`);
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'تم الإضافة بنجاح!',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false
            }).then(() => {
                window.location.reload();
            });
        }
    });
}

// دالة لفتح نموذج إضافة موقع تخزين
function openAddLocationModal() {
    Swal.fire({
        title: 'إضافة موقع تخزين جديد',
        html: `
            <form id="locationForm" method="post" action="{% url 'inventory:warehouse_location_create' %}">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم الموقع</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="warehouse" class="form-label">المستودع</label>
                            <select class="form-select" id="warehouse" name="warehouse" required>
                                <option value="">اختر المستودع</option>
                                {% for warehouse in warehouses %}
                                <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="location_type" class="form-label">نوع الموقع</label>
                            <select class="form-select" id="location_type" name="location_type">
                                <option value="shelf">رف</option>
                                <option value="area">منطقة</option>
                                <option value="room">غرفة</option>
                                <option value="section">قسم</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="capacity" class="form-label">السعة</label>
                            <input type="number" class="form-control" id="capacity" name="capacity" min="0">
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="description" class="form-label">الوصف</label>
                    <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                </div>
            </form>
        `,
        showCancelButton: true,
        confirmButtonText: 'إضافة',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        reverseButtons: true,
        width: '600px',
        customClass: {
            popup: 'swal2-rtl',
            confirmButton: 'btn btn-success',
            cancelButton: 'btn btn-secondary'
        },
        preConfirm: () => {
            const form = document.getElementById('locationForm');
            const formData = new FormData(form);
            
            return fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(response.statusText);
                }
                return response.json();
            })
            .catch(error => {
                Swal.showValidationMessage(`خطأ في الإرسال: ${error}`);
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'تم الإضافة بنجاح!',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false
            }).then(() => {
                window.location.reload();
            });
        }
    });
}

// دالة لفتح نموذج إضافة طلب شراء
function openAddPurchaseOrderModal() {
    Swal.fire({
        title: 'إنشاء طلب شراء جديد',
        html: `
            <form id="purchaseOrderForm" method="post" action="{% url 'inventory:purchase_order_create' %}">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="supplier" class="form-label">المورد</label>
                            <select class="form-select" id="supplier" name="supplier" required>
                                <option value="">اختر المورد</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="warehouse" class="form-label">المستودع (اختياري)</label>
                            <select class="form-select" id="warehouse" name="warehouse">
                                <option value="">اختر المستودع</option>
                                {% for warehouse in warehouses %}
                                <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="expected_date" class="form-label">تاريخ التسليم المتوقع</label>
                            <input type="date" class="form-control" id="expected_date" name="expected_date">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="draft" selected>مسودة</option>
                                <option value="pending">قيد الانتظار</option>
                                <option value="approved">تمت الموافقة</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                </div>
            </form>
        `,
        showCancelButton: true,
        confirmButtonText: 'إنشاء',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        reverseButtons: true,
        width: '600px',
        customClass: {
            popup: 'swal2-rtl',
            confirmButton: 'btn btn-success',
            cancelButton: 'btn btn-secondary'
        },
        preConfirm: () => {
            const form = document.getElementById('purchaseOrderForm');
            const formData = new FormData(form);
            
            return fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(response.statusText);
                }
                return response.json();
            })
            .catch(error => {
                Swal.showValidationMessage(`خطأ في الإرسال: ${error}`);
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'تم إنشاء الطلب بنجاح!',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false
            }).then(() => {
                window.location.reload();
            });
        }
    });
}

// دالة تأكيد الحذف
function confirmDelete(url, itemName) {
    Swal.fire({
        title: 'تأكيد الحذف',
        text: `هل أنت متأكد من حذف ${itemName}؟`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'حذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        reverseButtons: true,
        customClass: {
            popup: 'swal2-rtl',
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار مؤشر التحميل
            Swal.fire({
                title: 'جارٍ الحذف...',
                text: 'يرجى الانتظار',
                icon: 'info',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // إنشاء form مخفي للحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            
            // إضافة CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = 'csrfmiddlewaretoken';
            csrfToken.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
            form.appendChild(csrfToken);
            
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// دالة عرض رسالة نجاح
function showSuccessMessage(message) {
    Swal.fire({
        title: 'نجح!',
        text: message,
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
    });
}

// دالة عرض رسالة خطأ
function showErrorMessage(message) {
    Swal.fire({
        title: 'خطأ!',
        text: message,
        icon: 'error',
        confirmButtonText: 'حسناً'
    });
}

// دالة عرض رسالة تحذير
function showWarningMessage(message) {
    Swal.fire({
        title: 'تحذير!',
        text: message,
        icon: 'warning',
        confirmButtonText: 'حسناً'
    });
}

// دالة اختبار زر الحذف
function testDeleteButton() {
    Swal.fire({
        title: 'اختبار زر الحذف',
        text: 'هذا اختبار لزر الحذف',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'حذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'تم الحذف!',
                text: 'هذا مجرد اختبار',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        }
    });
}
</script> 