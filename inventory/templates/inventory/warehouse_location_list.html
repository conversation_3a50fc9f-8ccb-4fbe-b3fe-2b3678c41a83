{% extends 'inventory/inventory_base_custom.html' %}
{% load static %}

{% block inventory_title %}مواقع التخزين{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">لوحة التحكم</a></li>
<li class="breadcrumb-item"><a href="{% url 'inventory:warehouse_list' %}">المستودعات</a></li>
<li class="breadcrumb-item active" aria-current="page">مواقع التخزين</li>
{% endblock %}

{% block quick_actions %}
<button type="button" class="btn btn-success btn-sm" onclick="openAddLocationModal()">
    <i class="fas fa-plus"></i> إضافة موقع
</button>
<a href="{% url 'inventory:warehouse_list' %}" class="btn btn-primary btn-sm">
    <i class="fas fa-warehouse"></i> المستودعات
</a>
{% endblock %}

{% block inventory_content %}
<div class="warehouse-location-list-container">
    <!-- إحصائيات سريعة -->
    <div class="stats-cards-container">
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">إجمالي المواقع</div>
                <div class="stat-card-value">{{ total_locations }}</div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i> 3% منذ الشهر الماضي
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-warehouse"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">المستودعات</div>
                <div class="stat-card-value">{{ warehouses_count }}</div>
                <div class="stat-card-change neutral">
                    <i class="fas fa-minus"></i> لا تغيير
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-box"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">المنتجات المخزنة</div>
                <div class="stat-card-value">{{ products_count }}</div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i> 8% منذ الشهر الماضي
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-card-icon">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="stat-card-content">
                <div class="stat-card-title">نسبة الإشغال</div>
                <div class="stat-card-value">{{ occupancy_rate }}%</div>
                <div class="stat-card-change negative">
                    <i class="fas fa-arrow-up"></i> 5% منذ الشهر الماضي
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="filters-container mb-4">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="اسم الموقع، الرمز...">
                    </div>
                    <div class="col-md-3">
                        <label for="warehouse" class="form-label">المستودع</label>
                        <select class="form-select" id="warehouse" name="warehouse">
                            <option value="">جميع المستودعات</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}" {% if selected_warehouse == warehouse.id|stringformat:"s" %}selected{% endif %}>{{ warehouse.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" {% if selected_status == 'active' %}selected{% endif %}>نشط</option>
                            <option value="inactive" {% if selected_status == 'inactive' %}selected{% endif %}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="sort" class="form-label">ترتيب</label>
                        <select class="form-select" id="sort" name="sort">
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>الاسم (أ-ي)</option>
                            <option value="-name" {% if sort_by == '-name' %}selected{% endif %}>الاسم (ي-أ)</option>
                            <option value="warehouse" {% if sort_by == 'warehouse' %}selected{% endif %}>المستودع</option>
                            <option value="occupancy" {% if sort_by == 'occupancy' %}selected{% endif %}>نسبة الإشغال (تصاعدي)</option>
                            <option value="-occupancy" {% if sort_by == '-occupancy' %}selected{% endif %}>نسبة الإشغال (تنازلي)</option>
                        </select>
                    </div>
                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="{% url 'inventory:warehouse_location_list' %}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- قائمة مواقع التخزين -->
    <div class="data-table-container">
        <div class="data-table-header">
            <h4 class="data-table-title">
                مواقع التخزين
                {% if locations %}
                <span class="badge bg-primary">{{ locations|length }}</span>
                {% endif %}
            </h4>
            <div class="data-table-actions">
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-file-excel"></i> Excel</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-file-pdf"></i> PDF</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-print"></i> طباعة</a></li>
                    </ul>
                </div>
                                    <button type="button" class="btn btn-success" onclick="openAddLocationModal()">
                        <i class="fas fa-plus"></i> إضافة موقع
                    </button>
            </div>
        </div>
        <div class="data-table-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>اسم الموقع</th>
                            <th>الرمز</th>
                            <th>المستودع</th>
                            <th>السعة</th>
                            <th>المستخدم</th>
                            <th>نسبة الإشغال</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for location in locations %}
                        <tr>
                            <td>
                                <strong>{{ location.name }}</strong>
                            </td>
                            <td>{{ location.code }}</td>
                            <td>{{ location.warehouse.name }}</td>
                            <td>{{ location.capacity }}</td>
                            <td>{{ location.used_capacity }}</td>
                            <td>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar {% if location.occupancy_rate > 90 %}bg-danger{% elif location.occupancy_rate > 70 %}bg-warning{% else %}bg-success{% endif %}" role="progressbar" style="width: {{ location.occupancy_rate }}%;" aria-valuenow="{{ location.occupancy_rate }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <small>{{ location.occupancy_rate }}%</small>
                            </td>
                            <td>
                                {% if location.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'inventory:warehouse_location_update' location.id %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'inventory:warehouse_location_delete' location.id %}" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <a href="{% url 'inventory:warehouse_location_detail' location.id %}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">لا توجد مواقع تخزين مطابقة للبحث</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- الصفحات -->
        {% if page_obj.has_other_pages %}
        <div class="data-table-footer">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_warehouse %}&warehouse={{ selected_warehouse }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_warehouse %}&warehouse={{ selected_warehouse }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                        <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                        {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                        <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_warehouse %}&warehouse={{ selected_warehouse }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">{{ i }}</a></li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_warehouse %}&warehouse={{ selected_warehouse }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_warehouse %}&warehouse={{ selected_warehouse }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

{% include 'inventory/sweetalert2_utils.html' %}
{% endblock %}
