# Generated by Django 4.2.21 on 2025-08-07 08:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0003_alter_warehouse_branch'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='stocktransaction',
            new_name='stock_product_idx',
            old_name='transaction_product_idx',
        ),
        migrations.RenameIndex(
            model_name='stocktransaction',
            new_name='stock_type_idx',
            old_name='transaction_type_idx',
        ),
        migrations.RenameIndex(
            model_name='stocktransaction',
            new_name='stock_date_idx',
            old_name='transaction_date_idx',
        ),
        migrations.AddField(
            model_name='stocktransaction',
            name='warehouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='stock_transactions', to='inventory.warehouse', verbose_name='المستودع'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['warehouse'], name='stock_warehouse_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['product', 'warehouse'], name='stock_prod_wareh_idx'),
        ),
    ]
