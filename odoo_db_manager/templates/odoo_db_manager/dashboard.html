{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'odoo_db_manager/css/style.css' %}">
<style>
    /* أنماط الأيقونات */
    .btn-link {
        border: none;
        background: none;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .btn-link:hover {
        transform: scale(1.1);
        text-decoration: none;
    }

    .btn-link i {
        transition: all 0.3s ease;
    }

    .btn-link:hover i {
        filter: brightness(1.2);
    }

    /* تحسين عرض SweetAlert */
    .swal-wide {
        width: 600px !important;
    }

    .swal-wide .swal2-html-container {
        text-align: right;
        direction: rtl;
    }

    .swal-wide .alert {
        margin-bottom: 15px;
        padding: 12px;
        border-radius: 6px;
    }

    .swal-wide ul {
        list-style: none;
        padding-right: 20px;
    }

    /* تخطيط الصفحة الجديد */
    .page-header {
        margin-bottom: 2rem;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1.5rem;
        text-align: right;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: flex-start;
        margin-bottom: 2rem;
    }

    /* تصميم أزرار جديد وجميل */
    .modern-btn {
        position: relative;
        padding: 0.8rem 1.5rem;
        border: none;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.95rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.6rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        cursor: pointer;
    }

    .modern-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .modern-btn:hover::before {
        left: 100%;
    }

    .modern-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        text-decoration: none;
    }

    .modern-btn:active {
        transform: translateY(0);
    }

    /* ألوان الأزرار الحديثة */
    .modern-btn.btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .modern-btn.btn-success {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
    }

    .modern-btn.btn-secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .modern-btn.btn-info {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
    }

    .modern-btn.btn-warning {
        background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
        color: white;
    }

    .modern-btn.btn-dark {
        background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
        color: white;
    }

    .modern-btn.btn-danger {
        background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        color: white;
    }

    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
            text-align: center;
        }

        .action-buttons {
            flex-direction: column;
            align-items: stretch;
        }

        .modern-btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="odoo-dashboard">
        


        <!-- رأس الصفحة الجديد -->
        <div class="page-header">
            <h1 class="page-title">إدارة قواعد البيانات</h1>

            <div class="action-buttons">
                <a href="{% url 'odoo_db_manager:database_create' %}" class="modern-btn btn-primary">
                    <i class="fas fa-plus"></i>
                    إنشاء قاعدة بيانات
                </a>
                <a href="{% url 'backup_system:dashboard' %}" class="modern-btn btn-success">
                    <i class="fas fa-shield-alt"></i>
                    نظام النسخ الاحتياطي
                </a>
                <button onclick="refreshConnectionStatus()" class="modern-btn btn-secondary">
                    <i class="fas fa-sync-alt"></i>
                    تحديث حالة الاتصال
                </button>
                <a href="{% url 'odoo_db_manager:google_drive_settings' %}" class="modern-btn btn-info">
                    <i class="fab fa-google-drive"></i>
                    إعدادات Google Drive
                </a>
                <a href="{% url 'odoo_db_manager:google_sync' %}" class="modern-btn btn-warning">
                    <i class="fas fa-sync-alt"></i>
                    مزامنة غوغل
                </a>
                <a href="{% url 'odoo_db_manager:advanced_sync_dashboard' %}" class="modern-btn btn-dark">
                    <i class="fas fa-cogs"></i>
                    المزامنة المتقدمة
                </a>
            </div>
        </div>

        <!-- قائمة قواعد البيانات -->
        <div class="odoo-list-view">
            <table class="table">
                <thead>
                    <tr>
                        <th>اسم قاعدة البيانات</th>
                        <th>النوع</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for db in databases %}
                    <tr>
                        <td>
                            <strong>{{ db.name }}</strong>
                            {% if db.connection_info.NAME %}
                                <small class="text-muted d-block">{{ db.connection_info.NAME }}</small>
                            {% endif %}
                        </td>
                        <td>{{ db.get_db_type_display }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if db.is_active %}
                                <span class="badge bg-primary me-1">نشطة</span>
                                {% endif %}

                                {% if db.status %}
                                <span class="badge bg-success" title="متصلة">
                                    <i class="fas fa-plug"></i>
                                </span>
                                {% else %}
                                <span class="badge bg-danger" title="غير متصلة">
                                    <i class="fas fa-times-circle"></i>
                                </span>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ db.created_at|date:"Y-m-d H:i" }}</td>
                        <td>
                            <div class="d-flex justify-content-center gap-1">
                                <a href="{% url 'odoo_db_manager:database_detail' db.id %}" class="btn btn-link text-info p-1" title="عرض التفاصيل">
                                    <i class="fas fa-eye fa-lg"></i>
                                </a>
                                <a href="{% url 'odoo_db_manager:schedule_create_for_database' db.id %}" class="btn btn-link text-warning p-1" title="جدولة النسخ الاحتياطية">
                                    <i class="fas fa-clock fa-lg"></i>
                                </a>
                                {% if not db.is_active %}
                                <button type="button" onclick="confirmActivateDatabase('{% url 'odoo_db_manager:activate_database' db.id %}', '{{ db.name }}'); return false;" class="btn btn-link text-primary p-1" title="تنشيط قاعدة البيانات">
                                    <i class="fas fa-power-off fa-lg"></i>
                                </button>
                                {% endif %}
                                <a href="{% url 'odoo_db_manager:delete_database' db.id %}" class="btn btn-link text-danger p-1" title="حذف قاعدة البيانات">
                                    <i class="fas fa-trash fa-lg"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center">لا توجد قواعد بيانات</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="odoo-stats">
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">قواعد البيانات</h5>
                            <p class="card-text display-4">{{ databases.count }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">النسخ الاحتياطية</h5>
                            <p class="card-text display-4">{{ backups.count }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">المساحة المستخدمة</h5>
                            <p class="card-text display-4">{{ total_size_display }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">آخر نسخة احتياطية</h5>
                            <p class="card-text">{{ last_backup.created_at|date:"Y-m-d H:i"|default:"لا يوجد" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تم حذف قسم النسخ الاحتياطية - استخدم backup_system بدلاً من ذلك -->

        <!-- قواعد البيانات المكتشفة في PostgreSQL -->
        {% if discovered_databases %}
        <div class="odoo-section mt-4">
            <h3>قواعد البيانات الموجودة في PostgreSQL</h3>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم قاعدة البيانات</th>
                            <th>المالك</th>
                            <th>الترميز</th>
                            <th>الحجم</th>
                            <th>مسجلة في النظام</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for db_info in discovered_databases %}
                        <tr>
                            <td><strong>{{ db_info.name }}</strong></td>
                            <td>{{ db_info.owner|default:"غير محدد" }}</td>
                            <td>{{ db_info.encoding|default:"UTF8" }}</td>
                            <td>{{ db_info.size|default:"غير محدد" }}</td>
                            <td>
                                {% if db_info.registered %}
                                    <span class="badge bg-success">مسجلة</span>
                                {% else %}
                                    <span class="badge bg-warning">غير مسجلة</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if not db_info.registered %}
                                    <button class="btn btn-sm btn-primary" onclick="registerDatabase('{{ db_info.name }}')">
                                        <i class="fas fa-plus"></i> تسجيل
                                    </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // عرض رسالة نجاح تنشيط قاعدة البيانات إذا كان ضروريًا
    {% if show_activation_success %}
    document.addEventListener('DOMContentLoaded', function() {
        Swal.fire({
            title: 'تم تنشيط قاعدة البيانات بنجاح!',
            html: `
                <div style="text-align: right; direction: rtl;">
                    <p>تم تنشيط قاعدة البيانات <strong>{{ activated_db_name }}</strong> بنجاح.</p>

                    {% if created_default_user %}
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-user-plus"></i>
                        تم إنشاء مستخدم افتراضي:
                        <ul class="mt-2">
                            <li><strong>اسم المستخدم:</strong> admin</li>
                            <li><strong>كلمة المرور:</strong> admin</li>
                        </ul>
                    </div>
                    {% endif %}

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle"></i>
                        يرجى إعادة تشغيل السيرفر لتطبيق التغييرات.
                    </div>
                </div>
            `,
            icon: 'success',
            confirmButtonText: 'تم',
            customClass: {
                popup: 'rtl-popup',
                title: 'rtl-title',
                content: 'rtl-content'
            }
        });
    });
    {% endif %}

    // دالة تأكيد تنشيط قاعدة البيانات باستخدام SweetAlert
    function confirmActivateDatabase(url, dbName) {
        console.log('confirmActivateDatabase called with:', url, dbName);

        if (typeof Swal === 'undefined') {
            console.error('SweetAlert2 is not loaded!');
            alert('خطأ: SweetAlert2 غير محمل');
            return false;
        }

        console.log('SweetAlert2 is loaded, showing confirmation dialog...');

        Swal.fire({
            title: 'تنشيط قاعدة البيانات',
            html: `هل أنت متأكد من تنشيط قاعدة البيانات <strong>${dbName}</strong>؟<br><br>
                  <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    ملاحظة: سيتم إعادة تشغيل السيرف�� لتطبيق التغييرات.
                  </div>`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، قم بالتنشيط',
            cancelButtonText: 'إلغاء',
            customClass: {
                popup: 'rtl-popup',
                title: 'rtl-title',
                content: 'rtl-content'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('User confirmed activation, sending request to:', url);

                Swal.fire({
                    title: 'جاري تنشيط قاعدة البيانات...',
                    html: 'يرجى الانتظار...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: 'action=activate'
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        return response.json();
                    } else {
                        return response.text().then(text => {
                            throw new Error(`Expected JSON but got: ${text.substring(0, 100)}...`);
                        });
                    }
                })
                .then(data => {
                    console.log('Response data:', data);
                    Swal.close();
                    
                    if (data.success) {
                        let htmlContent = `
                            <div class="text-center">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    تم تنشيط قاعدة البيانات "${data.database_name}" بنجاح!
                                </div>
                        `;

                        if (data.created_default_user) {
                            htmlContent += `
                                <div class="alert alert-info">
                                    <i class="fas fa-user-plus"></i>
                                    <strong>تم إنشاء مستخدم افتراضي:</strong><br>
                                    اسم المستخدم: <code>admin</code><br>
                                    كلمة المرور: <code>admin123</code>
                                </div>
                            `;
                        }

                        if (data.requires_restart) {
                            htmlContent += `
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    يرجى إعادة تشغيل السيرفر لتطبيق التغييرات.
                                </div>
                            `;
                        } else {
                            htmlContent += `
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    تم تطبيق التغييرات مباشرة. يمكنك الآن استخدام قاعدة البيانات الجديدة.
                                </div>
                            `;
                        }

                        htmlContent += '</div>';

                        Swal.fire({
                            title: 'تم التنشيط بنجاح!',
                            html: htmlContent,
                            icon: 'success',
                            confirmButtonText: 'تم',
                            confirmButtonColor: '#28a745',
                            width: '600px',
                            customClass: {
                                popup: 'rtl-popup swal-wide',
                                title: 'rtl-title',
                                content: 'rtl-content'
                            }
                        }).then(() => {
                            window.location.reload();
                        });
                    } else {
                        Swal.fire({
                            title: 'خطأ في التنشيط',
                            text: data.message,
                            icon: 'error',
                            confirmButtonText: 'تم',
                            customClass: {
                                popup: 'rtl-popup',
                                title: 'rtl-title',
                                content: 'rtl-content'
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء تنشيط قاعدة البيانات. يرجى المحاولة مرة أخرى.',
                        icon: 'error',
                        confirmButtonText: 'تم',
                        customClass: {
                            popup: 'rtl-popup',
                            title: 'rtl-title',
                            content: 'rtl-content'
                        }
                    });
                });
            }
        });

        return false;
    }

    // دالة تسجيل قاعدة بيانات جديدة
    function registerDatabase(dbName) {
        Swal.fire({
            title: 'تسجيل قاعدة بيانات جديدة',
            html: `هل أنت متأكد من تسجيل قاعدة بيانات جديدة باسم <strong>${dbName}</strong>؟`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، قم بالتسجيل',
            cancelButtonText: 'إلغاء',
            customClass: {
                popup: 'rtl-popup',
                title: 'rtl-title',
                content: 'rtl-content'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'جاري تسجيل قاعدة البيانات...',
                    html: 'يرجى الانتظار...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                fetch('{% url "odoo_db_manager:database_register" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: JSON.stringify({ name: dbName })
                })
                .then(response => response.json())
                .then(data => {
                    Swal.close();

                    if (data.success) {
                        location.reload();
                    } else {
                        Swal.fire({
                            title: 'خطأ',
                            text: data.error,
                            icon: 'error',
                            confirmButtonText: 'تم',
                            customClass: {
                                popup: 'rtl-popup',
                                title: 'rtl-title',
                                content: 'rtl-content'
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء تسجيل قاعدة البيانات. يرجى المحاولة مرة أخرى.',
                        icon: 'error',
                        confirmButtonText: 'تم',
                        customClass: {
                            popup: 'rtl-popup',
                            title: 'rtl-title',
                            content: 'rtl-content'
                        }
                    });
                });
            }
        });
    }

    // دالة لتحديث حالة الاتصال لجميع قواعد البيانات
    function refreshConnectionStatus() {
        Swal.fire({
            title: 'جاري تحديث حالة الاتصال...',
            html: 'يرجى الانتظار...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        fetch('{% url "odoo_db_manager:database_refresh_status" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            Swal.close();

            if (data.success) {
                location.reload();
            } else {
                Swal.fire({
                    title: 'خطأ',
                    text: data.error,
                    icon: 'error',
                    confirmButtonText: 'تم',
                    customClass: {
                        popup: 'rtl-popup',
                        title: 'rtl-title',
                        content: 'rtl-content'
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                title: 'خطأ',
                text: 'حدث خطأ أثناء تحديث حالة الاتصال. يرجى المحاولة مرة أخرى.',
                icon: 'error',
                confirmButtonText: 'تم',
                customClass: {
                    popup: 'rtl-popup',
                    title: 'rtl-title',
                    content: 'rtl-content'
                }
            });
        });
    }
</script>
{% endblock %}