# Generated by Django 4.2.21 on 2025-07-28 15:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='RestoreJob',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='اسم الاستعادة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('source_file', models.CharField(max_length=500, verbose_name='ملف المصدر')),
                ('file_size', models.BigIntegerField(default=0, verbose_name='حجم الملف (بايت)')),
                ('clear_existing_data', models.BooleanField(default=False, verbose_name='حذف البيانات الموجودة')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('running', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('failed', 'فشل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('progress_percentage', models.FloatField(default=0.0, verbose_name='نسبة التقدم')),
                ('current_step', models.CharField(blank=True, max_length=200, verbose_name='الخطوة الحالية')),
                ('total_records', models.IntegerField(default=0, verbose_name='إجمالي السجلات')),
                ('processed_records', models.IntegerField(default=0, verbose_name='السجلات المعالجة')),
                ('success_records', models.IntegerField(default=0, verbose_name='السجلات الناجحة')),
                ('failed_records', models.IntegerField(default=0, verbose_name='السجلات الفاشلة')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ البدء')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'مهمة استعادة',
                'verbose_name_plural': 'مهام الاستعادة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BackupSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الجدولة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('frequency', models.CharField(choices=[('daily', 'يومياً'), ('weekly', 'أسبوعياً'), ('monthly', 'شهرياً')], max_length=20, verbose_name='التكرار')),
                ('hour', models.IntegerField(default=2, verbose_name='الساعة')),
                ('minute', models.IntegerField(default=0, verbose_name='الدقيقة')),
                ('backup_type', models.CharField(choices=[('full', 'نسخة كاملة'), ('partial', 'نسخة جزئية')], default='full', max_length=20, verbose_name='نوع النسخة')),
                ('max_backups_to_keep', models.IntegerField(default=7, verbose_name='عدد النسخ المحفوظة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('last_run', models.DateTimeField(blank=True, null=True, verbose_name='آخر تشغيل')),
                ('next_run', models.DateTimeField(blank=True, null=True, verbose_name='التشغيل القادم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'جدولة نسخ احتياطي',
                'verbose_name_plural': 'جدولة النسخ الاحتياطية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BackupJob',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='اسم النسخة الاحتياطية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('backup_type', models.CharField(choices=[('full', 'نسخة كاملة'), ('partial', 'نسخة جزئية')], default='full', max_length=20, verbose_name='نوع النسخة')),
                ('file_path', models.CharField(blank=True, max_length=500, verbose_name='مسار الملف')),
                ('file_size', models.BigIntegerField(default=0, verbose_name='حجم الملف (بايت)')),
                ('compressed_size', models.BigIntegerField(default=0, verbose_name='الحجم المضغوط (بايت)')),
                ('compression_ratio', models.FloatField(default=0.0, verbose_name='نسبة الضغط')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('running', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('failed', 'فشل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('progress_percentage', models.FloatField(default=0.0, verbose_name='نسبة التقدم')),
                ('current_step', models.CharField(blank=True, max_length=200, verbose_name='الخطوة الحالية')),
                ('total_records', models.IntegerField(default=0, verbose_name='إجمالي السجلات')),
                ('processed_records', models.IntegerField(default=0, verbose_name='السجلات المعالجة')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ البدء')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'مهمة نسخ احتياطي',
                'verbose_name_plural': 'مهام النسخ الاحتياطي',
                'ordering': ['-created_at'],
            },
        ),
    ]
