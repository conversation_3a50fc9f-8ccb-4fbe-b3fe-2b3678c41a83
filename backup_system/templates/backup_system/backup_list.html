{% extends 'backup_system/base.html' %}

{% block page_actions %}
<a href="{% url 'backup_system:backup_create' %}" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i>
    إنشاء نسخة احتياطية
</a>
{% endblock %}

{% block backup_content %}
<!-- فلاتر البحث -->
<div class="backup-card mb-4">
    <div class="backup-card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">فلترة حسب الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>في الانتظار</option>
                    <option value="running" {% if status_filter == 'running' %}selected{% endif %}>قيد التنفيذ</option>
                    <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                    <option value="failed" {% if status_filter == 'failed' %}selected{% endif %}>فاشل</option>
                </select>
            </div>
            
            <div class="col-md-4">
                <label class="form-label">فلترة حسب النوع</label>
                <select name="type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="full" {% if type_filter == 'full' %}selected{% endif %}>نسخة كاملة</option>
                    <option value="partial" {% if type_filter == 'partial' %}selected{% endif %}>نسخة جزئية</option>
                </select>
            </div>
            
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-filter me-1"></i>
                    تطبيق الفلتر
                </button>
                <a href="{% url 'backup_system:backup_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    إزالة الفلتر
                </a>
            </div>
        </form>
    </div>
</div>

<!-- قائمة النسخ الاحتياطية -->
<div class="backup-card">
    <div class="backup-card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            النسخ الاحتياطية
            {% if page_obj.paginator.count %}
                <span class="badge bg-primary">{{ page_obj.paginator.count }}</span>
            {% endif %}
        </h5>
    </div>
    <div class="backup-card-body">
        {% if page_obj %}
            <div class="backup-table">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>النوع</th>
                            <th>الحالة</th>
                            <th>التقدم</th>
                            <th>الحجم</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for backup in page_obj %}
                            <tr>
                                <td>
                                    <a href="{% url 'backup_system:backup_detail' backup.id %}" class="text-decoration-none">
                                        <strong>{{ backup.name }}</strong>
                                    </a>
                                    {% if backup.description %}
                                        <br><small class="text-muted">{{ backup.description|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                
                                <td>
                                    <span class="badge bg-info">{{ backup.get_backup_type_display }}</span>
                                </td>
                                
                                <td>
                                    {% if backup.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                    {% elif backup.status == 'running' %}
                                        <span class="badge bg-warning">قيد التنفيذ</span>
                                    {% elif backup.status == 'failed' %}
                                        <span class="badge bg-danger">فاشل</span>
                                    {% elif backup.status == 'pending' %}
                                        <span class="badge bg-secondary">في الانتظار</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ backup.get_status_display }}</span>
                                    {% endif %}
                                </td>
                                
                                <td>
                                    {% if backup.status == 'running' %}
                                        <div class="progress position-relative" style="width: 100px; height: 20px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ backup.progress_percentage }}%">
                                            </div>
                                            <div class="progress-text">
                                                {{ backup.progress_percentage|floatformat:0 }}%
                                            </div>
                                        </div>
                                    {% elif backup.status == 'completed' %}
                                        <span class="text-success">100%</span>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                
                                <td class="file-size">
                                    {% if backup.status == 'completed' %}
                                        {{ backup.compressed_size_display }}
                                        {% if backup.compression_ratio > 0 %}
                                            <br><small class="text-muted">ضغط {{ backup.compression_ratio|floatformat:1 }}%</small>
                                        {% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                
                                <td>
                                    <span data-time="{{ backup.created_at|date:'c' }}">
                                        {{ backup.created_at|date:"Y-m-d H:i" }}
                                    </span>
                                    <br><small class="text-muted">{{ backup.created_by.get_full_name|default:backup.created_by.username }}</small>
                                </td>
                                
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'backup_system:backup_detail' backup.id %}" 
                                           class="btn btn-outline-primary" title="التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        {% if backup.status == 'completed' %}
                                            <a href="{% url 'backup_system:backup_download' backup.id %}" 
                                               class="btn btn-outline-success" title="تحميل">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            
                                            <a href="{% url 'backup_system:restore_from_backup' backup.id %}" 
                                               class="btn btn-outline-info" title="استعادة">
                                                <i class="fas fa-undo"></i>
                                            </a>
                                        {% endif %}
                                        
                                        <a href="{% url 'backup_system:backup_delete' backup.id %}" 
                                           class="btn btn-outline-danger" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if status_filter %}&status={{ status_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
            
        {% else %}
            <div class="text-center text-muted py-5">
                <i class="fas fa-inbox fa-4x mb-3"></i>
                <h4>لا توجد نسخ احتياطية</h4>
                <p>لم يتم إنشاء أي نسخ احتياطية بعد</p>
                <a href="{% url 'backup_system:backup_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إنشاء أول نسخة احتياطية
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث الصفحة كل 30 ثانية للنسخ قيد التنفيذ
{% if page_obj %}
    {% for backup in page_obj %}
        {% if backup.status == 'running' %}
            setTimeout(() => {
                location.reload();
            }, 30000);
            break;
        {% endif %}
    {% endfor %}
{% endif %}
</script>
{% endblock %}