{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'odoo_db_manager/css/style.css' %}">
<style>
    /* أنماط نظام النسخ الاحتياطي - متوافقة مع الهوية البصرية */
    .backup-system-container {
        background-color: var(--bg-color, #f8f9fa);
        min-height: calc(100vh - 200px);
    }
    
    .backup-toolbar {
        background: var(--card-bg, #fff);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid var(--border-color, #dee2e6);
    }
    
    .backup-toolbar h1 {
        color: var(--text-color, #333);
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
    }
    
    .backup-card {
        background: var(--card-bg, #fff);
        border: 1px solid var(--border-color, #dee2e6);
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
    }
    
    .backup-card-header {
        background: var(--header-bg, #f8f9fa);
        border-bottom: 1px solid var(--border-color, #dee2e6);
        padding: 1rem;
        border-radius: 8px 8px 0 0;
        font-weight: 600;
        color: var(--text-color, #333);
    }
    
    .backup-card-body {
        padding: 1.5rem;
    }
    
    .stats-card {
        background: var(--card-bg, #fff);
        border: 1px solid var(--border-color, #dee2e6);
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        transition: transform 0.2s ease;
        border-left: 4px solid;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .stats-card.primary {
        border-left-color: #007bff;
    }
    
    .stats-card.success {
        border-left-color: #28a745;
    }
    
    .stats-card.danger {
        border-left-color: #dc3545;
    }
    
    .stats-card.warning {
        border-left-color: #ffc107;
    }
    
    .stats-card h2 {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 0.5rem 0;
        color: var(--text-color, #333);
    }
    
    .stats-card h5 {
        color: var(--text-muted, #6c757d);
        margin-bottom: 0.5rem;
    }
    
    .backup-table {
        background: var(--card-bg, #fff);
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .backup-table .table {
        margin: 0;
    }
    
    .backup-table .table th {
        background: var(--header-bg, #f8f9fa);
        border-top: none;
        font-weight: 600;
        color: var(--text-color, #333);
        padding: 1rem;
    }
    
    .backup-table .table td {
        padding: 1rem;
        vertical-align: middle;
        border-color: var(--border-color, #dee2e6);
        color: var(--text-color, #333);
    }
    
    .progress {
        height: 1.5rem;
        border-radius: 0.375rem;
        background-color: var(--progress-bg, #e9ecef);
    }
    
    .progress-text {
        position: absolute;
        width: 100%;
        text-align: center;
        line-height: 1.5rem;
        color: #fff;
        font-weight: 600;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        z-index: 1;
    }
    
    .file-size {
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        color: var(--text-muted, #6c757d);
    }
    
    .btn-group .btn {
        border-radius: 0.375rem;
        margin: 0 2px;
    }
    
    .alert {
        border-radius: 8px;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* تحسينات للثيم المظلم */
    [data-theme="modern-black"] .backup-system-container {
        background-color: var(--bg-color);
    }
    
    [data-theme="modern-black"] .backup-card,
    [data-theme="modern-black"] .backup-toolbar,
    [data-theme="modern-black"] .backup-table {
        background: var(--card-bg);
        border-color: var(--border-color);
    }
    
    [data-theme="modern-black"] .backup-card-header {
        background: var(--header-bg);
    }
    
    /* تحسينات الاستجابة */
    @media (max-width: 768px) {
        .backup-toolbar {
            padding: 0.75rem;
        }
        
        .backup-toolbar h1 {
            font-size: 1.25rem;
        }
        
        .stats-card {
            margin-bottom: 1rem;
        }
        
        .stats-card h2 {
            font-size: 2rem;
        }
        
        .backup-table .table th,
        .backup-table .table td {
            padding: 0.75rem 0.5rem;
            font-size: 0.875rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="backup-system-container">
    <div class="container-fluid">
        <!-- شريط الأدوات العلوي -->
        <div class="backup-toolbar">
            <div class="d-flex justify-content-between align-items-center">
                <h1>
                    <i class="fas fa-database me-2"></i>
                    {{ title }}
                </h1>
                {% block page_actions %}{% endblock %}
            </div>
        </div>
        
        <!-- محتوى الصفحة -->
        {% block backup_content %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // دالة لتنسيق حجم الملف
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    // دالة لتنسيق المدة
    function formatDuration(seconds) {
        if (!seconds) return '-';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}س ${minutes}د ${secs}ث`;
        } else if (minutes > 0) {
            return `${minutes}د ${secs}ث`;
        } else {
            return `${secs}ث`;
        }
    }
    
    // تحديث الوقت النسبي
    function updateRelativeTime() {
        document.querySelectorAll('[data-time]').forEach(function(element) {
            const time = new Date(element.getAttribute('data-time'));
            const now = new Date();
            const diff = Math.floor((now - time) / 1000);
            
            let relative;
            if (diff < 60) {
                relative = 'منذ لحظات';
            } else if (diff < 3600) {
                relative = `منذ ${Math.floor(diff / 60)} دقيقة`;
            } else if (diff < 86400) {
                relative = `منذ ${Math.floor(diff / 3600)} ساعة`;
            } else {
                relative = `منذ ${Math.floor(diff / 86400)} يوم`;
            }
            
            element.textContent = relative;
        });
    }
    
    // تحديث الوقت كل دقيقة
    setInterval(updateRelativeTime, 60000);
    updateRelativeTime();
</script>
{% endblock %}