{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ customer.name }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Customer Header with Enhanced Design -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm {% if is_cross_branch %}cross-branch-customer-detail{% endif %}">
                {% if is_cross_branch %}
                <div class="alert alert-warning mb-0 border-0 rounded-top" style="border-radius: 0.375rem 0.375rem 0 0 !important;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> هذا العميل من فرع آخر ({{ customer.branch.name }})
                        <span class="ms-auto">
                            <i class="fas fa-info-circle me-1"></i>
                            يمكنك عرض البيانات وإ��شاء طلبات مرتبطة بفرعك ({{ user_branch.name }})
                        </span>
                    </div>
                </div>
                {% endif %}
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            {% if customer.image %}
                            <img src="{{ customer.image.url }}" alt="{{ customer.name }}"
                                 class="img-fluid rounded-circle border border-3 border-primary"
                                 style="max-width: 120px; max-height: 120px;">
                            {% else %}
                            <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center"
                                 style="width: 120px; height: 120px;">
                                <i class="fas fa-user fa-3x text-white"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-8">
                            <h2 class="mb-1">
                                <i class="fas fa-user-tie text-primary"></i> {{ customer.name }}
                                <small class="text-muted fs-6">#{{ customer.code }}</small>
                                {% if is_cross_branch %}
                                    <span class="badge bg-warning text-dark ms-2">
                                        <i class="fas fa-exchange-alt"></i> فرع آخر
                                    </span>
                                {% endif %}
                            </h2>
                            <div class="mb-3">
                                <span class="badge {% if customer.status == 'active' %}bg-success
                                       {% elif customer.status == 'inactive' %}bg-warning text-dark
                                       {% else %}bg-danger{% endif %} me-2">
                                    <i class="fas fa-circle me-1"></i>{{ customer.get_status_display }}
                                </span>
                                <span class="badge bg-info me-2">
                                    <i class="fas fa-tag me-1"></i>{{ customer.get_customer_type_display }}
                                </span>
                                {% if customer.category %}
                                <span class="badge bg-secondary me-2">
                                    <i class="fas fa-folder me-1"></i>{{ customer.category.name }}
                                </span>
                                {% endif %}
                                {% if customer.branch %}
                                <span class="badge {% if is_cross_branch %}bg-warning text-dark{% else %}bg-dark{% endif %}">
                                    <i class="fas fa-building me-1"></i>{{ customer.branch.name }}
                                    {% if is_cross_branch %}
                                        <small class="d-block">فرع العميل</small>
                                    {% endif %}
                                </span>
                                {% endif %}
                            </div>
                            <div class="d-flex flex-wrap gap-3">
                                {% if customer.phone %}
                                <div>
                                    <small class="text-muted d-block">{% trans 'الهاتف الأساسي' %}</small>
                                    <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                        <i class="fas fa-phone text-success"></i> {{ customer.phone }}
                                    </a>
                                </div>
                                {% endif %}
                                {% if customer.email %}
                                <div>
                                    <small class="text-muted d-block">{% trans 'البريد الإلكتروني' %}</small>
                                    <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                        <i class="fas fa-envelope text-primary"></i> {{ customer.email }}
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-2 text-end">
                            <div class="btn-group-vertical d-grid gap-2">
                                <a href="{% url 'orders:order_create' %}?customer={{ customer.code }}" class="btn btn-success btn-sm">
                                    <i class="fas fa-plus"></i> {% trans 'إنشاء طلب' %}
                                    {% if is_cross_branch %}
                                        <small class="d-block">مرتبط بفرعك</small>
                                    {% endif %}
                                </a>
                                {% if can_edit %}
                                    <a href="{% url 'customers:customer_update' customer.pk %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i> {% trans 'تعديل' %}
                                    </a>
                                {% else %}
                                    {% if is_cross_branch %}
                                        <button class="btn btn-secondary btn-sm disabled" title="لا يمكن التعديل - عميل من فرع آخر">
                                            <i class="fas fa-lock"></i> {% trans 'محظور التعديل' %}
                                        </button>
                                    {% endif %}
                                {% endif %}
                                {% if perms.customers.delete_customer and not is_cross_branch %}
                                    <a href="{% url 'customers:customer_delete' customer.pk %}" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash"></i> {% trans 'حذف' %}
                                    </a>
                                {% endif %}
                                <a href="{% url 'customers:customer_list' %}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-arrow-left"></i> {% trans 'العودة' %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Customer Information -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans 'معلومات العميل' %}</h5>
                </div>
                <div class="card-body">
                    {% if customer.image %}
                    <div class="text-center mb-3">
                        <img src="{{ customer.image.url }}" alt="{{ customer.name }}"
                             class="img-fluid rounded-circle" style="max-width: 150px;">
                    </div>
                    {% endif %}                    <table class="table table-sm">
                        <tr>
                            <th>{% trans 'كود العميل' %}:</th>
                            <td><strong>{{ customer.code }}</strong></td>
                        </tr>
                        <tr>
                            <th>{% trans 'نوع العميل' %}:</th>
                            <td>
                                <span class="badge bg-info">{{ customer.get_customer_type_display }}</span>
                            </td>
                        </tr>
                        {% if customer.category %}
                        <tr>
                            <th>{% trans 'تصنيف العميل' %}:</th>
                            <td>
                                <span class="badge bg-secondary">{{ customer.category.name }}</span>
                            </td>
                        </tr>
                        {% endif %}
                        <tr>
                            <th>{% trans 'الحالة' %}:</th>
                            <td>
                                <span class="badge {% if customer.status == 'active' %}bg-success
                                       {% elif customer.status == 'inactive' %}bg-warning text-dark
                                       {% else %}bg-danger{% endif %}">
                                    {{ customer.get_status_display }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>{% trans 'الهاتف' %}:</th>
                            <td>
                                <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                    <i class="fas fa-phone text-success"></i> {{ customer.phone }}
                                </a>
                            </td>
                        </tr>
                        {% if customer.phone2 %}
                        <tr>
                            <th>{% trans 'الهاتف الثاني' %}:</th>
                            <td>
                                <a href="tel:{{ customer.phone2 }}" class="text-decoration-none">
                                    <i class="fas fa-phone text-success"></i> {{ customer.phone2 }}
                                </a>
                            </td>
                        </tr>
                        {% endif %}
                        {% if customer.email %}
                        <tr>
                            <th>{% trans 'البريد الإلكتروني' %}:</th>
                            <td>
                                <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                    <i class="fas fa-envelope text-primary"></i> {{ customer.email }}
                                </a>
                            </td>
                        </tr>
                        {% endif %}
                        {% if customer.birth_date %}
                        <tr>
                            <th>{% trans 'تاريخ الميلاد' %}:</th>
                            <td>
                                <i class="fas fa-birthday-cake text-warning"></i> 
                                {{ customer.birth_date|date:"Y-m-d" }}
                                <small class="text-muted">{{ customer.birth_date|date:"Y-m-d" }}</small>
                            </td>
                        </tr>
                        {% endif %}
                        <tr>
                            <th>{% trans 'العنوان' %}:</th>
                            <td>
                                <i class="fas fa-map-marker-alt text-danger"></i> {{ customer.address }}
                            </td>
                        </tr>
                        {% if customer.branch %}
                        <tr>
                            <th>{% trans 'الفرع' %}:</th>
                            <td>
                                <i class="fas fa-building text-info"></i> {{ customer.branch.name }}
                            </td>
                        </tr>
                        {% endif %}
                        <tr>
                            <th>{% trans 'تاريخ الإنشاء' %}:</th>
                            <td>
                                <i class="fas fa-calendar-alt text-info"></i> {{ customer.created_at|date:"Y-m-d H:i" }}
                            </td>
                        </tr>
                        {% if customer.created_by %}
                        <tr>
                            <th>{% trans 'أنشئ بواسطة' %}:</th>
                            <td>
                                <i class="fas fa-user text-primary"></i> {{ customer.created_by.get_full_name|default:customer.created_by.username }}
                            </td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
              <!-- Customer Interests -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-heart text-primary me-2"></i>
                        اهتمامات العميل
                    </h5>
                </div>
                <div class="card-body">
                    {% if customer.interests %}
                        <p class="card-text">{{ customer.interests|linebreaks }}</p>
                    {% else %}
                        <p class="text-muted">لم يتم تحديد اهتمامات للعميل</p>
                    {% endif %}
                </div>
            </div>

            <!-- Customer General Notes -->
            {% if customer.notes %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sticky-note text-warning"></i> {% trans 'ملاحظات عامة' %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="p-3 bg-light rounded">
                        {{ customer.notes|linebreaks }}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Notes & History -->
        <div class="col-md-8">
            <!-- Add Note Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans 'إضافة ملاحظة' %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'customers:add_note' customer.pk %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            {{ note_form.note }}
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> {% trans 'إضافة' %}
                        </button>
                    </form>
                </div>
            </div>

            <!-- Notes History -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans 'سجل الملاحظات' %}</h5>
                </div>
                <div class="card-body">
                    {% if customer.notes_history.all %}
                    <div class="timeline">
                        {% for note in customer.notes_history.all %}
                        <div class="timeline-item">
                            <div class="timeline-date">
                                {{ note.created_at|date:"Y-m-d H:i" }}
                            </div>
                            <div class="timeline-content">
                                <p>{{ note.note }}</p>
                                <small class="text-muted">
                                    {% trans 'بواسطة' %}:
                                    {% if note.created_by %}
                                        {{ note.created_by.get_full_name|default:note.created_by.username }}
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">{% trans 'لا توجد ملاحظات' %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Orders & Inspections -->
    <div class="row mt-4">
        <!-- Recent Orders -->
        <div class="col-md-6">
            <div class="card" style="font-size: 0.93em; border-radius: 10px; box-shadow: 0 2px 6px rgba(0,0,0,0.04);">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">{% trans 'آخر الطلبات' %}</h5>
                    <a href="{% url 'orders:order_list' %}?customer={{ customer.code }}" class="btn btn-sm btn-outline-primary">
                        {% trans 'عرض الكل' %}
                    </a>
                </div>
                <div class="card-body" style="padding: 0.7rem 0.7rem 0.5rem 0.7rem;">
                    {% if orders %}
                    <div class="table-responsive">
                        <table class="table table-sm" id="orders-table-customer-detail">
                            <thead>
                                <tr>
                                    <th style="width: 60px; min-width: 60px; text-align: center;">رقم الطلب</th>
                                    <th style="min-width: 100px; text-align: center;">التاريخ</th>
                                    <th style="min-width: 120px; text-align: center;">النوع</th>
                                    <th style="min-width: 120px; text-align: center;">الحالة</th>
                                    <th style="min-width: 90px; text-align: center;">القيمة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 60px; text-align: center; font-weight: bold; font-size: 1em;">
                                        <a href="{% url 'orders:order_detail' order.pk %}" tabindex="0" class="order-popover" data-bs-toggle="popover" data-bs-trigger="hover focus" data-bs-html="true" data-bs-content="<b>رقم العقد:</b> {{ order.contract_number|default:'-' }}{% if order.contract_number_2 %}<br/><b>عقد إضافي 2:</b> {{ order.contract_number_2 }}{% endif %}{% if order.contract_number_3 %}<br/><b>عقد إضافي 3:</b> {{ order.contract_number_3 }}{% endif %}<br/><b>البائع:</b> {{ order.salesperson.name|default:'-' }}<br/><b>تاريخ الطلب:</b> {{ order.order_date|date:'Y-m-d H:i' }}<br/><b>حالة التصنيع:</b> {% if order.manufacturing_order %}{{ order.manufacturing_order.get_status_display }}{% else %}-{% endif %}">>
                                            {{ order.order_number }}
                                        </a>
                                    </td>
                                    <td style="white-space: nowrap; max-width: 100px; text-align: center;">{{ order.order_date|date:"Y-m-d" }}</td>
                                    <td style="white-space: nowrap; max-width: 140px; text-align: center;">
                                        {% if order.order_type == 'product' %}
                                            <span class="badge bg-primary">منتج</span>
                                        {% elif order.order_type == 'service' %}
                                            <span class="badge bg-info">خدمة</span>
                                            {% for service_type in order.service_types %}
                                                {% if service_type == 'installation' %}
                                                    <span class="badge bg-secondary">تركيب</span>
                                                {% elif service_type == 'inspection' %}
                                                    <span class="badge bg-warning">معاينة</span>
                                                {% elif service_type == 'transport' %}
                                                    <span class="badge bg-dark">نقل</span>
                                                {% elif service_type == 'tailoring' %}
                                                    <span class="badge bg-success">تسليم</span>
                                                {% endif %}
                                            {% endfor %}
                                        {% else %}
                                            <span class="badge bg-secondary">طلب</span>
                                            {% for selected_type in order.selected_types %}
                                                {% if selected_type == 'fabric' %}
                                                    <span class="badge bg-primary">قماش</span>
                                                {% elif selected_type == 'accessory' %}
                                                    <span class="badge bg-info">إكسسوار</span>
                                                {% elif selected_type == 'installation' %}
                                                    <span class="badge bg-secondary">تركيب</span>
                                                {% elif selected_type == 'inspection' %}
                                                    <span class="badge bg-warning">معاينة</span>
                                                {% elif selected_type == 'transport' %}
                                                    <span class="badge bg-dark">نقل</span>
                                                {% elif selected_type == 'tailoring' %}
                                                    <span class="badge bg-success">تسليم</span>
                                                {% endif %}
                                            {% endfor %}
                                        {% endif %}
                                    </td>
                                    <td style="white-space: nowrap; max-width: 140px; text-align: center;">
                                        <span class="badge {{ order.get_display_status_badge_class }}" style="font-size: 0.95em; padding: 0.35em 0.7em;">
                                            <i class="{{ order.get_display_status_icon }} me-1"></i>
                                            {{ order.get_display_status_text }}
                                        </span>
                                    </td>
                                    <td style="white-space: nowrap; max-width: 90px; text-align: center; font-weight: 600;">{{ order.total|default:'-' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted">{% trans 'لا توجد طلبات' %}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Inspections -->
        <div class="col-md-6">
            <div class="card" style="font-size: 0.93em; border-radius: 10px; box-shadow: 0 2px 6px rgba(0,0,0,0.04);">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">{% trans 'آخر المعاينات' %}</h5>
                    <a href="{% url 'inspections:inspection_list' %}?q={{ customer.code }}" class="btn btn-sm btn-outline-success">
                        {% trans 'عرض الكل' %}
                    </a>
                </div>
                <div class="card-body" style="padding: 0.7rem 0.7rem 0.5rem 0.7rem;">
                    {% if inspections %}
                    <div class="table-responsive">
                        <table class="table table-sm" id="inspections-table-customer-detail">
                            <thead>
                                <tr>
                                    <th style="width: 80px; min-width: 80px; text-align: center;">كود المعاينة</th>
                                    <th style="min-width: 100px; text-align: center;">التاريخ</th>
                                    <th style="min-width: 120px; text-align: center;">الحالة</th>
                                    <th style="min-width: 100px; text-align: center;">الموعد</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for inspection in inspections %}
                                <tr onclick="window.location.href='{% url 'inspections:inspection_detail_by_code' inspection_code=inspection.inspection_code %}'" style="cursor: pointer;">
                                    <td style="text-align: center;">{{ inspection.inspection_code|default:"--" }}</td>
                                    <td style="text-align: center;">{{ inspection.created_at|date:"Y-m-d" }}</td>
                                    <td style="text-align: center;">
                                        <span class="status-label status-{{ inspection.status }}">
                                            {{ inspection.get_status_display }}
                                        </span>
                                    </td>
                                    <td style="text-align: center;">
                                        {% if inspection.scheduled_date %}
                                            {{ inspection.scheduled_date|date:"Y-m-d" }}
                                        {% else %}
                                            --
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted">{% trans 'لا توجد معاينات' %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .timeline {
        position: relative;
        padding: 20px 0;
    }

    .timeline-item {
        padding: 20px 30px;
        border-left: 2px solid #e9ecef;
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-item:before {
        content: '';
        position: absolute;
        left: -7px;
        top: 24px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007bff;
    }

    .timeline-date {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    .timeline-content {
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
    }

    .card-header {
        border-bottom: 2px solid var(--primary);
    }

    .card-title {
        font-size: 1.1rem;
        font-weight: 600;
    }

    .card-text {
        white-space: pre-line;
        color: var(--dark);
    }

    .text-muted {
        font-style: italic;
    }

    .cross-branch-customer-detail {
        border-left: 4px solid #ffc107 !important;
    }

    .cross-branch-customer-detail .alert-warning {
        background-color: #fff3cd;
        border-color: #ffc107;
        color: #856404;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var popoverTriggerList = [].slice.call(document.querySelectorAll('.order-popover'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl, {
                container: 'body',
                placement: 'auto',
                trigger: 'hover focus',
                html: true
            });
        });
    });
</script>
{% endblock %}

<style>
#orders-table td, #orders-table th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
}
.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
    align-items: center;
}
</style>
