{% extends 'base.html' %}

{% block title %}قائمة العملاء - نظام الخواجه{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">قائمة العملاء</h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'customers:customer_create' %}" class="btn" style="background-color: var(--primary); color: white;">
                <i class="fas fa-plus"></i> إضافة عميل جديد
            </a>
        </div>
    </div>

    <!-- Search Form -->
    <div class="card mb-4" style="border-color: var(--neutral);">
        <div class="card-body">
            <form method="get" action="{% url 'customers:customer_list' %}">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="input-group">
                            {{ form.search }}
                            <button type="submit" class="btn" style="background-color: var(--primary); color: white;">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">                    <div class="col-md-3 mb-2">
                        <label for="{{ form.category.id_for_label }}" class="form-label">التصنيف</label>
                        {{ form.category }}
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="{{ form.branch.id_for_label }}" class="form-label">الفرع</label>
                        {{ form.branch }}
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="{{ form.customer_type.id_for_label }}" class="form-label">نوع العميل</label>
                        {{ form.customer_type }}
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="{{ form.status.id_for_label }}" class="form-label">الحالة</label>
                        {{ form.status }}
                    </div>
                    <div class="col-md-3 mb-2 d-flex align-items-end">
                        <a href="{% url 'customers:customer_list' %}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Customers List -->
    <div class="card" style="border-color: var(--neutral);">
        <div class="card-header" style="background-color: var(--primary); color: white;">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-0">العملاء ({{ total_customers }})</h5>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-striped table-hover customers-table">                    <thead>
                        <tr>                            <th>#</th>
                            <th>كود العميل</th>
                            <th>اسم العميل</th>
                            <th>نوع العميل</th>
                            <th>التصنيف</th>
                            <th>الفرع</th>
                            <th>رقم الهاتف</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in page_obj %}
                        <tr {% if customer.pk in cross_branch_customers %}class="cross-branch-customer"{% endif %}>
                            <td>{{ forloop.counter }}</td>
                            <td>{{ customer.code|default:"-" }}</td>
                            <td>
                                <a href="{% url 'customers:customer_detail' customer.pk %}" style="color: var(--primary); text-decoration: none; font-weight: bold;">
                                    <strong>{{ customer.name }}</strong>
                                </a>
                                {% if customer.pk in cross_branch_customers %}
                                    <span class="badge bg-warning text-dark ms-1" title="عميل من فرع آخر">
                                        <i class="fas fa-exchange-alt"></i> فرع آخر
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ customer.get_customer_type_display }}</span>
                            </td>
                            <td>
                                {% if customer.category %}
                                    <span class="badge bg-secondary">{{ customer.category.name }}</span>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.branch %}
                                    {{ customer.branch.name }}
                                    {% if customer.pk in cross_branch_customers %}
                                        <small class="text-muted d-block">
                                            <i class="fas fa-info-circle"></i> فرع العميل
                                        </small>
                                    {% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>{{ customer.phone }}</td>
                            <td>{{ customer.created_at|date:"Y-m-d" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm action-buttons">
                                    <a href="{% url 'customers:customer_detail' customer.pk %}" class="btn btn-info" title="عرض" style="font-size: 0.7em; padding: 0.15rem 0.3rem;"><i class="fas fa-eye" style="font-size: 0.7em;"></i></a>
                                    
                                    {% if customer.pk in cross_branch_customers %}
                                        <!-- أزرار خاصة للعملاء من فروع أخرى -->
                                        <a href="{% url 'orders:order_create' %}?customer_id={{ customer.pk }}" class="btn btn-success" title="إنشاء طلب" style="font-size: 0.7em; padding: 0.15rem 0.3rem;">
                                            <i class="fas fa-plus" style="font-size: 0.7em;"></i>
                                        </a>
                                        <span class="btn btn-secondary disabled" title="لا يمكن التعديل - عميل من فرع آخر" style="font-size: 0.7em; padding: 0.15rem 0.3rem;">
                                            <i class="fas fa-lock" style="font-size: 0.7em;"></i>
                                        </span>
                                    {% else %}
                                        <!-- أزرار عادية للعملاء من نفس الفرع -->
                                        <a href="{% url 'customers:customer_update' customer.pk %}" class="btn btn-primary" title="تعديل" style="font-size: 0.7em; padding: 0.15rem 0.3rem;"><i class="fas fa-edit" style="font-size: 0.7em;"></i></a>
                                        <a href="{% url 'customers:customer_delete' customer.pk %}" class="btn btn-danger" title="حذف" style="font-size: 0.7em; padding: 0.15rem 0.3rem;"><i class="fas fa-trash" style="font-size: 0.7em;"></i></a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_value %}&category={{ category_value }}{% endif %}{% if customer_type_value %}&customer_type={{ customer_type_value }}{% endif %}{% if status_value %}&status={{ status_value }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_value %}&category={{ category_value }}{% endif %}{% if customer_type_value %}&customer_type={{ customer_type_value }}{% endif %}{% if status_value %}&status={{ status_value }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item"><a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_value %}&category={{ category_value }}{% endif %}{% if customer_type_value %}&customer_type={{ customer_type_value }}{% endif %}{% if status_value %}&status={{ status_value }}{% endif %}">{{ num }}</a></li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_value %}&category={{ category_value }}{% endif %}{% if customer_type_value %}&customer_type={{ customer_type_value }}{% endif %}{% if status_value %}&status={{ status_value }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_value %}&category={{ category_value }}{% endif %}{% if customer_type_value %}&customer_type={{ customer_type_value }}{% endif %}{% if status_value %}&status={{ status_value }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x mb-3" style="color: var(--neutral);"></i>
                <h4>لا يوجد عملاء</h4>
                <p class="text-muted">لم يتم العثور على أي عملاء. يمكنك إضافة عميل جديد من خلال الزر أعلاه.</p>
                {% if search_query %}
                <a href="{% url 'customers:customer_list' %}" class="btn btn-outline-secondary mt-3">
                    <i class="fas fa-redo"></i> إعادة تعيين البحث
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    const searchForm = document.querySelector('form[method="get"]');
    let searchTimeout;

    // إضافة مؤشر البحث
    function showSearchIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'search-indicator';
        indicator.className = 'alert alert-info mt-2';
        indicator.innerHTML = '<i class="fas fa-search fa-spin me-2"></i>جاري البحث...';
        
        const existingIndicator = document.getElementById('search-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }
        
        searchForm.appendChild(indicator);
    }

    // إزالة مؤشر البحث
    function hideSearchIndicator() {
        const indicator = document.getElementById('search-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    // البحث التلقائي عند كتابة رقم الهاتف
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const searchValue = this.value.trim();
            
            // التحقق من أن المدخل يشبه رقم هاتف
            const isPhoneNumber = /^[\d\+\-\s]+$/.test(searchValue) && searchValue.length >= 3;
            
            if (isPhoneNumber && searchValue.length >= 7) {
                searchTimeout = setTimeout(() => {
                    showSearchIndicator();
                    
                    // إرسال طلب AJAX للبحث
                    fetch(`/customers/api/find-by-phone/?phone=${encodeURIComponent(searchValue)}`)
                        .then(response => response.json())
                        .then(data => {
                            hideSearchIndicator();
                            
                            if (data.found && data.customers.length > 0) {
                                showSearchResults(data.customers);
                            } else {
                                hideSearchResults();
                            }
                        })
                        .catch(error => {
                            hideSearchIndicator();
                            console.error('خطأ في البحث:', error);
                        });
                }, 1000); // زيادة الوقت إلى ثانية واحدة لتقليل الطلبات
            } else {
                hideSearchResults();
            }
        });
    }

    // عرض نتائج البحث
    function showSearchResults(customers) {
        let resultsHtml = '<div id="search-results" class="alert alert-success mt-2">';
        resultsHtml += '<h6><i class="fas fa-users me-2"></i>نتائج البحث السريع:</h6>';
        
        customers.forEach(customer => {
            const crossBranchBadge = customer.is_cross_branch ? 
                '<span class="badge bg-warning text-dark ms-1"><i class="fas fa-exchange-alt"></i> فرع آخر</span>' : '';
            
            resultsHtml += `
                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                    <div>
                        <strong>${customer.name}</strong> ${crossBranchBadge}
                        <br>
                        <small class="text-muted">
                            <i class="fas fa-phone"></i> ${customer.phone}
                            <i class="fas fa-building ms-2"></i> ${customer.branch}
                        </small>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <a href="${customer.url}" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> عرض
                        </a>
                        <a href="/orders/create/?customer_id=${customer.id}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> طلب
                        </a>
                    </div>
                </div>
            `;
        });
        
        resultsHtml += '</div>';
        
        // إزالة النتائج السابقة وإضافة الجديدة
        hideSearchResults();
        searchForm.insertAdjacentHTML('afterend', resultsHtml);
    }

    // إخفاء نتائج البحث
    function hideSearchResults() {
        const results = document.getElementById('search-results');
        if (results) {
            results.remove();
        }
    }

    // إخفاء النتائج عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!searchForm.contains(e.target)) {
            hideSearchResults();
        }
    });
});
</script>
{% endblock %}

<style>
.action-buttons {
    display: flex;
    gap: 0.1rem;
    justify-content: center;
    align-items: center;
}

.cross-branch-customer {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107;
}

.cross-branch-customer:hover {
    background-color: #ffeaa7 !important;
}

.cross-branch-customer td {
    position: relative;
}

.cross-branch-customer::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: #ffc107;
}

/* تحسين مظهر البادج */
.badge {
    font-size: 0.75em;
}

/* تحسين مظهر الأزرار */
.btn-group-sm .btn {
    border-radius: 0.25rem;
    margin: 0 1px;
}

/* إضافة تأثير hover للصفوف */
.customers-table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

/* تحسين مظهر الجدول */
.customers-table {
    border-collapse: separate;
    border-spacing: 0;
}

.customers-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-align: center;
}

.customers-table td {
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
}

/* تحسين مظهر الروابط */
.customers-table a {
    transition: color 0.2s ease;
}

.customers-table a:hover {
    text-decoration: underline !important;
}
</style>

/* تحسين مظهر نتائج البحث لتقليل الوميض */
#search-results {
    transition: opacity 0.3s ease, transform 0.3s ease;
    transform: translateY(0);
}

#search-results.hiding {
    opacity: 0;
    transform: translateY(-10px);
}

#search-results.showing {
    opacity: 1;
    transform: translateY(0);
}

/* تحسين مؤشر البحث */
#search-indicator {
    transition: opacity 0.3s ease;
}
