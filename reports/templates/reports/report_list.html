{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans 'التقارير' %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>{% trans 'التقارير' %}</h2>
        <a href="{% url 'reports:report_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {% trans 'إنشاء تقرير جديد' %}
        </a>
    </div>

    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    {% if reports %}
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>{% trans 'عنوان التقرير' %}</th>
                    <th>{% trans 'نوع التقرير' %}</th>
                    <th>{% trans 'تاريخ الإنشاء' %}</th>
                    <th>{% trans 'آخر تحديث' %}</th>
                    <th>{% trans 'الإجراءات' %}</th>
                </tr>
            </thead>
            <tbody>
                {% for report in reports %}
                <tr>
                    <td>{{ report.title }}</td>
                    <td>{{ report.get_report_type_display }}</td>
                    <td>{{ report.created_at|date:"Y-m-d H:i" }}</td>
                    <td>{{ report.updated_at|date:"Y-m-d H:i" }}</td>
                    <td>
                        <div class="btn-group btn-group-sm action-buttons">
                            <a href="{% url 'reports:report_detail' report.pk %}" class="btn btn-info" title="عرض التقرير" style="font-size: 0.7em; padding: 0.15rem 0.3rem;"><i class="fas fa-eye" style="font-size: 0.7em;"></i></a>
                            <a href="{% url 'reports:report_update' report.pk %}" class="btn btn-warning" title="تعديل التقرير" style="font-size: 0.7em; padding: 0.15rem 0.3rem;"><i class="fas fa-edit" style="font-size: 0.7em;"></i></a>
                            <a href="{% url 'reports:report_delete' report.pk %}" class="btn btn-danger" title="حذف التقرير" style="font-size: 0.7em; padding: 0.15rem 0.3rem;"><i class="fas fa-trash" style="font-size: 0.7em;"></i></a>
                            <a href="{% url 'reports:schedule_create' report.pk %}" class="btn btn-success" title="جدولة التقرير" style="font-size: 0.7em; padding: 0.15rem 0.3rem;"><i class="fas fa-clock" style="font-size: 0.7em;"></i></a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="alert alert-info">
        {% trans 'لا توجد تقارير حتى الآن.' %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize tooltips
        $('[title]').tooltip();
        
        // Fade out alerts after 3 seconds
        $('.alert').delay(3000).fadeOut(500);
    });
</script>
{% endblock %}

<style>
.action-buttons {
    display: flex;
    gap: 0.1rem;
    justify-content: center;
    align-items: center;
}
</style>
