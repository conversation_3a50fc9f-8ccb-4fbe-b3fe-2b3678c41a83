{% extends 'base.html' %}
{% load static %}

{% block title %}الإشعارات - نظام الخواجه{% endblock %}

{% block extra_css %}
<style>
    .notifications-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .notification-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border-left: 4px solid #dee2e6;
    }
    
    .notification-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .notification-card.unread {
        border-left-color: #007bff;
        background: linear-gradient(to right, #f8f9ff 0%, white 100%);
    }
    
    .notification-card.urgent {
        border-left-color: #dc3545;
    }
    
    .notification-card.warning {
        border-left-color: #ffc107;
    }
    
    .notification-card.success {
        border-left-color: #28a745;
    }
    
    .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: white;
    }
    
    .notification-icon.info { background-color: #007bff; }
    .notification-icon.urgent { background-color: #dc3545; }
    .notification-icon.warning { background-color: #ffc107; }
    .notification-icon.success { background-color: #28a745; }
    
    .notification-meta {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .notification-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }
    
    .filter-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #007bff;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #007bff;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
    
    .bulk-actions {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        display: none;
    }
    
    .bulk-actions.show {
        display: block;
    }
    
    .no-notifications {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .no-notifications i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'complaints:dashboard' %}">لوحة التحكم</a></li>
            <li class="breadcrumb-item active">الإشعارات</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="notifications-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-bell me-2"></i>
                    الإشعارات
                </h1>
                <p class="mb-0 opacity-75">
                    جميع الإشعارات والتنبيهات المتعلقة بالشكاوى والأنشطة
                </p>
            </div>
            <div class="col-md-4 text-end">
                <button type="button" class="btn btn-light" onclick="markAllAsRead()">
                    <i class="fas fa-check-double me-1"></i>
                    تعليم الكل كمقروء
                </button>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_notifications }}</div>
            <div class="stat-label">إجمالي الإشعارات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-primary">{{ stats.unread_notifications }}</div>
            <div class="stat-label">غير مقروءة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-danger">{{ stats.urgent_notifications }}</div>
            <div class="stat-label">عاجلة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number text-success">{{ stats.today_notifications }}</div>
            <div class="stat-label">اليوم</div>
        </div>
    </div>

    <!-- فلاتر -->
    <div class="filter-card">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="notification_type" class="form-label">نوع الإشعار</label>
                <select name="notification_type" id="notification_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="info" {% if request.GET.notification_type == 'info' %}selected{% endif %}>معلوماتي</option>
                    <option value="urgent" {% if request.GET.notification_type == 'urgent' %}selected{% endif %}>عاجل</option>
                    <option value="warning" {% if request.GET.notification_type == 'warning' %}selected{% endif %}>تحذير</option>
                    <option value="success" {% if request.GET.notification_type == 'success' %}selected{% endif %}>نجح</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select name="status" id="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="unread" {% if request.GET.status == 'unread' %}selected{% endif %}>غير مقروءة</option>
                    <option value="read" {% if request.GET.status == 'read' %}selected{% endif %}>مقروءة</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="date_range" class="form-label">الفترة الزمنية</label>
                <select name="date_range" id="date_range" class="form-select">
                    <option value="">جميع الفترات</option>
                    <option value="today" {% if request.GET.date_range == 'today' %}selected{% endif %}>اليوم</option>
                    <option value="week" {% if request.GET.date_range == 'week' %}selected{% endif %}>هذا الأسبوع</option>
                    <option value="month" {% if request.GET.date_range == 'month' %}selected{% endif %}>هذا الشهر</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter me-1"></i>
                    تطبيق الفلاتر
                </button>
            </div>
        </form>
        
        {% if request.GET %}
        <div class="mt-3">
            <a href="{% url 'complaints:notifications_list' %}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-times me-1"></i>
                إزالة الفلاتر
            </a>
        </div>
        {% endif %}
    </div>

    <!-- الإجراءات المجمعة -->
    <div class="bulk-actions" id="bulkActions">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <span id="selectedCount">0</span> إشعار محدد
            </div>
            <div>
                <button type="button" class="btn btn-sm btn-primary me-2" onclick="markSelectedAsRead()">
                    <i class="fas fa-check me-1"></i>
                    تعليم كمقروء
                </button>
                <button type="button" class="btn btn-sm btn-danger" onclick="deleteSelected()">
                    <i class="fas fa-trash me-1"></i>
                    حذف
                </button>
            </div>
        </div>
    </div>

    <!-- قائمة الإشعارات -->
    <div class="notifications-list">
        {% for notification in notifications %}
        <div class="notification-card {% if not notification.is_read %}unread{% endif %} {{ notification.notification_type }}">
            <div class="row align-items-start">
                <div class="col-auto">
                    <input type="checkbox" class="form-check-input notification-checkbox" 
                           value="{{ notification.id }}" onchange="updateBulkActions()">
                </div>
                
                <div class="col-auto">
                    <div class="notification-icon {{ notification.notification_type }}">
                        {% if notification.notification_type == 'urgent' %}
                            <i class="fas fa-exclamation-triangle"></i>
                        {% elif notification.notification_type == 'warning' %}
                            <i class="fas fa-exclamation-circle"></i>
                        {% elif notification.notification_type == 'success' %}
                            <i class="fas fa-check-circle"></i>
                        {% else %}
                            <i class="fas fa-info-circle"></i>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0 {% if not notification.is_read %}fw-bold{% endif %}">
                            {{ notification.title }}
                        </h6>
                        <div class="notification-actions">
                            {% if not notification.is_read %}
                            <span class="badge bg-primary">جديد</span>
                            {% endif %}
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                        type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    {% if not notification.is_read %}
                                    <li>
                                        <a class="dropdown-item" href="javascript:void(0)" 
                                           onclick="markAsRead({{ notification.id }})">
                                            <i class="fas fa-check me-2"></i>
                                            تعليم كمقروء
                                        </a>
                                    </li>
                                    {% endif %}
                                    <li>
                                        <a class="dropdown-item text-danger" href="javascript:void(0)" 
                                           onclick="deleteNotification({{ notification.id }})">
                                            <i class="fas fa-trash me-2"></i>
                                            حذف
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <p class="mb-2 text-muted">{{ notification.message|truncatechars:150 }}</p>
                    
                    <div class="notification-meta">
                        <div class="row">
                            <div class="col-md-6">
                                <i class="fas fa-clock me-1"></i>
                                {{ notification.created_at|date:"Y-m-d H:i" }}
                            </div>
                            <div class="col-md-6">
                                {% if notification.content_object %}
                                <a href="{% url 'complaints:complaint_detail' notification.content_object.pk %}" 
                                   class="text-decoration-none">
                                    <i class="fas fa-ticket-alt me-1"></i>
                                    شكوى {{ notification.content_object.complaint_number }}
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="no-notifications">
            <i class="fas fa-bell-slash"></i>
            <h4>لا توجد إشعارات</h4>
            <p class="text-muted">لا توجد إشعارات لعرضها في الوقت الحالي</p>
        </div>
        {% endfor %}
    </div>

    <!-- التنقل بين الصفحات -->
    {% if notifications.has_other_pages %}
    <nav aria-label="تنقل بين صفحات الإشعارات">
        <ul class="pagination justify-content-center">
            {% if notifications.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ notifications.previous_page_number }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            {% endif %}
            
            {% for num in notifications.paginator.page_range %}
            {% if notifications.number == num %}
            <li class="page-item active">
                <span class="page-link">{{ num }}</span>
            </li>
            {% elif num > notifications.number|add:'-3' and num < notifications.number|add:'3' %}
            <li class="page-item">
                <a class="page-link" href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ num }}">{{ num }}</a>
            </li>
            {% endif %}
            {% endfor %}
            
            {% if notifications.has_next %}
            <li class="page-item">
                <a class="page-link" href="?{% if request.GET %}{{ request.GET.urlencode }}&{% endif %}page={{ notifications.next_page_number }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث الإجراءات المجمعة
    function updateBulkActions() {
        const checkboxes = document.querySelectorAll('.notification-checkbox:checked');
        const bulkActions = document.getElementById('bulkActions');
        const selectedCount = document.getElementById('selectedCount');
        
        selectedCount.textContent = checkboxes.length;
        
        if (checkboxes.length > 0) {
            bulkActions.classList.add('show');
        } else {
            bulkActions.classList.remove('show');
        }
    }

    // تعليم إشعار واحد كمقروء
    function markAsRead(notificationId) {
        fetch(`/complaints/notifications/${notificationId}/mark-read/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تحديث الإشعار');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحديث الإشعار');
        });
    }

    // تعليم جميع الإشعارات كمقروءة
    function markAllAsRead() {
        if (!confirm('هل أنت متأكد من تعليم جميع الإشعارات كمقروءة؟')) {
            return;
        }
        
        fetch('/complaints/notifications/mark-all-read/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تحديث الإشعارات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحديث الإشعارات');
        });
    }

    // تعليم الإشعارات المحددة كمقروءة
    function markSelectedAsRead() {
        const checkboxes = document.querySelectorAll('.notification-checkbox:checked');
        const notificationIds = Array.from(checkboxes).map(cb => cb.value);
        
        if (notificationIds.length === 0) {
            alert('يرجى اختيار إشعارات للتحديث');
            return;
        }
        
        fetch('/complaints/notifications/mark-selected-read/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({notification_ids: notificationIds})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تحديث الإشعارات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحديث الإشعارات');
        });
    }

    // حذف إشعار واحد
    function deleteNotification(notificationId) {
        if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
            return;
        }
        
        fetch(`/complaints/notifications/${notificationId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف الإشعار');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الإشعار');
        });
    }

    // حذف الإشعارات المحددة
    function deleteSelected() {
        const checkboxes = document.querySelectorAll('.notification-checkbox:checked');
        const notificationIds = Array.from(checkboxes).map(cb => cb.value);
        
        if (notificationIds.length === 0) {
            alert('يرجى اختيار إشعارات للحذف');
            return;
        }
        
        if (!confirm(`هل أنت متأكد من حذف ${notificationIds.length} إشعار؟`)) {
            return;
        }
        
        fetch('/complaints/notifications/delete-selected/', {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({notification_ids: notificationIds})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف الإشعارات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الإشعارات');
        });
    }

    // اختيار/إلغاء اختيار جميع الإشعارات
    function toggleAllCheckboxes() {
        const checkboxes = document.querySelectorAll('.notification-checkbox');
        const selectAllCheckbox = document.getElementById('selectAll');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
        
        updateBulkActions();
    }

    // تحديث تلقائي للإشعارات كل دقيقة
    setInterval(function() {
        // يمكن إضافة AJAX call لتحديث قائمة الإشعارات
        // بدون إعادة تحميل كامل للصفحة
    }, 60000); // كل دقيقة
</script>
{% endblock %}
