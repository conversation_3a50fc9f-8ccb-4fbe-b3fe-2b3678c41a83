{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if complaint %}
        تعديل شكوى {{ complaint.complaint_number }} - نظام الخواجه
    {% else %}
        شكوى جديدة - نظام الخواجه
    {% endif %}
{% endblock %}

{% block extra_css %}
<!-- Select2 CSS for smart search -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.rtl.min.css" rel="stylesheet" />

<style>
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .form-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .form-section {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 2rem;
        margin-bottom: 2rem;
    }
    
    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .form-section-title {
        color: #495057;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #007bff;
        display: inline-block;
    }
    
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .form-help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    .customer-info-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        border-left: 3px solid #007bff;
    }
    
    .btn-action {
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        min-width: 120px;
    }
    
    .priority-preview {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-left: 10px;
    }
    
    .priority-urgent { background-color: #dc3545; }
    .priority-high { background-color: #fd7e14; }
    .priority-medium { background-color: #ffc107; }
    .priority-low { background-color: #20c997; }
    
    .related-object-card {
        background: #e3f2fd;
        border-radius: 10px;
        padding: 1rem;
        margin-top: 1rem;
        border-left: 3px solid #2196f3;
    }
    
    #id_deadline {
        min-width: 200px;
    }
    
    /* تنسيق البحث الذكي */
    .dropdown-menu {
        max-height: 300px;
        overflow-y: auto;
        z-index: 9999 !important;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .customer-search-item {
        padding: 0.75rem;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .customer-search-item:hover {
        background-color: #f8f9fa;
    }
    
    .customer-search-item:last-child {
        border-bottom: none;
    }
    
    #customerSearchResults {
        position: relative;
        z-index: 9999;
    }
    
    .position-relative {
        position: relative !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'complaints:dashboard' %}">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="{% url 'complaints:complaint_list' %}">قائمة الشكاوى</a></li>
            {% if complaint %}
                <li class="breadcrumb-item"><a href="{% url 'complaints:complaint_detail' complaint.pk %}">{{ complaint.complaint_number }}</a></li>
                <li class="breadcrumb-item active">تعديل</li>
            {% else %}
                <li class="breadcrumb-item active">شكوى جديدة</li>
            {% endif %}
        </ol>
    </nav>

    <!-- Form Header -->
    <div class="form-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-edit me-2"></i>
                    {% if complaint %}
                        تعديل شكوى {{ complaint.complaint_number }}
                    {% else %}
                        إنشاء شكوى جديدة
                    {% endif %}
                </h1>
                <p class="mb-0 opacity-75">
                    {% if complaint %}
                        قم بتعديل بيانات الشكوى وحفظ التغييرات
                    {% else %}
                        املأ البيانات المطلوبة لإنشاء شكوى جديدة
                    {% endif %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                {% if complaint %}
                <span class="badge bg-light text-dark fs-6">
                    {{ complaint.get_status_display }}
                </span>
                {% else %}
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-plus me-1"></i>
                    شكوى جديدة
                </span>
                {% endif %}
            </div>
        </div>
    </div>

    <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
        {% csrf_token %}
        
        <div class="row">
            <div class="col-md-8">
                <!-- معلومات الشكوى الأساسية -->
                <div class="form-card">
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الشكوى الأساسية
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.customer.id_for_label }}" class="form-label required-field">
                                    {{ form.customer.label }}
                                </label>
                                <select id="customer_search_select" class="form-select" style="width: 100%;">
                                    {% if complaint.customer %}
                                        <option value="{{ complaint.customer.id }}" selected>
                                            {{ complaint.customer.name }} - {{ complaint.customer.phone }}
                                        </option>
                                    {% endif %}
                                </select>
                                {{ form.customer }}
                                {% if form.customer.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.customer.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="form-help-text">ابحث عن العميل بالاسم أو الهاتف</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                                                <label for="id_complaint_type" class="form-label required-field">
                                    نوع الشكوى
                                </label>
                                {{ form.complaint_type }}
                                {% if form.complaint_type.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.complaint_type.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                                                            <label for="id_title" class="form-label required-field">
                                {{ form.title.label }}
                            </label>
                            {{ form.title }}
                            {% if form.title.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.title.errors|join:", " }}
                                </div>
                            {% endif %}
                            <div class="form-help-text">عنوان مختصر وواضح للشكوى</div>
                        </div>
                        
                        <div class="mb-3">
                                                            <label for="id_description" class="form-label required-field">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.description.errors|join:", " }}
                                </div>
                            {% endif %}
                            <div class="form-help-text">وصف تفصيلي للشكوى والمشكلة المراد حلها</div>
                        </div>
                    </div>

                    <!-- تصنيف ومعلومات إضافية -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-tags me-2"></i>
                            التصنيف والأولوية
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                                                <label for="id_priority" class="form-label required-field">
                                    الأولوية
                                </label>
                                {{ form.priority }}
                                {% if form.priority.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.priority.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div id="priority-preview" class="mt-2" style="display:none;">
                                    <span class="priority-preview"></span>
                                    <span class="priority-text"></span>
                                </div>
                                
                                <!-- عرض التاريخ والموعد النهائي -->
                                <div id="deadline-preview" class="mt-2" style="display:none;">
                                    <div class="alert alert-info p-2">
                                        <small>
                                            <i class="fas fa-clock me-1"></i>
                                            <strong>التاريخ الحالي:</strong> <span id="current-date"></span><br>
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            <strong>الموعد النهائي المتوقع:</strong> <span id="expected-deadline"></span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="id_status" class="form-label">
                                    {{ form.status.label }}
                                </label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.status.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="id_deadline" class="form-label">
                                    {{ form.deadline.label }}
                                </label>
                                {{ form.deadline }}
                                {% if form.deadline.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.deadline.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="form-help-text">تاريخ ووقت الموعد النهائي لحل الشكوى</div>
                            </div>
                            
                            {% if complaint and complaint.resolved_at %}
                            <div class="col-md-4 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                    تاريخ الحل
                                </label>
                                <div class="form-control-plaintext bg-light border rounded p-2">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ complaint.resolved_at|date:"Y-m-d H:i" }}
                                </div>
                                <div class="form-help-text text-success">تم حل الشكوى في هذا التاريخ</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- الأشخاص المسؤولون -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-users me-2"></i>
                            التعيين والمسؤولية
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="id_assigned_to" class="form-label">
                                    {{ form.assigned_to.label }}
                                </label>
                                {{ form.assigned_to }}
                                {% if form.assigned_to.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.assigned_to.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="form-help-text">الموظف المسؤول عن متابعة الشكوى</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="id_assigned_department" class="form-label">
                                    {{ form.assigned_department.label }}
                                </label>
                                {{ form.assigned_department }}
                                {% if form.assigned_department.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.assigned_department.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="form-help-text">القسم المختص بحل هذا النوع من الشكاوى</div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات مرتبطة -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-link me-2"></i>
                            ربط بسجلات أخرى (اختياري)
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.related_order.id_for_label }}" class="form-label">
                                    {{ form.related_order.label }}
                                </label>
                                {{ form.related_order }}
                                {% if form.related_order.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.related_order.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="form-help-text">اختر الطلب المرتبط بالشكوى (سيتم تحديث القائمة عند اختيار العميل)</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.content_type.id_for_label }}" class="form-label">
                                    {{ form.content_type.label }}
                                </label>
                                {{ form.content_type }}
                                {% if form.content_type.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.content_type.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="form-help-text">نوع السجل المرتبط</div>
                            </div>
                        </div>
                        
                        <div class="row" id="object-id-row" style="display:none;">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.object_id.id_for_label }}" class="form-label">
                                    {{ form.object_id.label }}
                                </label>
                                {{ form.object_id }}
                                {% if form.object_id.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.object_id.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="form-help-text">معرف السجل المحدد</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- العمود الجانبي -->
            <div class="col-md-4">
                <!-- معلومات العميل -->
                <div class="form-card">
                    <h5 class="mb-3">
                        <i class="fas fa-user me-2"></i>
                        معلومات العميل
                    </h5>
                    <div id="customer-info" class="customer-info-card" style="display:none;">
                        <div id="customer-details"></div>
                    </div>
                    <div id="no-customer-selected" class="text-muted text-center py-3">
                        <i class="fas fa-user-slash me-2"></i>
                        لم يتم اختيار عميل بعد
                    </div>
                </div>

                <!-- معلومات نوع الشكوى -->
                <div class="form-card">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات نوع الشكوى
                    </h5>
                    <div id="complaint-type-info" style="display:none;">
                        <div id="complaint-type-details"></div>
                    </div>
                    <div id="no-type-selected" class="text-muted text-center py-3">
                        <i class="fas fa-tag me-2"></i>
                        لم يتم اختيار نوع الشكوى بعد
                    </div>
                </div>

                <!-- معلومات السجل المرتبط -->
                <div class="form-card" id="related-object-card" style="display:none;">
                    <h5 class="mb-3">
                        <i class="fas fa-link me-2"></i>
                        السجل المرتبط
                    </h5>
                    <div id="related-object-info" class="related-object-card">
                        <div id="related-object-details"></div>
                    </div>
                </div>

                <!-- معلومات حالة الشكوى -->
                {% if complaint %}
                <div class="form-card">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الحالة
                    </h5>
                    <div class="customer-info-card">
                        <div class="mb-2">
                            <strong>الحالة الحالية:</strong> 
                            <span class="badge bg-{{ complaint.get_status_badge_class }} ms-1">
                                {{ complaint.get_status_display }}
                            </span>
                        </div>
                        <div class="mb-2">
                            <strong>تاريخ الإنشاء:</strong> 
                            <small class="text-muted">{{ complaint.created_at|date:"Y-m-d H:i" }}</small>
                        </div>
                        {% if complaint.deadline %}
                        <div class="mb-2">
                            <strong>الموعد النهائي:</strong> 
                            <small class="text-{% if complaint.is_overdue %}danger{% else %}primary{% endif %}">
                                {{ complaint.deadline|date:"Y-m-d H:i" }}
                            </small>
                        </div>
                        {% endif %}
                        {% if complaint.resolved_at %}
                        <div class="mb-2">
                            <strong>تاريخ الحل:</strong> 
                            <small class="text-success">{{ complaint.resolved_at|date:"Y-m-d H:i" }}</small>
                        </div>
                        <div class="mb-0">
                            <strong>مدة الحل:</strong> 
                            <small class="text-info">{{ complaint.get_resolution_time_display }}</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- أزرار الحفظ -->
                <div class="form-card">
                    <h5 class="mb-3">
                        <i class="fas fa-save me-2"></i>
                        حفظ الشكوى
                    </h5>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" name="action" value="save" class="btn btn-primary btn-action submit-btn">
                            <i class="fas fa-save me-1"></i>
                            {% if complaint %}تحديث الشكوى{% else %}حفظ الشكوى{% endif %}
                        </button>
                        
                        {% if not complaint %}
                        <button type="submit" name="action" value="save_and_new" class="btn btn-success btn-action submit-btn">
                            <i class="fas fa-plus me-1"></i>
                            حفظ وإنشاء جديدة
                        </button>
                        {% endif %}
                        
                        <a href="{% if complaint %}{% url 'complaints:complaint_detail' complaint.pk %}{% else %}{% url 'complaints:complaint_list' %}{% endif %}" 
                           class="btn btn-secondary btn-action">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                    </div>
                    
                    {% if complaint %}
                    <hr>
                    <div class="d-grid">
                        <a href="{% url 'complaints:complaint_detail' complaint.pk %}" class="btn btn-outline-info btn-action">
                            <i class="fas fa-eye me-1"></i>
                            عرض تفاصيل الشكوى
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/i18n/ar.js"></script>

<script>
    $(document).ready(function() {
        // تتبع الزر المضغوط
        $('.submit-btn').click(function() {
            $('.submit-btn').removeAttr('clicked');
            $(this).attr('clicked', 'true');
        });
        
        // تهيئة Select2 للبحث الذكي عن العملاء
        console.log('تهيئة Select2 للبحث عن العملاء...');
        $('#customer_search_select').select2({
            theme: 'bootstrap-5',
            language: 'ar',
            placeholder: 'ابحث عن العميل بالاسم أو الهاتف...',
            allowClear: true,
            minimumInputLength: 2,
            ajax: {
                url: '/complaints/ajax/customers/search/',
                dataType: 'json',
                delay: 300,
                data: function (params) {
                    console.log('إرسال طلب بحث:', params.term);
                    return {
                        q: params.term,
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    console.log('استلام نتائج البحث:', data);
                    if (data.error) {
                        console.error('خطأ في البحث:', data.error);
                        return { results: [] };
                    }
                    return {
                        results: data.results.map(function(customer) {
                            return {
                                id: customer.id,
                                text: customer.name + ' - ' + customer.phone,
                                customer: customer
                            };
                        })
                    };
                },
                cache: true
            },
            templateResult: function(customer) {
                if (customer.loading) return customer.text;
                if (!customer.customer) return customer.text;
                
                return $('<div><div class="fw-bold">' + customer.customer.name + '</div><div class="text-muted small">' + customer.customer.phone + ' - ' + customer.customer.email + '</div></div>');
            },
            templateSelection: function(customer) {
                if (!customer.customer) return customer.text;
                return customer.customer.name + ' - ' + customer.customer.phone;
            }
        }).on('select2:open', function() {
            console.log('تم فتح قائمة Select2');
        }).on('select2:close', function() {
            console.log('تم إغلاق قائمة Select2');
        });

        // عند اختيار عميل
        $('#customer_search_select').on('select2:select', function (e) {
            console.log('تم اختيار عميل:', e.params.data);
            var customerData = e.params.data.customer;
            if (customerData) {
                console.log('بيانات العميل:', customerData);
                $('#id_customer').val(customerData.id);
                console.log('تم تعيين ID العميل:', customerData.id);
                updateCustomerInfo(customerData.id);
                updateCustomerOrders(customerData.id);
            } else {
                console.error('لا توجد بيانات عميل في الاختيار');
            }
        });

        // عند إزالة اختيار العميل
        $('#customer_search_select').on('select2:clear', function (e) {
            console.log('تم إزالة اختيار العميل');
            $('#id_customer').val('');
            $('#customer-info').hide();
            $('#no-customer-selected').show();
            updateCustomerOrders(null);
        });

        // تحديث معلومات العميل عند الاختيار
        function updateCustomerInfo(customerId) {
            console.log('تحديث معلومات العميل:', customerId);
            if (customerId) {
                console.log('إرسال طلب جلب معلومات العميل...');
                $.get(`/complaints/ajax/customer-info/${customerId}/`, function(data) {
                    console.log('تم استلام معلومات العميل:', data);
                    $('#customer-details').html(`
                        <h6 class="mb-2">${data.name}</h6>
                        <p class="mb-1"><i class="fas fa-phone me-1"></i> ${data.phone}</p>
                        <p class="mb-1"><i class="fas fa-envelope me-1"></i> ${data.email}</p>
                        <p class="mb-0"><i class="fas fa-map-marker-alt me-1"></i> ${data.address}</p>
                    `);
                    $('#customer-info').show();
                    $('#no-customer-selected').hide();
                    console.log('تم تحديث عرض معلومات العميل');
                }).fail(function(xhr, status, error) {
                    console.error('خطأ في جلب معلومات العميل:');
                    console.error('Status:', status);
                    console.error('Error:', error);
                    console.error('Response:', xhr.responseText);
                    $('#customer-info').hide();
                    $('#no-customer-selected').show();
                });
            } else {
                console.log('لا يوجد معرف عميل - إخفاء المعلومات');
                $('#customer-info').hide();
                $('#no-customer-selected').show();
            }
        }
        
        // تحديث قائمة الطلبات بناءً على العميل المختار
        function updateCustomerOrders(customerId) {
            const orderSelect = $('#id_related_order');
            
            if (customerId) {
                // تعطيل حقل اختيار الطلب أثناء التحميل
                orderSelect.prop('disabled', true);
                
                // تفريغ حقل الطلب المرتبط وإزالة أي خطأ سابق
                orderSelect.empty();
                orderSelect.removeClass('is-invalid is-valid');
                $('.invalid-feedback').remove();
                
                $.get(`/complaints/ajax/customer-orders/${customerId}/`, function(data) {
                    orderSelect.empty();
                    orderSelect.append('<option value="">اختر طلب (اختياري)</option>');
                    
                    // إنشاء مجموعة لتخزين هويات الطلبات المتاحة
                    window.availableOrderIds = new Set();
                    
                    // إعادة تعيين القيمة المختارة للطلب المرتبط
                    $('#id_related_order').val('');
                    
                    if (data.orders && data.orders.length > 0) {
                        console.log(`تم العثور على ${data.orders.length} طلبات للعميل`);
                        
                        // طباعة بيانات الطلبات للتحقق
                        console.log('بيانات الطلبات المستلمة:', data.orders);
                        
                        data.orders.forEach(function(order) {
                            // حفظ هوية الطلب في المجموعة المتاحة
                            window.availableOrderIds.add(order.id.toString());
                            
                            // تحضير نص الطلب مع معلومات إضافية
                            let orderText = `${order.order_number} - ${order.created_at}`;
                            if (order.total_amount > 0) {
                                orderText += ` (${order.total_amount} ج.م)`;
                            }
                            if (order.salesperson && order.salesperson !== 'غير محدد') {
                                orderText += ` - البائع: ${order.salesperson}`;
                            }
                            if (order.created_by && order.created_by !== 'غير محدد') {
                                orderText += ` - المنشئ: ${order.created_by}`;
                            }
                            
                            const option = `<option value="${order.id}" 
                                data-salesperson="${order.salesperson || ''}"
                                data-created-by="${order.created_by || ''}"
                                data-status="${order.status || ''}"
                                data-description="${order.description || ''}"
                                title="${order.description || 'لا يوجد وصف'}">
                                ${orderText}
                            </option>`;
                            orderSelect.append(option);
                        });
                        
                        console.log('الطلبات المتاحة:', Array.from(window.availableOrderIds));
                    } else {
                        console.log('لا توجد طلبات للعميل');
                        orderSelect.append('<option value="">لا توجد طلبات لهذا العميل</option>');
                    }
                    
                    // إعادة تفعيل حقل اختيار الطلب
                    orderSelect.prop('disabled', false);
                }).fail(function(xhr, status, error) {
                    console.error('خطأ في جلب طلبات العميل:', error);
                    console.error('استجابة الخادم:', xhr.responseText);
                    orderSelect.empty();
                    orderSelect.append('<option value="">حدث خطأ في جلب الطلبات</option>');
                    orderSelect.prop('disabled', false);
                    
                    // تفريغ مجموعة الطلبات المتاحة
                    window.availableOrderIds = new Set();
                });
            } else {
                orderSelect.empty();
                orderSelect.append('<option value="">اختر عميل أولاً</option>');
                // تفريغ مجموعة الطلبات المتاحة
                window.availableOrderIds = new Set();
            }
        }

        // تحديث مؤشر الأولوية
        $('#id_priority').change(function() {
            const priority = $(this).val();
            console.log('تم تغيير الأولوية إلى:', priority);
            
            const priorityTexts = {
                'urgent': 'عاجل',
                'high': 'مرتفع',
                'medium': 'متوسط',
                'low': 'منخفض'
            };
            
            if (priority) {
                // تحديث مؤشر الأولوية
                $('.priority-preview').removeClass().addClass(`priority-preview priority-${priority}`);
                $('.priority-text').text(priorityTexts[priority]);
                $('#priority-preview').show();
                
                // تحديث التواريخ
                updateDeadlinePreview(priority);
            } else {
                $('#priority-preview').hide();
                $('#deadline-preview').hide();
            }
        });
        
        // دالة تحديث عرض الموعد النهائي
        function updateDeadlinePreview(priority) {
            const priorityHours = {
                'urgent': 4,
                'high': 24,
                'medium': 72,
                'low': 168
            };
            
            if (priority && priorityHours[priority]) {
                const now = new Date();
                const deadline = new Date();
                deadline.setHours(deadline.getHours() + priorityHours[priority]);
                
                // تنسيق التواريخ
                const currentDateStr = now.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                const deadlineStr = deadline.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                // عرض التواريخ
                $('#current-date').text(currentDateStr);
                $('#expected-deadline').text(deadlineStr + ` (بعد ${priorityHours[priority]} ساعة)`);
                $('#deadline-preview').show();
                
                // تحديث حقل الموعد النهائي
                const deadlineValue = deadline.toISOString().slice(0, 16);
                $('#id_deadline').val(deadlineValue);
                
                console.log(`تم تحديد الموعد النهائي تلقائياً: ${deadlineValue}`);
            } else {
                $('#deadline-preview').hide();
                console.log('لم يتم تحديد موعد نهائي تلقائي');
            }
        }

        // إظهار/إخفاء حقل معرف الكائن
        $('#id_content_type').change(function() {
            if ($(this).val()) {
                $('#object-id-row').show();
            } else {
                $('#object-id-row').hide();
                $('#id_object_id').val('');
            }
        });

        // تحديث معلومات السجل المرتبط
        $('#id_content_type, #id_object_id').change(function() {
            const contentType = $('#id_content_type').val();
            const objectId = $('#id_object_id').val();
            
            if (contentType && objectId) {
                // AJAX call لجلب معلومات السجل المرتبط
                $.get(`/api/related-objects/${contentType}/${objectId}/`, function(data) {
                    $('#related-object-details').html(`
                        <h6 class="mb-2">${data.title}</h6>
                        <p class="mb-1">${data.description}</p>
                        <a href="${data.url}" class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>
                            عرض السجل
                        </a>
                    `);
                    $('#related-object-card').show();
                }).fail(function() {
                    $('#related-object-card').hide();
                });
            } else {
                $('#related-object-card').hide();
            }
        });

        // تفعيل التحقق من صحة النموذج وإرسال النموذج
        $('.needs-validation').on('submit', function(e) {
            e.preventDefault();
            console.log('بدء التحقق من صحة النموذج...');
            
            const form = $(this);
            const formData = new FormData(form[0]);
            const submitBtn = $(document.activeElement);
            const originalText = submitBtn.html();
            
            // طباعة جميع البيانات للتحقق
            console.log('===== بيانات النموذج =====');
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
            }
            
            // إضافة action إذا كان موجوداً
            const submitButton = $('button[type="submit"][clicked=true]');
            if (submitButton.length) {
                formData.append('action', submitButton.val());
            }
            
            // التحقق من الطلب المرتبط إذا تم اختياره
            const relatedOrder = formData.get('related_order');
            if (relatedOrder && relatedOrder !== '') {
                if (!window.availableOrderIds || !window.availableOrderIds.has(relatedOrder)) {
                    console.error('الطلب المحدد غير صالح:', relatedOrder);
                    console.error('الطلبات المتاحة:', window.availableOrderIds ? Array.from(window.availableOrderIds) : 'غير محدد');
                    
                    alert('خطأ: الطلب المحدد غير صالح. يرجى اختيار طلب آخر أو ترك الحقل فارغاً.');
                    
                    // إزالة القيمة غير الصالحة
                    $('#id_related_order').addClass('is-invalid')
                        .after('<div class="invalid-feedback">اختر طلباً صالحاً أو اترك الحقل فارغاً</div>');
                    
                    submitBtn.prop('disabled', false).html(originalText);
                    return false;
                } else {
                    console.log('الطلب المرتبط صالح:', relatedOrder);
                    $('#id_related_order').removeClass('is-invalid').addClass('is-valid');
                }
            }
            
            // التحقق من الحقول المطلوبة
            const requiredFields = ['customer', 'title', 'complaint_type', 'priority', 'description'];
            let hasErrors = false;
            let errorMessages = [];
            
            requiredFields.forEach(function(field) {
                const element = $(`#id_${field}`);
                const value = element.val();
                console.log(`فحص ${field}:`, value);
                
                if (!value || value.trim() === '') {
                    element.addClass('is-invalid');
                    hasErrors = true;
                    errorMessages.push(`يجب ملء حقل ${field}`);
                } else {
                    element.removeClass('is-invalid').addClass('is-valid');
                }
            });
            
            if (hasErrors) {
                alert('يرجى تصحيح الأخطاء التالية:\n' + errorMessages.join('\n'));
                return false;
            }
            
            // تعطيل زر الحفظ وإظهار مؤشر التحميل
            submitBtn.prop('disabled', true)
                .html('<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...');
            
            // إضافة CSRF token
            const csrftoken = $('[name=csrfmiddlewaretoken]').val();
            
            // إرسال النموذج
            $.ajax({
                url: form.attr('action') || window.location.href,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRFToken': csrftoken
                },
                success: function(response) {
                    console.log('تم الحفظ بنجاح:', response);
                    if (response.redirect_url) {
                        window.location.href = response.redirect_url;
                    } else {
                        window.location.href = '/complaints/';
                    }
                },
                error: function(xhr, status, error) {
                    console.error('خطأ في الحفظ:', error);
                    console.error('استجابة الخادم:', xhr.responseText);
                    
                    let errorMessage = 'حدث خطأ أثناء الحفظ:\n';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        console.error('استجابة الخادم المفكوكة:', response);
                        
                        if (response.errors) {
                            // إظهار الأخطاء في النموذج
                            Object.entries(response.errors).forEach(([field, errors]) => {
                                errorMessage += `${field}: ${errors.join(', ')}\n`;
                                
                                // تحديث واجهة المستخدم لإظهار الخطأ
                                const fieldElement = $(`#id_${field}`);
                                if (fieldElement.length) {
                                    fieldElement.addClass('is-invalid');
                                    
                                    // إزالة رسائل الخطأ السابقة
                                    fieldElement.siblings('.invalid-feedback').remove();
                                    
                                    // إضافة رسالة الخطأ أسفل الحقل
                                    fieldElement.after(`<div class="invalid-feedback">${errors.join(', ')}</div>`);
                                    
                                    // معالجة خاصة لحقل الطلب المرتبط
                                    if (field === 'related_order') {
                                        console.error('خطأ في حقل الطلب المرتبط:', errors);
                                        console.error('قيمة الطلب المرسلة:', formData.get('related_order'));
                                        console.error('الطلبات المتاحة:', window.availableOrderIds ? Array.from(window.availableOrderIds) : 'غير محدد');
                                        
                                        // إعادة تعيين قيمة الحقل
                                        fieldElement.val('');
                                        
                                        // إظهار تنبيه خاص للمستخدم
                                        const specificError = 'يبدو أن هناك مشكلة في الطلب المحدد. الرجاء اختيار طلب آخر أو ترك الحقل فارغًا.';
                                        alert(specificError);
                                    }
                                }
                            });
                        } else if (response.message) {
                            errorMessage += response.message;
                        }
                    } catch (e) {
                        console.error('خطأ في تحليل استجابة الخادم:', e);
                        errorMessage += xhr.responseText || 'حدث خطأ غير متوقع';
                    }
                    
                    alert(errorMessage);
                    submitBtn.prop('disabled', false).html(originalText);
                }
            });
            
            return false;
        });

        // التحقق من صحة قيمة related_order
        $('#id_related_order').change(function() {
            const selectedValue = $(this).val();
            if (selectedValue) {
                if (window.availableOrderIds && !window.availableOrderIds.has(selectedValue)) {
                    $(this).addClass('is-invalid');
                    alert('هذا الطلب غير صالح. يرجى اختيار طلب آخر من القائمة.');
                } else {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                }
            } else {
                $(this).removeClass('is-invalid').removeClass('is-valid');
            }
        });

        // تحديث حقول التعيين عند تغيير نوع الشكوى
        $('#id_complaint_type').change(function() {
            const typeId = $(this).val();
            console.log('تم اختيار نوع الشكوى:', typeId);
            
            if (typeId) {
                $.ajax({
                    url: `/complaints/api/complaint-types/${typeId}/`,
                    method: 'GET',
                    success: function(data) {
                        console.log('تم جلب معلومات نوع الشكوى بنجاح:', data);
                        
                        // تحديث بطاقة معلومات نوع الشكوى
                        const selectedTypeName = $('#id_complaint_type option:selected').text();
                        $('#complaint-type-details').html(`
                            <h6 class="mb-2">${selectedTypeName}</h6>
                            <p class="mb-1"><i class="fas fa-info-circle me-1"></i> ${data.description || 'لا يوجد وصف'}</p>
                            <p class="mb-1"><i class="fas fa-users me-1"></i> الموظفين المتاحين: ${data.staff ? data.staff.length : 0}</p>
                            <p class="mb-0"><i class="fas fa-building me-1"></i> الأقسام المتاحة: ${data.departments ? data.departments.length : 0}</p>
                        `);
                        $('#complaint-type-info').show();
                        $('#no-type-selected').hide();
                        
                        // تحديث قائمة الموظفين
                        const assignedToSelect = $('#id_assigned_to');
                        assignedToSelect.empty();
                        assignedToSelect.append('<option value="">اختر الموظف المسؤول</option>');
                        
                        if (data.staff && data.staff.length > 0) {
                            data.staff.forEach(function(staff) {
                                assignedToSelect.append(`<option value="${staff.id}">${staff.name}</option>`);
                            });
                            console.log('تم تحديث قائمة الموظفين:', data.staff.length, 'موظف');
                        } else {
                            console.log('لا توجد موظفين متاحين');
                        }
                        
                        // تحديث قائمة الأقسام
                        const deptSelect = $('#id_assigned_department');
                        deptSelect.empty();
                        deptSelect.append('<option value="">اختر القسم المختص</option>');
                        
                        if (data.departments && data.departments.length > 0) {
                            data.departments.forEach(function(dept) {
                                deptSelect.append(`<option value="${dept.id}">${dept.name}</option>`);
                            });
                            console.log('تم تحديث قائمة الأقسام:', data.departments.length, 'قسم');
                        } else {
                            console.log('لا توجد أقسام متاحة');
                        }
                        
                        // تعيين القسم الافتراضي إذا كان متوفراً
                        if (data.default_department) {
                            deptSelect.val(data.default_department);
                            console.log('تم تعيين القسم الافتراضي:', data.default_department);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('خطأ في جلب معلومات نوع الشكوى:');
                        console.error('Status:', status);
                        console.error('Error:', error);
                        console.error('Response:', xhr.responseText);
                        console.error('URL المطلوب:', `/complaints/api/complaint-types/${typeId}/`);
                        
                        // إظهار رسالة خطأ للمستخدم
                        alert('حدث خطأ في تحديث معلومات نوع الشكوى. يرجى المحاولة مرة أخرى.');
                    }
                });
            } else {
                console.log('لم يتم اختيار نوع شكوى');
                // إعادة تعيين القوائم إلى الحالة الافتراضية
                $('#id_assigned_to').empty().append('<option value="">اختر الموظف المسؤول</option>');
                $('#id_assigned_department').empty().append('<option value="">اختر القسم المختص</option>');
            }
        });

        // تشغيل التحديثات عند تحميل الصفحة للبيانات المحفوظة
        console.log('تهيئة الصفحة...');
        
        // التحقق من وجود قيم محددة مسبقاً
        const initialCustomer = $('#id_customer').val();
        const initialComplaintType = $('#id_complaint_type').val();
        const initialPriority = $('#id_priority').val();
        
        console.log('القيم الأولية:');
        console.log('- العميل:', initialCustomer);
        console.log('- نوع الشكوى:', initialComplaintType);
        console.log('- الأولوية:', initialPriority);
        
        // الإعداد الأولي للمصفوفة المتاحة للطلبات
        window.availableOrderIds = new Set();
        
        // تفعيل الأحداث
        if (initialCustomer) {
            $('#id_customer').trigger('change');
        }
        if (initialComplaintType) {
            $('#id_complaint_type').trigger('change');
        }
        if (initialPriority) {
            $('#id_priority').trigger('change');
        }
        $('#id_content_type').trigger('change');
    });

    // تأكيد قبل الخروج إذا كانت هناك تغييرات غير محفوظة
    window.formChanged = false;
    $('form input, form select, form textarea').change(function() {
        window.formChanged = true;
    });

    $(window).on('beforeunload', function() {
        if (window.formChanged) {
            return 'هناك تغييرات غير محفوظة. هل أنت متأكد من المغادرة؟';
        }
    });

    $('form').on('submit', function() {
        window.formChanged = false;
    });

    // إضافة رسائل تصحيح شاملة للحفظ
    $('form').on('submit', function(e) {
        console.log('===== بدء عملية حفظ الشكوى =====');
        
        // طباعة جميع بيانات النموذج أولاً
        const formData = new FormData(this);
        console.log('بيانات النموذج:');
        for (let [key, value] of formData.entries()) {
            console.log(`${key}: ${value}`);
        }
        
        // التحقق من الحقول المطلوبة
        const requiredFields = ['title', 'complaint_type', 'priority', 'description'];
        let hasErrors = false;
        
        console.log('فحص الحقول المطلوبة:');
        requiredFields.forEach(function(field) {
            const element = $(`#id_${field}`);
            const value = element.val();
            console.log(`- فحص ${field}: "${value}"`);
            
            if (!value || value.trim() === '') {
                console.error(`❌ الحقل المطلوب "${field}" فارغ`);
                element.addClass('is-invalid');
                hasErrors = true;
            } else {
                console.log(`✅ الحقل "${field}" صحيح`);
                element.removeClass('is-invalid').addClass('is-valid');
            }
        });
        
        // التحقق من اختيار العميل
        const customerId = $('#id_customer').val();
        console.log(`- فحص العميل: "${customerId}"`);
        if (customerId) {
            console.log('✅ تم اختيار العميل');
            $('#id_customer').removeClass('is-invalid').addClass('is-valid');
        } else {
            console.error('❌ لم يتم اختيار عميل');
            $('#id_customer').addClass('is-invalid');
            hasErrors = true;
        }
        
        // التحقق من الموعد النهائي
        const deadline = $('#id_deadline').val();
        console.log(`- فحص الموعد النهائي: "${deadline}"`);
        if (!deadline) {
            console.error('❌ لم يتم تحديد موعد نهائي');
            $('#id_deadline').addClass('is-invalid');
            hasErrors = true;
        } else {
            console.log('✅ تم تحديد الموعد النهائي');
            $('#id_deadline').removeClass('is-invalid').addClass('is-valid');
        }
        
        // التحقق من صحة النموذج HTML5
        const form = this;
        const isValid = form.checkValidity();
        console.log('صحة النموذج HTML5:', isValid);
        
        if (!isValid || hasErrors) {
            console.error('===== يوجد أخطاء في النموذج - لن يتم الحفظ =====');
            console.error('أخطاء HTML5:', !isValid);
            console.error('أخطاء مخصصة:', hasErrors);
            
            form.classList.add('was-validated');
            e.preventDefault();
            
            // عرض رسالة تفصيلية للمستخدم
            let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
            if (!customerId) errorMessage += '- اختر عميل\n';
            if (!$('#id_title').val()) errorMessage += '- أدخل عنوان الشكوى\n';
            if (!$('#id_complaint_type').val()) errorMessage += '- اختر نوع الشكوى\n';
            if (!$('#id_priority').val()) errorMessage += '- اختر أولوية الشكوى\n';
            if (!$('#id_description').val()) errorMessage += '- أدخل وصف الشكوى\n';
            if (!deadline) errorMessage += '- حدد الموعد النهائي\n';
            
            alert(errorMessage);
            return false;
        }
        
        console.log('===== تم التحقق من جميع البيانات - سيتم الحفظ =====');
        
        // تعطيل زر الحفظ لمنع الإرسال المتكرر
        $('button[type="submit"]').prop('disabled', true).text('جاري الحفظ...');
        
        return true;
    });
</script>
{% endblock %}
