# Generated by Django 4.2.21 on 2025-08-04 16:59

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customers', '0006_alter_customer_branch'),
        ('accounts', '0008_initial_fixed'),
        ('orders', '0010_order_salesperson_name_raw'),
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='Complaint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('complaint_number', models.CharField(editable=False, max_length=50, unique=True, verbose_name='رقم الشكوى')),
                ('title', models.CharField(max_length=200, verbose_name='موضوع الشكوى')),
                ('description', models.TextField(verbose_name='وصف الشكوى')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الكائن المرتبط')),
                ('status', models.CharField(choices=[('new', 'جديدة'), ('in_progress', 'قيد الحل'), ('resolved', 'محلولة'), ('closed', 'مغلقة'), ('overdue', 'متأخرة'), ('escalated', 'مصعدة')], default='new', max_length=20, verbose_name='حالة الشكوى')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقديم')),
                ('deadline', models.DateTimeField(verbose_name='الموعد النهائي للحل')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('closed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإغلاق')),
                ('customer_rating', models.PositiveSmallIntegerField(blank=True, choices=[(1, 'غير راضي جداً'), (2, 'غير راضي'), (3, 'محايد'), (4, 'راضي'), (5, 'راضي جداً')], null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='تقييم العميل')),
                ('customer_feedback', models.TextField(blank=True, verbose_name='تعليق العميل على الحل')),
                ('internal_notes', models.TextField(blank=True, verbose_name='ملاحظات داخلية')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('last_activity_at', models.DateTimeField(auto_now=True, verbose_name='آخر نشاط')),
                ('assigned_department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounts.department', verbose_name='القسم المحول إليه')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_complaints', to=settings.AUTH_USER_MODEL, verbose_name='مسؤول المتابعة')),
                ('branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounts.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'شكوى',
                'verbose_name_plural': 'الشكاوى',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ComplaintUpdate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('update_type', models.CharField(choices=[('status_change', 'تغيير الحالة'), ('assignment', 'تعيين مسؤول'), ('comment', 'تعليق'), ('escalation', 'تصعيد'), ('resolution', 'حل'), ('customer_response', 'رد العميل'), ('internal_note', 'ملاحظة داخلية')], max_length=20, verbose_name='نوع التحديث')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التحديث')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التحديث')),
                ('is_visible_to_customer', models.BooleanField(default=True, verbose_name='مرئي للعميل')),
                ('old_status', models.CharField(blank=True, max_length=20, verbose_name='الحالة السابقة')),
                ('new_status', models.CharField(blank=True, max_length=20, verbose_name='الحالة الجديدة')),
                ('complaint', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='updates', to='complaints.complaint', verbose_name='الشكوى')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('new_assignee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='new_assignments', to=settings.AUTH_USER_MODEL, verbose_name='المسؤول الجديد')),
                ('old_assignee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='old_assignments', to=settings.AUTH_USER_MODEL, verbose_name='المسؤول السابق')),
            ],
            options={
                'verbose_name': 'تحديث شكوى',
                'verbose_name_plural': 'تحديثات الشكاوى',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ComplaintType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='نوع الشكوى')),
                ('description', models.TextField(blank=True, verbose_name='وصف النوع')),
                ('default_priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=10, verbose_name='الأولوية الافتراضية')),
                ('default_deadline_hours', models.PositiveIntegerField(default=72, help_text='المهلة الزمنية المحددة تلقائياً لحل الشكوى', verbose_name='المهلة الافتراضية (بالساعات)')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('default_assignee', models.ForeignKey(blank=True, help_text='الموظف المسؤول عن المتابعة', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المسؤول الافتراضي')),
                ('responsible_department', models.ForeignKey(blank=True, help_text='القسم المحول إليه تلقائياً', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounts.department', verbose_name='القسم المسؤول')),
            ],
            options={
                'verbose_name': 'نوع شكوى',
                'verbose_name_plural': 'أنواع الشكاوى',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ComplaintSLA',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('response_time_hours', models.PositiveIntegerField(default=4, verbose_name='وقت الاستجابة (ساعات)')),
                ('resolution_time_hours', models.PositiveIntegerField(default=72, verbose_name='وقت الحل (ساعات)')),
                ('escalation_time_hours', models.PositiveIntegerField(default=48, verbose_name='وقت التصعيد (ساعات)')),
                ('target_satisfaction_rate', models.DecimalField(decimal_places=2, default=90.0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='معدل الرضا المستهدف (%)')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('complaint_type', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='sla', to='complaints.complainttype', verbose_name='نوع الشكوى')),
            ],
            options={
                'verbose_name': 'اتفاقية مستوى خدمة',
                'verbose_name_plural': 'اتفاقيات مستوى الخدمة',
            },
        ),
        migrations.CreateModel(
            name='ComplaintNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('new_complaint', 'شكوى جديدة'), ('status_change', 'تغيير الحالة'), ('assignment', 'تعيين مسؤول'), ('deadline_reminder', 'تذكير الموعد النهائي'), ('overdue', 'تجاوز الموعد النهائي'), ('escalation', 'تصعيد'), ('resolution', 'حل'), ('customer_rating', 'تقييم العميل')], max_length=20, verbose_name='نوع الإشعار')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الإشعار')),
                ('message', models.TextField(verbose_name='نص الإشعار')),
                ('is_read', models.BooleanField(default=False, verbose_name='تم القراءة')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
                ('complaint', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='complaints.complaint', verbose_name='الشكوى')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستلم')),
            ],
            options={
                'verbose_name': 'إشعار شكوى',
                'verbose_name_plural': 'إشعارات الشكاوى',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ComplaintEscalation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.CharField(choices=[('overdue', 'تجاوز الموعد النهائي'), ('high_priority', 'أولوية عالية'), ('customer_request', 'طلب العميل'), ('complex_issue', 'مشكلة معقدة'), ('department_change', 'تغيير القسم'), ('unsatisfactory_response', 'استجابة غير مرضية')], max_length=30, verbose_name='سبب التصعيد')),
                ('description', models.TextField(verbose_name='وصف التصعيد')),
                ('escalated_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التصعيد')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ حل التصعيد')),
                ('resolution_notes', models.TextField(blank=True, verbose_name='ملاحظات الحل')),
                ('complaint', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='escalations', to='complaints.complaint', verbose_name='الشكوى')),
                ('escalated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='escalation_actions', to=settings.AUTH_USER_MODEL, verbose_name='تم التصعيد بواسطة')),
                ('escalated_from', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='escalated_from_complaints', to=settings.AUTH_USER_MODEL, verbose_name='المصعد من')),
                ('escalated_to', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='escalated_to_complaints', to=settings.AUTH_USER_MODEL, verbose_name='المصعد إلى')),
            ],
            options={
                'verbose_name': 'تصعيد شكوى',
                'verbose_name_plural': 'تصعيدات الشكاوى',
                'ordering': ['-escalated_at'],
            },
        ),
        migrations.CreateModel(
            name='ComplaintAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='complaints/attachments/%Y/%m/%d/', verbose_name='الملف')),
                ('filename', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('description', models.CharField(blank=True, max_length=500, verbose_name='وصف الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True, verbose_name='حجم الملف (بايت)')),
                ('complaint', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='complaints.complaint', verbose_name='الشكوى')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='رفع بواسطة')),
            ],
            options={
                'verbose_name': 'مرفق شكوى',
                'verbose_name_plural': 'مرفقات الشكاوى',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.AddField(
            model_name='complaint',
            name='complaint_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='complaints.complainttype', verbose_name='نوع الشكوى'),
        ),
        migrations.AddField(
            model_name='complaint',
            name='content_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='contenttypes.contenttype', verbose_name='نوع المحتوى المرتبط'),
        ),
        migrations.AddField(
            model_name='complaint',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_complaints', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AddField(
            model_name='complaint',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='complaints', to='customers.customer', verbose_name='العميل'),
        ),
        migrations.AddField(
            model_name='complaint',
            name='related_order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='complaints', to='orders.order', verbose_name='الطلب المرتبط'),
        ),
        migrations.AddIndex(
            model_name='complaint',
            index=models.Index(fields=['complaint_number'], name='complaints__complai_998a4d_idx'),
        ),
        migrations.AddIndex(
            model_name='complaint',
            index=models.Index(fields=['customer'], name='complaints__custome_934d95_idx'),
        ),
        migrations.AddIndex(
            model_name='complaint',
            index=models.Index(fields=['status'], name='complaints__status_7c8de0_idx'),
        ),
        migrations.AddIndex(
            model_name='complaint',
            index=models.Index(fields=['priority'], name='complaints__priorit_f4170d_idx'),
        ),
        migrations.AddIndex(
            model_name='complaint',
            index=models.Index(fields=['assigned_to'], name='complaints__assigne_57ef98_idx'),
        ),
        migrations.AddIndex(
            model_name='complaint',
            index=models.Index(fields=['deadline'], name='complaints__deadlin_07647c_idx'),
        ),
        migrations.AddIndex(
            model_name='complaint',
            index=models.Index(fields=['created_at'], name='complaints__created_805464_idx'),
        ),
    ]
