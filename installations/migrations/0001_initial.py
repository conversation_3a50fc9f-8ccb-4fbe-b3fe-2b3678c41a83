# Generated by Django 5.2.4 on 2025-07-15 15:33

import django.db.models.deletion
import installations.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0001_initial'),
        ('orders', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Driver',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=100, verbose_name='اسم السائق')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                (
                    'license_number',
                    models.CharField(
                        blank=True, max_length=50, verbose_name='رقم الرخصة'
                    ),
                ),
                (
                    'vehicle_number',
                    models.CharField(
                        blank=True, max_length=50, verbose_name='رقم المركبة'
                    ),
                ),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
                ),
            ],
            options={
                'verbose_name': 'سائق',
                'verbose_name_plural': 'السائقين',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ModificationErrorType',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=100, verbose_name='اسم السبب')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
                ),
            ],
            options={
                'verbose_name': 'نوع سبب تعديل',
                'verbose_name_plural': 'أنواع أسباب التعديلات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Technician',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفني')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                (
                    'specialization',
                    models.CharField(blank=True, max_length=100, verbose_name='التخصص'),
                ),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
                ),
            ],
            options={
                'verbose_name': 'فني',
                'verbose_name_plural': 'الفنيين',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CustomerDebt',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'debt_amount',
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name='مبلغ المديونية'
                    ),
                ),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                (
                    'is_paid',
                    models.BooleanField(default=False, verbose_name='تم الدفع'),
                ),
                (
                    'payment_receipt_number',
                    models.CharField(
                        blank=True, max_length=100, verbose_name='رقم إذن استلام المبلغ'
                    ),
                ),
                (
                    'payment_receiver_name',
                    models.CharField(
                        blank=True, max_length=100, verbose_name='اسم المستلم'
                    ),
                ),
                (
                    'payment_date',
                    models.DateTimeField(
                        blank=True, null=True, verbose_name='تاريخ الدفع'
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
                ),
                (
                    'customer',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='customers.customer',
                        verbose_name='العميل',
                    ),
                ),
                (
                    'order',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='orders.order',
                        verbose_name='الطلب',
                    ),
                ),
            ],
            options={
                'verbose_name': 'مديونية عميل',
                'verbose_name_plural': 'مديونيات العملاء',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InstallationAnalytics',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'month',
                    models.DateField(
                        help_text='أول يوم من الشهر', verbose_name='الشهر'
                    ),
                ),
                (
                    'total_installations',
                    models.IntegerField(default=0, verbose_name='إجمالي التركيبات'),
                ),
                (
                    'completed_installations',
                    models.IntegerField(default=0, verbose_name='التركيبات المكتملة'),
                ),
                (
                    'pending_installations',
                    models.IntegerField(
                        default=0, verbose_name='التركيبات في الانتظار'
                    ),
                ),
                (
                    'in_progress_installations',
                    models.IntegerField(
                        default=0, verbose_name='التركيبات قيد التنفيذ'
                    ),
                ),
                (
                    'total_customers',
                    models.IntegerField(default=0, verbose_name='إجمالي العملاء'),
                ),
                (
                    'new_customers',
                    models.IntegerField(default=0, verbose_name='العملاء الجدد'),
                ),
                (
                    'total_visits',
                    models.IntegerField(default=0, verbose_name='إجمالي الزيارات'),
                ),
                (
                    'total_modifications',
                    models.IntegerField(default=0, verbose_name='إجمالي التعديلات'),
                ),
                (
                    'modification_rate',
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        verbose_name='نسبة التعديلات %',
                    ),
                ),
                (
                    'completion_rate',
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        verbose_name='نسبة الإكمال %',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
                ),
            ],
            options={
                'verbose_name': 'تحليل تركيب شهري',
                'verbose_name_plural': 'تحليلات التركيبات الشهرية',
                'ordering': ['-month'],
                'unique_together': {('month',)},
            },
        ),
        migrations.CreateModel(
            name='InstallationSchedule',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('scheduled_date', models.DateField(verbose_name='تاريخ التركيب')),
                ('scheduled_time', models.TimeField(verbose_name='موعد التركيب')),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('pending', 'في الانتظار'),
                            ('scheduled', 'مجدول'),
                            ('in_progress', 'قيد التنفيذ'),
                            ('completed', 'مكتمل'),
                            ('cancelled', 'ملغي'),
                            ('rescheduled', 'إعادة جدولة'),
                            ('modification_required', 'يحتاج تعديل'),
                            ('modification_in_progress', 'التعديل قيد التنفيذ'),
                            ('modification_completed', 'التعديل مكتمل'),
                        ],
                        default='pending',
                        max_length=30,
                        verbose_name='الحالة',
                    ),
                ),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                (
                    'completion_date',
                    models.DateTimeField(
                        blank=True, null=True, verbose_name='تاريخ الإكمال'
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
                ),
                (
                    'order',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='orders.order',
                        verbose_name='الطلب',
                    ),
                ),
            ],
            options={
                'verbose_name': 'جدولة تركيب',
                'verbose_name_plural': 'جدولة التركيبات',
                'ordering': ['-scheduled_date', '-scheduled_time'],
            },
        ),
        migrations.CreateModel(
            name='InstallationPayment',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'payment_type',
                    models.CharField(
                        choices=[
                            ('remaining', 'المتبقي'),
                            ('additional', 'إضافي'),
                            ('refund', 'استرداد'),
                        ],
                        max_length=20,
                        verbose_name='نوع الدفع',
                    ),
                ),
                (
                    'amount',
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name='المبلغ'
                    ),
                ),
                (
                    'payment_method',
                    models.CharField(
                        blank=True, max_length=50, verbose_name='طريقة الدفع'
                    ),
                ),
                (
                    'receipt_number',
                    models.CharField(
                        blank=True, max_length=50, verbose_name='رقم الإيصال'
                    ),
                ),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'installation',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='installations.installationschedule',
                        verbose_name='التركيب',
                    ),
                ),
            ],
            options={
                'verbose_name': 'دفعة تركيب',
                'verbose_name_plural': 'مدفوعات التركيب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InstallationArchive',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'completion_date',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإكمال'
                    ),
                ),
                (
                    'archive_notes',
                    models.TextField(blank=True, verbose_name='ملاحظات الأرشفة'),
                ),
                (
                    'archived_by',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='تم الأرشفة بواسطة',
                    ),
                ),
                (
                    'installation',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='installations.installationschedule',
                        verbose_name='التركيب',
                    ),
                ),
            ],
            options={
                'verbose_name': 'أرشيف تركيب',
                'verbose_name_plural': 'أرشيف التركيبات',
                'ordering': ['-completion_date'],
            },
        ),
        migrations.CreateModel(
            name='InstallationTeam',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفريق')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
                ),
                (
                    'driver',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='installations.driver',
                        verbose_name='السائق',
                    ),
                ),
                (
                    'technicians',
                    models.ManyToManyField(
                        to='installations.technician', verbose_name='الفنيين'
                    ),
                ),
            ],
            options={
                'verbose_name': 'فريق تركيب',
                'verbose_name_plural': 'فرق التركيب',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='installationschedule',
            name='team',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='installations.installationteam',
                verbose_name='الفريق',
            ),
        ),
        migrations.CreateModel(
            name='ManufacturingOrder',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'order_type',
                    models.CharField(
                        choices=[
                            ('new', 'جديد'),
                            ('modification', 'تعديل'),
                            ('repair', 'إصلاح'),
                        ],
                        default='modification',
                        max_length=20,
                        verbose_name='نوع الأمر',
                    ),
                ),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('pending', 'في الانتظار'),
                            ('approved', 'موافق عليه'),
                            ('in_progress', 'قيد التنفيذ'),
                            ('completed', 'مكتمل'),
                            ('cancelled', 'ملغي'),
                        ],
                        default='pending',
                        max_length=20,
                        verbose_name='الحالة',
                    ),
                ),
                ('description', models.TextField(verbose_name='تفاصيل الأمر')),
                (
                    'estimated_completion_date',
                    models.DateField(
                        blank=True, null=True, verbose_name='تاريخ الإكمال المتوقع'
                    ),
                ),
                (
                    'actual_completion_date',
                    models.DateTimeField(
                        blank=True, null=True, verbose_name='تاريخ الإكمال الفعلي'
                    ),
                ),
                (
                    'manager_notes',
                    models.TextField(blank=True, verbose_name='ملاحظات المدير'),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
                ),
                (
                    'assigned_to',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='installation_manufacturing_orders',
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='مُسند إلى',
                    ),
                ),
            ],
            options={
                'verbose_name': 'أمر تصنيع',
                'verbose_name_plural': 'أوامر التصنيع',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ModificationRequest',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'modification_type',
                    models.CharField(max_length=100, verbose_name='نوع التعديل'),
                ),
                ('description', models.TextField(verbose_name='تفاصيل التعديل')),
                (
                    'priority',
                    models.CharField(
                        choices=[
                            ('low', 'منخفض'),
                            ('medium', 'متوسط'),
                            ('high', 'عالي'),
                            ('urgent', 'عاجل'),
                        ],
                        default='medium',
                        max_length=20,
                        verbose_name='الأولوية',
                    ),
                ),
                (
                    'estimated_cost',
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name='التكلفة المتوقعة',
                    ),
                ),
                (
                    'customer_approval',
                    models.BooleanField(default=False, verbose_name='موافقة العميل'),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
                ),
                (
                    'customer',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='customers.customer',
                        verbose_name='العميل',
                    ),
                ),
                (
                    'installation',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='installations.installationschedule',
                        verbose_name='التركيب',
                    ),
                ),
            ],
            options={
                'verbose_name': 'طلب تعديل',
                'verbose_name_plural': 'طلبات التعديل',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ModificationReport',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'report_file',
                    models.FileField(
                        blank=True,
                        upload_to=installations.models.modification_report_path,
                        verbose_name='ملف التقرير',
                    ),
                ),
                ('description', models.TextField(verbose_name='وصف التعديل المنجز')),
                (
                    'completion_notes',
                    models.TextField(blank=True, verbose_name='ملاحظات الإكمال'),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'created_by',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name='أنشئ بواسطة',
                    ),
                ),
                (
                    'manufacturing_order',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='installations.manufacturingorder',
                        verbose_name='أمر التصنيع',
                    ),
                ),
                (
                    'modification_request',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='installations.modificationrequest',
                        verbose_name='طلب التعديل',
                    ),
                ),
            ],
            options={
                'verbose_name': 'تقرير تعديل',
                'verbose_name_plural': 'تقارير التعديل',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ModificationImage',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'image',
                    models.ImageField(
                        upload_to=installations.models.modification_images_path,
                        verbose_name='صورة',
                    ),
                ),
                (
                    'description',
                    models.CharField(
                        blank=True, max_length=200, verbose_name='وصف الصورة'
                    ),
                ),
                (
                    'uploaded_at',
                    models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع'),
                ),
                (
                    'modification',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='installations.modificationrequest',
                        verbose_name='طلب التعديل',
                    ),
                ),
            ],
            options={
                'verbose_name': 'صورة تعديل',
                'verbose_name_plural': 'صور التعديل',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='ModificationErrorAnalysis',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('error_description', models.TextField(verbose_name='وصف الخطأ')),
                ('root_cause', models.TextField(verbose_name='السبب الجذري')),
                ('solution_applied', models.TextField(verbose_name='الحل المطبق')),
                (
                    'prevention_measures',
                    models.TextField(blank=True, verbose_name='إجراءات الوقاية'),
                ),
                (
                    'cost_impact',
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name='التأثير المالي',
                    ),
                ),
                (
                    'time_impact_hours',
                    models.IntegerField(
                        default=0, verbose_name='التأثير الزمني (ساعات)'
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
                ),
                (
                    'error_type',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='installations.modificationerrortype',
                        verbose_name='نوع السبب',
                    ),
                ),
                (
                    'modification_request',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='installations.modificationrequest',
                        verbose_name='طلب التعديل',
                    ),
                ),
            ],
            options={
                'verbose_name': 'تحليل خطأ تعديل',
                'verbose_name_plural': 'تحليلات أخطاء التعديلات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='manufacturingorder',
            name='modification_request',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to='installations.modificationrequest',
                verbose_name='طلب التعديل',
            ),
        ),
        migrations.CreateModel(
            name='ReceiptMemo',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'receipt_image',
                    models.ImageField(
                        upload_to=installations.models.installation_receipt_path,
                        verbose_name='صورة مذكرة الاستلام',
                    ),
                ),
                (
                    'customer_signature',
                    models.BooleanField(default=False, verbose_name='توقيع العميل'),
                ),
                (
                    'amount_received',
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name='المبلغ المستلم',
                    ),
                ),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                (
                    'created_at',
                    models.DateTimeField(
                        auto_now_add=True, verbose_name='تاريخ الإنشاء'
                    ),
                ),
                (
                    'installation',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='installations.installationschedule',
                        verbose_name='التركيب',
                    ),
                ),
            ],
            options={
                'verbose_name': 'مذكرة استلام',
                'verbose_name_plural': 'مذكرات الاستلام',
            },
        ),
    ]
