# Generated by Django 4.2.21 on 2025-07-17 17:05

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('installations', '0002_installationorder'),
    ]

    operations = [
        migrations.CreateModel(
            name='InstallationEventLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('status_change', 'تغيير حالة'), ('schedule_change', 'تغيير جدولة'), ('team_assignment', 'تعيين فريق'), ('modification_request', 'طلب تعديل'), ('payment_received', 'استلام دفعة'), ('completion', 'إكمال'), ('cancellation', 'إلغاء')], max_length=20, verbose_name='نوع الحدث')),
                ('description', models.TextField(verbose_name='وصف الحدث')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='بيانات إضافية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الحدث')),
            ],
            options={
                'verbose_name': 'سجل حدث التركيب',
                'verbose_name_plural': 'سجل أحداث التركيبات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InstallationSchedulingSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('technician_name', models.CharField(blank=True, max_length=100, verbose_name='اسم الفني')),
                ('driver_name', models.CharField(blank=True, max_length=100, verbose_name='اسم السائق')),
                ('customer_address', models.TextField(blank=True, verbose_name='عنوان العميل')),
                ('customer_phone', models.CharField(blank=True, max_length=20, verbose_name='رقم هاتف العميل')),
                ('contract_number', models.CharField(blank=True, max_length=100, verbose_name='رقم العقد')),
                ('invoice_number', models.CharField(blank=True, max_length=100, verbose_name='رقم الفاتورة')),
                ('salesperson_name', models.CharField(blank=True, max_length=100, verbose_name='اسم البائع')),
                ('branch_name', models.CharField(blank=True, max_length=100, verbose_name='اسم الفرع')),
                ('special_instructions', models.TextField(blank=True, verbose_name='تعليمات خاصة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعدادات ج��ولة التركيب',
                'verbose_name_plural': 'إعدادات جدولة التركيبات',
            },
        ),
        migrations.CreateModel(
            name='InstallationStatusLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_status', models.CharField(choices=[('needs_scheduling', 'بحاجة جدولة'), ('scheduled', 'مجدول'), ('in_installation', 'قيد التركيب'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('modification_required', 'يحتاج تعديل'), ('modification_in_progress', 'التعديل قيد التنفيذ'), ('modification_completed', 'التعديل مكتمل')], max_length=30, verbose_name='الحالة السابقة')),
                ('new_status', models.CharField(choices=[('needs_scheduling', 'بحاجة جدولة'), ('scheduled', 'مجدول'), ('in_installation', 'قيد التركيب'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('modification_required', 'يحتاج تعديل'), ('modification_in_progress', 'التعديل قيد التنفيذ'), ('modification_completed', 'التعديل مكتمل')], max_length=30, verbose_name='الحالة الجديدة')),
                ('reason', models.TextField(blank=True, help_text='سبب تغيير الحالة (مطلوب لبعض التغييرات)', verbose_name='سبب التغيير')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التغيير بواسطة')),
            ],
            options={
                'verbose_name': 'سجل حالة التركيب',
                'verbose_name_plural': 'سجلات حالات التركيب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AlterField(
            model_name='installationschedule',
            name='status',
            field=models.CharField(choices=[('needs_scheduling', 'بحاجة جدولة'), ('scheduled', 'مجدول'), ('in_installation', 'قيد التركيب'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('modification_required', 'يحتاج تعديل'), ('modification_in_progress', 'التعديل قيد التنفيذ'), ('modification_completed', 'التعديل مكتمل')], default='pending', max_length=30, verbose_name='الحالة'),
        ),
        migrations.DeleteModel(
            name='InstallationOrder',
        ),
        migrations.AddField(
            model_name='installationstatuslog',
            name='installation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_logs', to='installations.installationschedule', verbose_name='التركيب'),
        ),
        migrations.AddField(
            model_name='installationschedulingsettings',
            name='installation',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='scheduling_settings', to='installations.installationschedule', verbose_name='التركيب'),
        ),
        migrations.AddField(
            model_name='installationeventlog',
            name='installation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='event_logs', to='installations.installationschedule', verbose_name='التركيب'),
        ),
        migrations.AddField(
            model_name='installationeventlog',
            name='user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم'),
        ),
    ]
