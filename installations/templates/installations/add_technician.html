{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة فني جديد{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-plus"></i>
                        إضافة فني جديد
                    </h6>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                {{ form.name.label }}
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">
                                {{ form.phone.label }}
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.phone.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.specialization.id_for_label }}" class="form-label">
                                {{ form.specialization.label }}
                            </label>
                            {{ form.specialization }}
                            {% if form.specialization.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.specialization.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                سيتم إضافة الفني لقسم التركيبات تلقائياً
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.is_active.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'installations:team_management' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الفني
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 