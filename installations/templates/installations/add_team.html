{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة فريق جديد{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-plus"></i>
                        إضافة فريق جديد
                    </h6>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    {{ form.name.label }}
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="{{ form.technicians.id_for_label }}" class="form-label">
                                    {{ form.technicians.label }}
                                </label>
                                <div class="border p-3 rounded" style="max-height: 200px; overflow-y: auto;">
                                    {{ form.technicians }}
                                </div>
                                {% if form.technicians.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.technicians.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    يتم عرض فنيي التركيبات فقط
                                </small>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.driver.id_for_label }}" class="form-label">
                                    {{ form.driver.label }}
                                </label>
                                {{ form.driver }}
                                {% if form.driver.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.driver.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'installations:team_management' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الفريق
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 