{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i>
                        {{ title }}
                    </h6>
                    <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-sm btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للتفاصيل
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="{{ form.status.id_for_label }}" class="form-label">
                                                <strong>حالة التركيب</strong>
                                            </label>
                                            {{ form.status }}
                                            {% if form.status.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ form.status.errors }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group" id="completion-date-group" style="display: none;">
                                            <label for="{{ form.completion_date.id_for_label }}" class="form-label">
                                                <strong>تاريخ الإكمال</strong>
                                            </label>
                                            {{ form.completion_date }}
                                            <small class="form-text text-muted">
                                                إذا لم يتم تحديد تاريخ، سيتم استخدام التاريخ الحالي
                                            </small>
                                            {% if form.completion_date.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ form.completion_date.errors }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">
                                        <strong>ملاحظات إضافية</strong>
                                    </label>
                                    {{ form.notes }}
                                    {% if form.notes.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.notes.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        حفظ التغييرات
                                    </button>
                                    <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card border-left-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info">
                                        <i class="fas fa-info-circle"></i>
                                        معلومات التركيب
                                    </h6>
                                    <div class="mt-3">
                                        <p><strong>رقم الطلب:</strong> {{ installation.order.order_number }}</p>
                                        <p><strong>العميل:</strong> {{ installation.order.customer.name }}</p>
                                        <p class="mb-0"><strong>تاريخ الجدولة:</strong> {{ installation.scheduled_date|date:"Y-m-d" }}</p>
                                        <p><strong>موعد التركيب:</strong> {{ installation.scheduled_time|time:"H:i" }}</p>
                                        <p><strong>الفريق:</strong> {{ installation.team.name|default:"غير محدد" }}</p>
                                        <p><strong>الحالة الحالية:</strong> 
                                            {% if installation.status == 'needs_scheduling' %}
                                                <span class="badge" style="background-color: #fd7e14; color: #fff; font-weight: bold; padding: 8px 12px; font-size: 0.9em;">
                                                    <i class="fas fa-calendar-plus"></i> بحاجة جدولة
                                                </span>
                                            {% elif installation.status == 'scheduled' %}
                                                <span class="badge" style="background-color: #17a2b8; color: #fff; font-weight: bold; padding: 8px 12px; font-size: 0.9em;">
                                                    <i class="fas fa-calendar-check"></i> مجدول
                                                </span>
                                            {% elif installation.status == 'in_installation' %}
                                                <span class="badge" style="background-color: #007bff; color: #fff; font-weight: bold; padding: 8px 12px; font-size: 0.9em;">
                                                    <i class="fas fa-tools"></i> قيد التركيب
                                                </span>
                                            {% elif installation.status == 'completed' %}
                                                <span class="badge" style="background-color: #28a745; color: #fff; font-weight: bold; padding: 8px 12px; font-size: 0.9em;">
                                                    <i class="fas fa-check-circle"></i> مكتمل
                                                </span>
                                            {% elif installation.status == 'cancelled' %}
                                                <span class="badge" style="background-color: #dc3545; color: #fff; font-weight: bold; padding: 8px 12px; font-size: 0.9em;">
                                                    <i class="fas fa-times-circle"></i> ملغي
                                                </span>
                                            {% elif installation.status == 'modification_required' %}
                                                <span class="badge" style="background-color: #fd7e14; color: #fff; font-weight: bold; padding: 8px 12px; font-size: 0.9em;">
                                                    <i class="fas fa-exclamation-triangle"></i> يحتاج تعديل
                                                </span>
                                            {% elif installation.status == 'modification_in_progress' %}
                                                <span class="badge" style="background-color: #6f42c1; color: #fff; font-weight: bold; padding: 8px 12px; font-size: 0.9em;">
                                                    <i class="fas fa-cogs"></i> التعديل قيد التنفيذ
                                                </span>
                                            {% elif installation.status == 'modification_completed' %}
                                                <span class="badge" style="background-color: #20c997; color: #fff; font-weight: bold; padding: 8px 12px; font-size: 0.9em;">
                                                    <i class="fas fa-check-circle"></i> التعديل مكتمل
                                                </span>
                                            {% else %}
                                                <span class="badge" style="background-color: #6c757d; color: #fff; font-weight: bold; padding: 8px 12px; font-size: 0.9em;">
                                                    {{ installation.get_status_display }}
                                                </span>
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // إظهار/إخفاء حقل تاريخ الإكمال حسب الحالة المختارة
    $('#installation-status').change(function() {
        var selectedStatus = $(this).val();
        if (selectedStatus === 'completed') {
            $('#completion-date-group').show();
            $('#{{ form.completion_date.id_for_label }}').prop('required', true);

            // تعيين التاريخ والوقت الحالي كقيمة افتراضية إذا لم يكن محدد
            var currentField = $('#{{ form.completion_date.id_for_label }}');
            if (!currentField.val()) {
                var now = new Date();
                var dateTimeString = now.getFullYear() + '-' +
                                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                    String(now.getDate()).padStart(2, '0') + 'T' +
                                    String(now.getHours()).padStart(2, '0') + ':' +
                                    String(now.getMinutes()).padStart(2, '0');
                currentField.val(dateTimeString);
            }
        } else {
            $('#completion-date-group').hide();
            $('#{{ form.completion_date.id_for_label }}').prop('required', false);
        }
    });

    // تشغيل عند تحميل الصفحة
    $('#installation-status').trigger('change');
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.form-control {
    border-radius: 8px;
    border: 1px solid #d1d3e2;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
}

.card {
    border-radius: 12px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.badge {
    font-size: 0.8em;
    padding: 0.5em 0.8em;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
</style>
{% endblock %} 