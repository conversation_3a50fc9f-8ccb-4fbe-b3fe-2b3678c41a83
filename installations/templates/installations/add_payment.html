{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة دفعة - {{ installation.order.order_number }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-money-bill"></i>
                        إضافة دفعة - {{ installation.order.order_number }}
                    </h6>
                </div>
                <div class="card-body">
                    <!-- معلومات الطلب -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> معلومات الطلب:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>رقم الطلب:</strong> {{ installation.order.order_number }}<br>
                                <strong>العميل:</strong> {{ installation.order.customer.name }}<br>
                                <strong>متبقي الحساب:</strong> {{ installation.order.remaining_amount|default:"0"|currency_format }}
                            </div>
                            <div class="col-md-6">
                                <strong>العنوان:</strong> {{ installation.order.customer.address|default:"غير محدد" }}<br>
                                <strong>الهاتف:</strong> {{ installation.order.customer.phone }}<br>
                                <strong>حالة التركيب:</strong> 
                                <span class="badge badge-{{ installation.status|yesno:'success,warning' }}">
                                    {{ installation.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج الدفعة -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.payment_type.id_for_label }}" class="form-label">
                                    {{ form.payment_type.label }}
                                </label>
                                {{ form.payment_type }}
                                {% if form.payment_type.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.payment_type.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.amount.id_for_label }}" class="form-label">
                                    {{ form.amount.label }}
                                </label>
                                {{ form.amount }}
                                {% if form.amount.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.amount.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.payment_method.id_for_label }}" class="form-label">
                                    {{ form.payment_method.label }}
                                </label>
                                {{ form.payment_method }}
                                {% if form.payment_method.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.payment_method.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.receipt_number.id_for_label }}" class="form-label">
                                    {{ form.receipt_number.label }}
                                </label>
                                {{ form.receipt_number }}
                                {% if form.receipt_number.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.receipt_number.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ الدفعة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// التحقق من المبلغ
document.getElementById('{{ form.amount.id_for_label }}').addEventListener('input', function() {
    const amount = parseFloat(this.value);
    const remainingAmount = {{ installation.order.remaining_amount|default:0 }};
    
    if (amount > remainingAmount) {
        this.setCustomValidity('المبلغ أكبر من المتبقي');
    } else {
        this.setCustomValidity('');
    }
});

// تحديث نوع الدفع
document.getElementById('{{ form.payment_type.id_for_label }}').addEventListener('change', function() {
    const paymentType = this.value;
    const amountField = document.getElementById('{{ form.amount.id_for_label }}');
    
    if (paymentType === 'remaining') {
        amountField.value = {{ installation.order.remaining_amount|default:0 }};
        amountField.readOnly = true;
    } else {
        amountField.readOnly = false;
    }
});
</script>
{% endblock %} 