{% extends 'base.html' %}
{% load static %}

{% block title %}تأكيد حذف السائق - {{ driver.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        تأكيد حذف السائق
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير</h6>
                        <p>أنت على وشك حذف السائق <strong>{{ driver.name }}</strong> من النظام.</p>
                        <p class="mb-0">هذا الإجراء لا يمكن التراجع عنه.</p>
                    </div>

                    <div class="card border-left-primary">
                        <div class="card-body">
                            <h6>بيانات السائق:</h6>
                            <p><strong>الاسم:</strong> {{ driver.name }}</p>
                            <p><strong>الهاتف:</strong> {{ driver.phone }}</p>
                            {% if driver.license_number %}
                            <p><strong>رقم الرخصة:</strong> {{ driver.license_number }}</p>
                            {% endif %}
                            {% if driver.vehicle_number %}
                            <p><strong>رقم المركبة:</strong> {{ driver.vehicle_number }}</p>
                            {% endif %}
                            <p><strong>الحالة:</strong> 
                                <span class="badge badge-{{ driver.is_active|yesno:'success,secondary' }}">
                                    {{ driver.is_active|yesno:'نشط,غير نشط' }}
                                </span>
                            </p>
                        </div>
                    </div>

                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-6">
                                <a href="{% url 'installations:team_management' %}" class="btn btn-secondary btn-block">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </a>
                            </div>
                            <div class="col-6">
                                <button type="submit" class="btn btn-danger btn-block">
                                    <i class="fas fa-trash"></i>
                                    تأكيد الحذف
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
