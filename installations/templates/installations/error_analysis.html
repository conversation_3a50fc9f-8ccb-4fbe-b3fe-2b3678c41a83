{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}تحليل أخطاء التعديلات{% endblock %}

{% block extra_css %}
<style>
    .error-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
    }
    .error-stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
    }
    .error-stat-card h4 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: bold;
    }
    .error-stat-card p {
        margin: 5px 0 0 0;
        opacity: 0.9;
        font-size: 0.9rem;
    }
    .error-type-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    .error-type-measurement { background-color: #ffc107; color: #000; }
    .error-type-design { background-color: #dc3545; color: #fff; }
    .error-type-material { background-color: #17a2b8; color: #fff; }
    .error-type-installation { background-color: #28a745; color: #fff; }
    .error-type-customer_request { background-color: #6f42c1; color: #fff; }
    .error-type-quality { background-color: #fd7e14; color: #fff; }
    .error-type-other { background-color: #6c757d; color: #fff; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">تحليل أخطاء التعديلات</h1>
        <div>
            <a href="{% url 'installations:dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <!-- إحصائيات الأخطاء -->
    <div class="error-stats">
        <div class="error-stat-card">
                                    <h4>{{ total_cost_impact|currency_format }}</h4>
            <p>إجمالي التأثير المالي (جنيه)</p>
        </div>
        <div class="error-stat-card">
            <h4>{{ total_time_impact }}</h4>
            <p>إجمالي التأثير الزمني (ساعة)</p>
        </div>
        <div class="error-stat-card">
            <h4>{{ error_analyses.count }}</h4>
            <p>إجمالي تحليلات الأخطاء</p>
        </div>
        <div class="error-stat-card">
            <h4>{{ error_stats.values|length }}</h4>
            <p>أنواع مختلفة من الأخطاء</p>
        </div>
    </div>

    <!-- توزيع أنواع الأخطاء -->
    <div class="row mb-4">
        <div class="col-xl-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">توزيع أنواع الأخطاء</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="errorTypesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">التأثير المالي والزمني</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="impactChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول تحليلات الأخطاء -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">تحليلات الأخطاء التفصيلية</h6>
            <div>
                <button class="btn btn-sm btn-outline-primary" onclick="exportTable()">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="errorAnalysisTable">
                    <thead class="thead-dark">
                        <tr>
                            <th>رقم الطلب</th>
                            <th>نوع الخطأ</th>
                            <th>وصف الخطأ</th>
                            <th>السبب الجذري</th>
                            <th>الحل المطبق</th>
                            <th>التأثير المالي</th>
                            <th>التأثير الزمني</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for analysis in error_analyses %}
                        <tr>
                            <td>
                                <a href="{% url 'installations:modification_detail' analysis.modification_request.id %}">
                                    {{ analysis.modification_request.installation.order.order_number }}
                                </a>
                            </td>
                            <td>
                                <span class="error-type-badge error-type-{{ analysis.error_type }}">
                                    {{ analysis.get_error_type_display }}
                                </span>
                            </td>
                            <td>
                                <span title="{{ analysis.error_description }}">
                                    {{ analysis.error_description|truncatechars:50 }}
                                </span>
                            </td>
                            <td>
                                <span title="{{ analysis.root_cause }}">
                                    {{ analysis.root_cause|truncatechars:50 }}
                                </span>
                            </td>
                            <td>
                                <span title="{{ analysis.solution_applied }}">
                                    {{ analysis.solution_applied|truncatechars:50 }}
                                </span>
                            </td>
                            <td>{{ analysis.cost_impact|currency_format }}</td>
                            <td>{{ analysis.time_impact_hours }} ساعة</td>
                            <td>{{ analysis.created_at|date:"Y-m-d" }}</td>
                            <td>
                                <button class="btn btn-sm btn-info" onclick="showErrorDetails({{ analysis.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- ترقيم الصفحات -->
    {% if error_analyses.has_other_pages %}
    <nav aria-label="ترقيم الصفحات">
        <ul class="pagination justify-content-center">
            {% if error_analyses.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ error_analyses.previous_page_number }}">السابق</a>
                </li>
            {% endif %}

            {% for num in error_analyses.paginator.page_range %}
                {% if error_analyses.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > error_analyses.number|add:'-3' and num < error_analyses.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if error_analyses.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ error_analyses.next_page_number }}">التالي</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- Modal تفاصيل الخطأ -->
<div class="modal fade" id="errorDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل تحليل الخطأ</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="errorDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // بيانات أنواع الأخطاء
    const errorTypes = [{% for error_type, label in error_types %}'{{ label }}'{% if not forloop.last %}, {% endif %}{% endfor %}];
    const errorCounts = [{% for error_type, count in error_stats.items %}{{ count }}{% if not forloop.last %}, {% endif %}{% endfor %}];

    // رسم بياني لأنواع الأخطاء
    const errorTypesCtx = document.getElementById('errorTypesChart').getContext('2d');
    new Chart(errorTypesCtx, {
        type: 'doughnut',
        data: {
            labels: errorTypes,
            datasets: [{
                data: errorCounts,
                backgroundColor: [
                    '#ffc107', '#dc3545', '#17a2b8', '#28a745', 
                    '#6f42c1', '#fd7e14', '#6c757d'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });

    // رسم بياني للتأثير
    const impactCtx = document.getElementById('impactChart').getContext('2d');
    new Chart(impactCtx, {
        type: 'bar',
        data: {
            labels: ['التأثير المالي', 'التأثير الزمني'],
            datasets: [{
                label: 'التأثير',
                data: [{{ total_cost_impact }}, {{ total_time_impact }}],
                backgroundColor: ['rgba(255, 99, 132, 0.8)', 'rgba(54, 162, 235, 0.8)']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});

function showErrorDetails(analysisId) {
    // هنا يمكن إضافة AJAX لجلب تفاصيل الخطأ
    $('#errorDetailsModal').modal('show');
}

function exportTable() {
    // تصدير الجدول إلى Excel
    const table = document.getElementById('errorAnalysisTable');
    const html = table.outerHTML;
    const url = 'data:application/vnd.ms-excel,' + encodeURIComponent(html);
    const downloadLink = document.createElement("a");
    document.body.appendChild(downloadLink);
    downloadLink.href = url;
    downloadLink.download = 'تحليل_الأخطاء.xls';
    downloadLink.target = '_blank';
    downloadLink.click();
}
</script>
{% endblock %} 