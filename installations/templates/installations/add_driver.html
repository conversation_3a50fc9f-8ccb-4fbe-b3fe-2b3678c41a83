{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة سائق جديد{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-truck"></i>
                        إضافة سائق جديد
                    </h6>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                {{ form.name.label }}
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">
                                {{ form.phone.label }}
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.phone.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.license_number.id_for_label }}" class="form-label">
                                {{ form.license_number.label }}
                            </label>
                            {{ form.license_number }}
                            {% if form.license_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.license_number.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.vehicle_number.id_for_label }}" class="form-label">
                                {{ form.vehicle_number.label }}
                            </label>
                            {{ form.vehicle_number }}
                            {% if form.vehicle_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.vehicle_number.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.is_active.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'installations:team_management' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ السائق
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 