{% if installations %}
    <div class="table-responsive">
        <table class="table table-bordered table-hover" width="100%" cellspacing="0">
            <thead style="background-color: #6c757d !important;">
                <tr>
                    <th style="color: white !important;">رقم الطلب</th>
                    <th style="color: white !important;">العميل</th>
                    <th style="color: white !important;">تاريخ التركيب</th>
                    <th style="color: white !important;">موعد التركيب</th>
                    <th style="color: white !important;">الفريق</th>
                    <th style="color: white !important;">الحالة</th>
                    <th style="color: white !important;">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="bg-white">
                {% for installation in installations %}
                <tr class="border-bottom">
                    <td class="text-dark">
                        <a href="{% url 'orders:order_detail' installation.order.id %}" class="text-primary font-weight-bold">
                            {{ installation.order.order_number }}
                        </a>
                    </td>
                    <td class="text-dark font-weight-bold">{{ installation.order.customer.name }}</td>
                    <td class="text-dark">
                        <span class="badge badge-info">{{ installation.scheduled_date|date:"d/m/Y" }}</span>
                    </td>
                    <td class="text-dark">
                        <span class="badge badge-warning">{{ installation.scheduled_time|time:"H:i" }}</span>
                    </td>
                    <td class="text-dark">
                        {% if installation.team %}
                            <span class="badge badge-primary">{{ installation.team.name }}</span>
                        {% else %}
                            <span class="text-muted">غير محدد</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if installation.status == 'scheduled' %}
                            <span class="badge badge-info font-weight-bold" style="font-family: 'Cairo', sans-serif;">
                                <i class="fas fa-calendar-check"></i> مجدول
                            </span>
                        {% elif installation.status == 'in_progress' %}
                            <span class="badge badge-warning font-weight-bold" style="font-family: 'Cairo', sans-serif;">
                                <i class="fas fa-tools"></i> قيد التنفيذ
                            </span>
                        {% elif installation.status == 'completed' %}
                            <span class="badge badge-success font-weight-bold" style="font-family: 'Cairo', sans-serif;">
                                <i class="fas fa-check-circle"></i> مكتمل
                            </span>
                        {% elif installation.status == 'pending' %}
                            <span class="badge badge-secondary font-weight-bold" style="font-family: 'Cairo', sans-serif;">
                                <i class="fas fa-clock"></i> في الانتظار
                            </span>
                        {% elif installation.status == 'needs_scheduling' %}
                            <span class="badge badge-warning font-weight-bold" style="font-family: 'Cairo', sans-serif;">
                                <i class="fas fa-calendar-plus"></i> بحاجة جدولة
                            </span>
                        {% elif installation.status == 'in_installation' %}
                            <span class="badge badge-primary font-weight-bold" style="font-family: 'Cairo', sans-serif;">
                                <i class="fas fa-tools"></i> قيد التركيب
                            </span>
                        {% elif installation.status == 'cancelled' %}
                            <span class="badge badge-danger font-weight-bold" style="font-family: 'Cairo', sans-serif;">
                                <i class="fas fa-times-circle"></i> ملغي
                            </span>
                        {% elif installation.status == 'modification_required' %}
                            <span class="badge badge-warning font-weight-bold" style="font-family: 'Cairo', sans-serif;">
                                <i class="fas fa-exclamation-triangle"></i> يحتاج تعديل
                            </span>
                        {% elif installation.status == 'modification_in_progress' %}
                            <span class="badge badge-info font-weight-bold" style="font-family: 'Cairo', sans-serif;">
                                <i class="fas fa-cogs"></i> التعديل قيد التنفيذ
                            </span>
                        {% elif installation.status == 'modification_completed' %}
                            <span class="badge badge-success font-weight-bold" style="font-family: 'Cairo', sans-serif;">
                                <i class="fas fa-check-circle"></i> التعديل مكتمل
                            </span>
                        {% else %}
                            <span class="badge badge-secondary font-weight-bold" style="font-family: 'Cairo', sans-serif;">
                                {{ installation.get_status_display }}
                            </span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{% url 'installations:installation_detail' installation.id %}" 
                               class="btn btn-sm btn-info">
                                <i class="fas fa-tools"></i> تفاصيل التركيب
                            </a>
                            <a href="{% url 'installations:installation_detail' installation.id %}" 
                               class="btn btn-sm btn-secondary">
                                <i class="fas fa-tools"></i> تفاصيل التركيب
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="text-center py-4">
        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
        <p class="text-muted font-weight-bold">لا توجد تركيبات مجدولة</p>
    </div>
{% endif %} 