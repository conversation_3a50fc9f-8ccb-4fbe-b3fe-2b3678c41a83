{% extends 'base.html' %}
{% load static %}

{% block title %}جدولة التركيب - {{ installation.order.order_number }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar-plus"></i>
                        جدولة التركيب - {{ installation.order.order_number }}
                    </h6>
                </div>
                <div class="card-body">
                    <!-- معلومات الطلب -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> معلومات الطلب:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>تاريخ الطلب:</strong> {{ installation.order.created_at|date:"Y-m-d" }}</p>
                                <strong>رقم الطلب:</strong> {{ installation.order.order_number }}<br>
                                <strong>العميل:</strong> {{ installation.order.customer.name }}<br>
                                <strong>الهاتف:</strong> {{ installation.order.customer.phone }}
                            </div>
                            <div class="col-md-6">
                                <strong>العنوان:</strong> {{ installation.order.customer.address|default:"غير محدد" }}<br>
                                <strong>نوع المكان:</strong> {{ installation.order.location_type|default:"غير محدد" }}<br>
                                <strong>متبقي الحساب:</strong> {{ installation.order.remaining_amount|default:"0"|currency_format }}
                            </div>
                        </div>
                    </div>

                    <!-- نموذج الجدولة -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.team.id_for_label }}" class="form-label">
                                    {{ form.team.label }}
                                </label>
                                {{ form.team }}
                                {% if form.team.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.team.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    اختر الفريق المسؤول عن التركيب
                                </small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.scheduled_date.id_for_label }}" class="form-label">
                                    {{ form.scheduled_date.label }}
                                </label>
                                {{ form.scheduled_date }}
                                {% if form.scheduled_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.scheduled_date.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    {{ form.scheduled_date.help_text }}
                                </small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.scheduled_time.id_for_label }}" class="form-label">
                                    {{ form.scheduled_time.label }}
                                </label>
                                {{ form.scheduled_time }}
                                {% if form.scheduled_time.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.scheduled_time.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    {{ form.scheduled_time.help_text }}
                                </small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.windows_count.id_for_label }}" class="form-label">
                                    {{ form.windows_count.label }}
                                </label>
                                {{ form.windows_count }}
                                {% if form.windows_count.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.windows_count.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    {{ form.windows_count.help_text }}
                                </small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.location_type.id_for_label }}" class="form-label">
                                    {{ form.location_type.label }}
                                </label>
                                {{ form.location_type }}
                                {% if form.location_type.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.location_type.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    {{ form.location_type.help_text }}
                                </small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">
                                    {{ form.notes.label }}
                                </label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.notes.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معلومات الفريق المختار -->
                        <div id="team-info" class="alert alert-success" style="display: none;">
                            <h6><i class="fas fa-users"></i> معلومات الفريق:</h6>
                            <div id="team-details"></div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-calendar-check"></i> جدولة التركيب
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// عرض معلومات الفريق عند اختياره
document.getElementById('{{ form.team.id_for_label }}').addEventListener('change', function() {
    const teamId = this.value;
    const teamInfo = document.getElementById('team-info');
    const teamDetails = document.getElementById('team-details');
    
    if (teamId) {
        // هنا يمكن إضافة طلب AJAX لجلب معلومات الفريق
        // للآن سنعرض رسالة بسيطة
        teamDetails.innerHTML = `
            <p><strong>تم اختيار الفريق:</strong> ${this.options[this.selectedIndex].text}</p>
            <p class="text-muted">سيتم عرض تفاصيل الفريق قريباً</p>
        `;
        teamInfo.style.display = 'block';
    } else {
        teamInfo.style.display = 'none';
    }
});

// التحقق من التاريخ والوقت
document.getElementById('{{ form.scheduled_date.id_for_label }}').addEventListener('change', function() {
    const selectedDate = new Date(this.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (selectedDate < today) {
        Swal.fire('خطأ', 'لا يمكن اختيار تاريخ ماضي', 'error');
        this.value = '';
    }
});
</script>
{% endblock %} 