{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الجدولة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للتفاصيل
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات التركيب -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4>معلومات التركيب</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>رقم الطلب:</strong></td>
                                            <td>{{ installation.order.order_number }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>العميل:</strong></td>
                                            <td>{{ installation.order.customer.name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ التركيب:</strong></td>
                                            <td>{{ installation.scheduled_date|date:"Y-m-d" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>وقت التركيب:</strong></td>
                                            <td>{{ installation.scheduled_time|time:"H:i" }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الفريق:</strong></td>
                                            <td>
                                                {% if installation.team %}
                                                    {{ installation.team.name }}
                                                {% else %}
                                                    <span class="text-muted">غير محدد</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                <span class="badge badge-{% if installation.status == 'completed' %}success{% elif installation.status == 'in_progress' %}warning{% elif installation.status == 'cancelled' %}danger{% else %}info{% endif %}">
                                                    {{ installation.get_status_display }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4>التركيبات في نفس اليوم</h4>
                                </div>
                                <div class="card-body">
                                    {% if same_day_installations %}
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>رقم الطلب</th>
                                                        <th>العميل</th>
                                                        <th>الوقت</th>
                                                        <th>الفريق</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for other_installation in same_day_installations %}
                                                        <tr>
                                                            <td>
                                                                <a href="{% url 'installations:installation_detail' other_installation.id %}">
                                                                    {{ other_installation.order.order_number }}
                                                                </a>
                                                            </td>
                                                            <td>{{ other_installation.order.customer.name }}</td>
                                                            <td>{{ other_installation.scheduled_time|time:"H:i" }}</td>
                                                            <td>
                                                                {% if other_installation.team %}
                                                                    {{ other_installation.team.name }}
                                                                {% else %}
                                                                    <span class="text-muted">غير محدد</span>
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    {% else %}
                                        <p class="text-muted">لا توجد تركيبات أخرى في نفس اليوم</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- سجل تغييرات الجدولة -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>سجل تغييرات الجدولة</h4>
                                </div>
                                <div class="card-body">
                                    {% if schedule_events %}
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>التاريخ والوقت</th>
                                                        <th>وصف التغيير</th>
                                                        <th>المستخدم</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for event in schedule_events %}
                                                        <tr>
                                                            <td>{{ event.created_at|date:"Y-m-d H:i" }}</td>
                                                            <td>{{ event.description }}</td>
                                                            <td>{{ event.user.get_full_name|default:event.user.username }}</td>
                                                        </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    {% else %}
                                        <p class="text-muted">لا توجد تغييرات في الجدولة</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="btn-group">
                                <a href="{% url 'installations:edit_schedule' installation.id %}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> تعديل الجدولة
                                </a>
                                <a href="{% url 'installations:edit_scheduling_settings' installation.id %}" class="btn btn-info">
                                    <i class="fas fa-cog"></i> إعدادات الجدولة
                                </a>
                                <a href="{% url 'installations:print_installation_schedule' %}?date={{ installation.scheduled_date }}&team={{ installation.team.id }}" 
                                   class="btn btn-secondary" target="_blank">
                                    <i class="fas fa-print"></i> طباعة الجدول
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}