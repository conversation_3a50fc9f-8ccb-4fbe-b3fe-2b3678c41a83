{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-tools"></i>
                        {{ title }}
                    </h6>
                    <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-sm btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للتركيب
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="{{ form.modification_type.id_for_label }}" class="form-label">
                                                <strong>نوع التعديل</strong>
                                            </label>
                                            {{ form.modification_type }}
                                            {% if form.modification_type.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ form.modification_type.errors }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="{{ form.priority.id_for_label }}" class="form-label">
                                                <strong>الأولوية</strong>
                                            </label>
                                            {{ form.priority }}
                                            {% if form.priority.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ form.priority.errors }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">
                                        <strong>تفاصيل التعديل</strong>
                                    </label>
                                    {{ form.description }}
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.description.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="{{ form.estimated_cost.id_for_label }}" class="form-label">
                                                <strong>التكلفة المتوقعة</strong>
                                            </label>
                                            {{ form.estimated_cost }}
                                            {% if form.estimated_cost.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ form.estimated_cost.errors }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="form-check mt-4">
                                                {{ form.customer_approval }}
                                                <label class="form-check-label" for="{{ form.customer_approval.id_for_label }}">
                                                    <strong>موافقة العميل</strong>
                                                </label>
                                                {% if form.customer_approval.errors %}
                                                    <div class="invalid-feedback d-block">
                                                        {{ form.customer_approval.errors }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save"></i>
                                        إنشاء طلب التعديل
                                    </button>
                                    <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card border-left-warning">
                                <div class="card-body">
                                    <h6 class="card-title text-warning">
                                        <i class="fas fa-info-circle"></i>
                                        معلومات التركيب
                                    </h6>
                                    <div class="mt-3">
                                        <p><strong>رقم الطلب:</strong> {{ installation.order.order_number }}</p>
                                        <p><strong>العميل:</strong> {{ installation.order.customer.name }}</p>
                                        <p><strong>تاريخ التركيب:</strong> {{ installation.scheduled_date|date:"Y-m-d" }}</p>
                                        <p><strong>موعد التركيب:</strong> {{ installation.scheduled_time|time:"H:i" }}</p>
                                        <p><strong>الفريق:</strong> {{ installation.team.name|default:"غير محدد" }}</p>
                                        <p><strong>الحالة الحالية:</strong> 
                                            <span class="badge badge-{{ installation.status|yesno:'success,warning' }}">
                                                {{ installation.get_status_display }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card border-left-info mt-3">
                                <div class="card-body">
                                    <h6 class="card-title text-info">
                                        <i class="fas fa-lightbulb"></i>
                                        ملاحظات مهمة
                                    </h6>
                                    <div class="mt-3">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success"></i> سيتم إنشاء أمر تصنيع من نوع تعديل</li>
                                            <li><i class="fas fa-check text-success"></i> سيتم عرض الطلب في لوحة المصنع</li>
                                            <li><i class="fas fa-check text-success"></i> يمكن رفع صور للتعديل لاحقاً</li>
                                            <li><i class="fas fa-check text-success"></i> سيتم تحديث حالة التركيب تلقائياً</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-control {
    border-radius: 8px;
    border: 1px solid #d1d3e2;
}

.form-control:focus {
    border-color: #f6c23e;
    box-shadow: 0 0 0 0.2rem rgba(246, 194, 62, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
}

.card {
    border-radius: 12px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.badge {
    font-size: 0.8em;
    padding: 0.5em 0.8em;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.form-check-input:checked {
    background-color: #f6c23e;
    border-color: #f6c23e;
}
</style>
{% endblock %} 