{% extends 'base.html' %}
{% load static %}

{% block title %}تعديل جدولة التركيب - {{ installation.order.order_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-edit text-primary"></i>
            تعديل جدولة التركيب - {{ installation.order.order_number }}
        </h1>
        <div class="btn-group">
            <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i> العودة للتفاصيل
            </a>
            <a href="{% url 'installations:dashboard' %}" class="btn btn-outline-primary">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar-edit"></i>
                        تعديل جدولة التركيب
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.scheduled_date.id_for_label }}">تاريخ التركيب:</label>
                                    {{ form.scheduled_date }}
                                    {% if form.scheduled_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.scheduled_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.scheduled_time.id_for_label }}">موعد التركيب:</label>
                                    {{ form.scheduled_time }}
                                    {% if form.scheduled_time.errors %}
                                        <div class="text-danger">
                                            {% for error in form.scheduled_time.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.team.id_for_label }}">الفريق:</label>
                                    {{ form.team }}
                                    {% if form.team.errors %}
                                        <div class="text-danger">
                                            {% for error in form.team.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.status.id_for_label }}">الحالة:</label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="text-danger">
                                            {% for error in form.status.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.notes.id_for_label }}">ملاحظات:</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- معلومات الطلب -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i>
                        معلومات الطلب
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th>رقم الطلب:</th>
                            <td>{{ installation.order.order_number }}</td>
                        </tr>
                        <tr>
                            <th>اسم العميل:</th>
                            <td>{{ installation.order.customer.name }}</td>
                        </tr>
                        <tr>
                            <th>رقم الهاتف:</th>
                            <td>{{ installation.order.customer.phone }}</td>
                        </tr>
                        <tr>
                            <th>العنوان:</th>
                            <td>{{ installation.order.customer.address|default:"غير محدد" }}</td>
                        </tr>
                        <tr>
                            <th>حالة الطلب:</th>
                            <td>
                                {% if installation.order.order_status == 'pending' %}
                                    <span class="badge badge-warning">في الانتظار</span>
                                {% elif installation.order.order_status == 'in_progress' %}
                                    <span class="badge badge-primary">قيد التنفيذ</span>
                                {% elif installation.order.order_status == 'completed' %}
                                    <span class="badge badge-success">مكتمل</span>
                                {% else %}
                                    <span class="badge badge-secondary">{{ installation.order.get_order_status_display }}</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .form-control {
        border-radius: 0.35rem;
    }
    .btn {
        border-radius: 0.35rem;
    }
</style>
{% endblock %} 