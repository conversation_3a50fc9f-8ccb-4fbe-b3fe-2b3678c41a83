{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة مذكرة استلام - {{ installation.order.order_number }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-receipt"></i>
                        إضافة مذكرة استلام - {{ installation.order.order_number }}
                    </h6>
                </div>
                <div class="card-body">
                    <!-- معلومات الطلب -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> معلومات الطلب:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>رقم الطلب:</strong> {{ installation.order.order_number }}<br>
                                <strong>العميل:</strong> {{ installation.order.customer.name }}<br>
                                <strong>متبقي الحساب:</strong> {{ installation.order.remaining_amount|default:"0"|currency_format }}
                            </div>
                            <div class="col-md-6">
                                <strong>العنوان:</strong> {{ installation.order.customer.address|default:"غير محدد" }}<br>
                                <strong>الهاتف:</strong> {{ installation.order.customer.phone }}<br>
                                <strong>حالة التركيب:</strong> 
                                <span class="badge badge-{{ installation.status|yesno:'success,warning' }}">
                                    {{ installation.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج مذكرة الاستلام -->
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.amount_received.id_for_label }}" class="form-label">
                                    {{ form.amount_received.label }}
                                </label>
                                {{ form.amount_received }}
                                {% if form.amount_received.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.amount_received.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    المبلغ المستلم من العميل
                                </small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    {{ form.customer_signature }}
                                    <label class="form-check-label" for="{{ form.customer_signature.id_for_label }}">
                                        {{ form.customer_signature.label }}
                                    </label>
                                </div>
                                {% if form.customer_signature.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.customer_signature.errors.0 }}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    تأكيد توقيع العميل على مذكرة الاستلام
                                </small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.receipt_image.id_for_label }}" class="form-label">
                                {{ form.receipt_image.label }}
                            </label>
                            {{ form.receipt_image }}
                            {% if form.receipt_image.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.receipt_image.errors.0 }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                صورة واضحة لمذكرة الاستلام. الحد الأقصى 2 ميجابايت
                            </small>
                        </div>

                        <!-- معاينة الصورة -->
                        <div id="image-preview" class="mb-3" style="display: none;">
                            <h6>معاينة الصورة:</h6>
                            <div id="preview-content"></div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                ملاحظات إضافية حول مذكرة الاستلام
                            </small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ مذكرة الاستلام
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// معاينة الصورة
document.getElementById('{{ form.receipt_image.id_for_label }}').addEventListener('change', function() {
    const file = this.files[0];
    const preview = document.getElementById('image-preview');
    const previewContent = document.getElementById('preview-content');
    
    if (file) {
        preview.style.display = 'block';
        
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContent.innerHTML = `
                    <img src="${e.target.result}" class="img-fluid rounded" style="max-height: 300px;" alt="معاينة مذكرة الاستلام">
                    <p class="mt-2"><strong>اسم الملف:</strong> ${file.name}</p>
                    <p><strong>الحجم:</strong> ${(file.size / 1024 / 1024).toFixed(2)} ميجابايت</p>
                `;
            };
            reader.readAsDataURL(file);
        } else {
            Swal.fire('خطأ', 'يرجى اختيار ملف صورة صالح', 'error');
            this.value = '';
            preview.style.display = 'none';
        }
    } else {
        preview.style.display = 'none';
    }
});

// التحقق من حجم الملف
document.getElementById('{{ form.receipt_image.id_for_label }}').addEventListener('change', function() {
    const file = this.files[0];
    const maxSize = 2 * 1024 * 1024; // 2MB
    
    if (file && file.size > maxSize) {
        Swal.fire('خطأ', 'حجم الصورة يجب أن يكون أقل من 2 ميجابايت', 'error');
        this.value = '';
        document.getElementById('image-preview').style.display = 'none';
    }
});

// تحديث المبلغ المستلم
document.getElementById('{{ form.amount_received.id_for_label }}').addEventListener('input', function() {
    const amount = parseFloat(this.value);
    const remainingAmount = {{ installation.order.remaining_amount|default:0 }};
    
    if (amount > remainingAmount) {
        this.setCustomValidity('المبلغ أكبر من المتبقي');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %} 