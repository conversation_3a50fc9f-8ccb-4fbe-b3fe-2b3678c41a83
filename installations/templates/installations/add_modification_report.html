{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة تقرير تعديل - {{ installation.order.order_number }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-file-alt"></i>
                        إضافة تقرير تعديل - {{ installation.order.order_number }}
                    </h6>
                </div>
                <div class="card-body">
                    <!-- معلومات الطلب -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> معلومات الطلب:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>رقم الطلب:</strong> {{ installation.order.order_number }}<br>
                                <strong>العميل:</strong> {{ installation.order.customer.name }}<br>
                                <strong>العنوان:</strong> {{ installation.order.customer.address|default:"غير محدد" }}
                            </div>
                            <div class="col-md-6">
                                <strong>الفريق:</strong> {{ installation.team.name|default:"غير محدد" }}<br>
                                <strong>تاريخ التركيب:</strong> {{ installation.scheduled_date|date:"Y-m-d" }}<br>
                                <strong>حالة التركيب:</strong> 
                                <span class="badge badge-{{ installation.status|yesno:'success,warning' }}">
                                    {{ installation.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج التقرير -->
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.description.errors.0 }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                وصف مفصل للتعديل المطلوب أو المشكلة المكتشفة
                            </small>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.report_file.id_for_label }}" class="form-label">
                                {{ form.report_file.label }}
                            </label>
                            {{ form.report_file }}
                            {% if form.report_file.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.report_file.errors.0 }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                يمكن رفع ملف PDF أو صورة. الحد الأقصى 5 ميجابايت
                            </small>
                        </div>

                        <!-- معاينة الملف -->
                        <div id="file-preview" class="mb-3" style="display: none;">
                            <h6>معاينة الملف:</h6>
                            <div id="preview-content"></div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// معاينة الملف
document.getElementById('{{ form.report_file.id_for_label }}').addEventListener('change', function() {
    const file = this.files[0];
    const preview = document.getElementById('file-preview');
    const previewContent = document.getElementById('preview-content');
    
    if (file) {
        preview.style.display = 'block';
        
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContent.innerHTML = `
                    <img src="${e.target.result}" class="img-fluid rounded" style="max-height: 200px;" alt="معاينة الصورة">
                    <p class="mt-2"><strong>اسم الملف:</strong> ${file.name}</p>
                    <p><strong>الحجم:</strong> ${(file.size / 1024 / 1024).toFixed(2)} ميجابايت</p>
                `;
            };
            reader.readAsDataURL(file);
        } else {
            previewContent.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-file-pdf"></i>
                    <strong>اسم الملف:</strong> ${file.name}<br>
                    <strong>الحجم:</strong> ${(file.size / 1024 / 1024).toFixed(2)} ميجابايت<br>
                    <strong>النوع:</strong> ${file.type}
                </div>
            `;
        }
    } else {
        preview.style.display = 'none';
    }
});

// التحقق من حجم الملف
document.getElementById('{{ form.report_file.id_for_label }}').addEventListener('change', function() {
    const file = this.files[0];
    const maxSize = 5 * 1024 * 1024; // 5MB
    
    if (file && file.size > maxSize) {
        Swal.fire('خطأ', 'حجم الملف يجب أن يكون أقل من 5 ميجابايت', 'error');
        this.value = '';
        document.getElementById('file-preview').style.display = 'none';
    }
});
</script>
{% endblock %} 