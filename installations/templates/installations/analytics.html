{% extends 'base.html' %}
{% load static %}

{% block title %}تحليل التركيبات الشهري{% endblock %}

{% block extra_css %}
<style>
    .analytics-card {
        transition: transform 0.2s;
    }
    .analytics-card:hover {
        transform: translateY(-2px);
    }
    .chart-container {
        position: relative;
        height: 400px;
        margin: 20px 0;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
    }
    .stat-card h3 {
        margin: 0;
        font-size: 2rem;
        font-weight: bold;
    }
    .stat-card p {
        margin: 5px 0 0 0;
        opacity: 0.9;
    }
    .progress-bar {
        height: 8px;
        border-radius: 4px;
        background: rgba(255,255,255,0.2);
        margin-top: 10px;
    }
    .progress-fill {
        height: 100%;
        border-radius: 4px;
        background: rgba(255,255,255,0.8);
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">تحليل التركيبات الشهري</h1>
        <div>
            <form method="get" class="d-inline">
                <select name="months" class="form-control d-inline-block w-auto" onchange="this.form.submit()">
                    <option value="3" {% if months_back == 3 %}selected{% endif %}>آخر 3 أشهر</option>
                    <option value="6" {% if months_back == 6 %}selected{% endif %}>آخر 6 أشهر</option>
                    <option value="12" {% if months_back == 12 %}selected{% endif %}>آخر 12 شهر</option>
                </select>
            </form>
        </div>
    </div>

    <!-- إحصائيات إجمالية -->
    <div class="stats-grid">
        <div class="stat-card">
            <h3>{{ total_stats.total_installations }}</h3>
            <p>إجمالي التركيبات</p>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 100%"></div>
            </div>
        </div>
        <div class="stat-card">
            <h3>{{ total_stats.total_completed }}</h3>
            <p>التركيبات المكتملة</p>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {{ total_stats.avg_completion_rate }}%"></div>
            </div>
        </div>
        <div class="stat-card">
            <h3>{{ total_stats.total_modifications }}</h3>
            <p>إجمالي التعديلات</p>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {{ total_stats.avg_modification_rate }}%"></div>
            </div>
        </div>
        <div class="stat-card">
            <h3>{{ total_stats.avg_completion_rate|floatformat:1 }}%</h3>
            <p>متوسط نسبة الإكمال</p>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {{ total_stats.avg_completion_rate }}%"></div>
            </div>
        </div>
    </div>

    <!-- جدول البيانات الشهرية -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">البيانات الشهرية</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="analyticsTable">
                    <thead class="thead-dark">
                        <tr>
                            <th>الشهر</th>
                            <th>إجمالي التركيبات</th>
                            <th>مكتمل</th>
                            <th>في الانتظار</th>
                            <th>قيد التنفيذ</th>
                            <th>العملاء</th>
                            <th>التعديلات</th>
                            <th>نسبة الإكمال</th>
                            <th>نسبة التعديلات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for data in analytics_data %}
                        <tr>
                            <td>{{ data.month|date:"Y-m" }}</td>
                            <td>{{ data.total_installations }}</td>
                            <td>{{ data.completed_installations }}</td>
                            <td>{{ data.pending_installations }}</td>
                            <td>{{ data.in_progress_installations }}</td>
                            <td>{{ data.total_customers }}</td>
                            <td>{{ data.total_modifications }}</td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-success" style="width: {{ data.completion_rate }}%">
                                        {{ data.completion_rate }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" style="width: {{ data.modification_rate }}%">
                                        {{ data.modification_rate }}%
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">تطور التركيبات الشهري</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="installationsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">نسب الإكمال والتعديلات</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="ratesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // بيانات الرسوم البيانية
    const months = [{% for data in analytics_data %}'{{ data.month|date:"Y-m" }}'{% if not forloop.last %}, {% endif %}{% endfor %}];
    const installations = [{% for data in analytics_data %}{{ data.total_installations }}{% if not forloop.last %}, {% endif %}{% endfor %}];
    const completed = [{% for data in analytics_data %}{{ data.completed_installations }}{% if not forloop.last %}, {% endif %}{% endfor %}];
    const modifications = [{% for data in analytics_data %}{{ data.total_modifications }}{% if not forloop.last %}, {% endif %}{% endfor %}];
    const completionRates = [{% for data in analytics_data %}{{ data.completion_rate }}{% if not forloop.last %}, {% endif %}{% endfor %}];
    const modificationRates = [{% for data in analytics_data %}{{ data.modification_rate }}{% if not forloop.last %}, {% endif %}{% endfor %}];

    // رسم بياني للتركيبات
    const installationsCtx = document.getElementById('installationsChart').getContext('2d');
    new Chart(installationsCtx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'إجمالي التركيبات',
                data: installations,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }, {
                label: 'مكتمل',
                data: completed,
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                tension: 0.1
            }, {
                label: 'التعديلات',
                data: modifications,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });

    // رسم بياني للنسب
    const ratesCtx = document.getElementById('ratesChart').getContext('2d');
    new Chart(ratesCtx, {
        type: 'bar',
        data: {
            labels: months,
            datasets: [{
                label: 'نسبة الإكمال %',
                data: completionRates,
                backgroundColor: 'rgba(75, 192, 192, 0.8)',
            }, {
                label: 'نسبة التعديلات %',
                data: modificationRates,
                backgroundColor: 'rgba(255, 99, 132, 0.8)',
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
});
</script>
{% endblock %} 