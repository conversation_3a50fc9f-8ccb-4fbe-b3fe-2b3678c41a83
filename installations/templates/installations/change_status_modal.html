<!-- نافذة منبثقة ذكية لتغيير حالة التركيب -->
<div class="modal-header bg-primary text-white">
    <h5 class="modal-title">
        <i class="fas fa-edit"></i>
        تغيير حالة التركيب - {{ installation.order.order_number }}
    </h5>
    <button type="button" class="close text-white" data-dismiss="modal" aria-label="إغلاق">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<form id="statusChangeForm" method="post">
    {% csrf_token %}
    <div class="modal-body">
        <!-- معلومات التركيب الحالية -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body p-3">
                        <h6 class="card-title text-primary">
                            <i class="fas fa-info-circle"></i>
                            معلومات التركيب
                        </h6>
                        <p class="mb-1"><strong>العميل:</strong> {{ installation.order.customer.name }}</p>
                        <p class="mb-1"><strong>رقم الطلب:</strong> {{ installation.order.order_number }}</p>
                        <p class="mb-1"><strong>الحالة الحالية:</strong> 
                            <span class="badge badge-info">{{ installation.get_status_display }}</span>
                        </p>
                        <p class="mb-0"><strong>تاريخ الجدولة:</strong> {{ installation.scheduled_date|date:"Y-m-d" }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-body p-3">
                        <h6 class="card-title text-success">
                            <i class="fas fa-users"></i>
                            فريق التركيب
                        </h6>
                        {% if installation.team %}
                            <p class="mb-1"><strong>الفريق:</strong> {{ installation.team.name }}</p>
                            <p class="mb-1"><strong>الفنيين:</strong> 
                                {% for tech in installation.team.technicians.all %}
                                    {{ tech.name }}{% if not forloop.last %}, {% endif %}
                                {% endfor %}
                            </p>
                            {% if installation.team.driver %}
                                <p class="mb-0"><strong>السائق:</strong> {{ installation.team.driver.name }}</p>
                            {% endif %}
                        {% else %}
                            <p class="text-muted">لم يتم تعيين فريق بعد</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج تغيير الحالة -->
        <div class="row">
            <div class="col-12">
                <div class="form-group">
                    <label for="{{ form.status.id_for_label }}" class="font-weight-bold">
                        <i class="fas fa-exchange-alt"></i>
                        الحالة الجديدة
                    </label>
                    {{ form.status }}
                    {% if form.status.help_text %}
                        <small class="form-text text-muted">{{ form.status.help_text }}</small>
                    {% endif %}
                    {% if form.status.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.status.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- حقل سبب التغيير -->
                <div class="form-group" id="reasonField">
                    <label for="{{ form.reason.id_for_label }}" class="font-weight-bold">
                        <i class="fas fa-comment"></i>
                        سبب التغيير
                    </label>
                    {{ form.reason }}
                    {% if form.reason.help_text %}
                        <small class="form-text text-muted">{{ form.reason.help_text }}</small>
                    {% endif %}
                    {% if form.reason.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.reason.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- حقل ملاحظات إضافية -->
                <div class="form-group">
                    <label for="{{ form.notes.id_for_label }}" class="font-weight-bold">
                        <i class="fas fa-sticky-note"></i>
                        ملاحظات إضافية
                    </label>
                    {{ form.notes }}
                    {% if form.notes.help_text %}
                        <small class="form-text text-muted">{{ form.notes.help_text }}</small>
                    {% endif %}
                    {% if form.notes.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.notes.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- حقل تاريخ الإكمال (مخفي افتراضياً) -->
                {% if form.completion_date.widget.input_type != 'hidden' %}
                <div class="form-group" id="completionDateField" style="display: none;">
                    <label for="{{ form.completion_date.id_for_label }}" class="font-weight-bold">
                        <i class="fas fa-calendar-check"></i>
                        تاريخ الإكمال
                    </label>
                    {{ form.completion_date }}
                    {% if form.completion_date.help_text %}
                        <small class="form-text text-muted">{{ form.completion_date.help_text }}</small>
                    {% endif %}
                    {% if form.completion_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.completion_date.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                {% else %}
                    {{ form.completion_date }}
                {% endif %}
            </div>
        </div>

        <!-- تحذيرات خاصة -->
        <div id="statusWarnings" class="mt-3"></div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fas fa-times"></i>
            إلغاء
        </button>
        <button type="submit" class="btn btn-primary" id="saveStatusBtn">
            <i class="fas fa-save"></i>
            حفظ التغيير
        </button>
    </div>
</form>

<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // مراقبة تغيير الحالة لإظهار/إخفاء الحقول المطلوبة
    $('#{{ form.status.id_for_label }}').on('change', function() {
        var selectedStatus = $(this).val();
        var currentStatus = '{{ installation.status }}';
        
        // إظهار/إخفاء حقل سبب التغيير
        if (selectedStatus === 'cancelled' || selectedStatus === 'modification_required') {
            $('#reasonField label').html('<i class="fas fa-comment"></i> سبب التغيير <span class="text-danger">*</span>');
            $('#{{ form.reason.id_for_label }}').prop('required', true);
        } else {
            $('#reasonField label').html('<i class="fas fa-comment"></i> سبب التغيير');
            $('#{{ form.reason.id_for_label }}').prop('required', false);
        }
        
        // إظهار/إخفاء حقل تاريخ الإكمال
        if (selectedStatus === 'completed') {
            $('#completionDateField').show();
            $('#{{ form.completion_date.id_for_label }}').prop('required', true);
            // تعيين التاريخ والوقت الحالي كقيمة افتراضية
            var now = new Date();
            var dateTimeString = now.getFullYear() + '-' + 
                                String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                                String(now.getDate()).padStart(2, '0') + 'T' + 
                                String(now.getHours()).padStart(2, '0') + ':' + 
                                String(now.getMinutes()).padStart(2, '0');
            $('#{{ form.completion_date.id_for_label }}').val(dateTimeString);
        } else {
            $('#completionDateField').hide();
            $('#{{ form.completion_date.id_for_label }}').prop('required', false);
        }
        
        // إظهار تحذيرات خاصة
        showStatusWarnings(selectedStatus, currentStatus);
    });
    
    // وظيفة إظهار التحذيرات
    function showStatusWarnings(newStatus, currentStatus) {
        var warningsDiv = $('#statusWarnings');
        warningsDiv.empty();
        
        if (newStatus === 'modification_required') {
            warningsDiv.html(`
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تنبيه:</strong> سيتم إنشاء طلب تعديل تلقائياً وإرساله إلى قسم التصنيع.
                </div>
            `);
        } else if (newStatus === 'completed') {
            warningsDiv.html(`
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>إكمال التركيب:</strong> سيتم تحديث حالة الطلب إلى "مكتمل" وإنشاء أرشيف للتركيب.
                </div>
            `);
        } else if (newStatus === 'cancelled') {
            warningsDiv.html(`
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i>
                    <strong>إلغاء التركيب:</strong> سيتم إلغاء جدولة التركيب. يمكن إعادة جدولته لاحقاً.
                </div>
            `);
        }
    }
    
    // معالجة إرسال النموذج
    $('#statusChangeForm').on('submit', function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        var submitBtn = $('#saveStatusBtn');
        var originalText = submitBtn.html();
        
        // تعطيل الزر وإظهار مؤشر التحميل
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
        
        $.ajax({
            url: '{% url "installations:change_installation_status" installation.id %}',
            type: 'POST',
            data: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    // إظهار رسالة نجاح جميلة
                    Swal.fire({
                        title: 'تم التحديث بنجاح!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'حسناً',
                        confirmButtonColor: '#28a745',
                        timer: 2000,
                        timerProgressBar: true
                    }).then(() => {
                        // إغلاق النافذة المنبثقة
                        $('#statusModal').modal('hide');
                        
                        // تحديث الصفحة
                        location.reload();
                    });
                } else {
                    // إظهار أخطاء النموذج
                    if (response.errors) {
                        var errorMessages = [];
                        for (var field in response.errors) {
                            errorMessages = errorMessages.concat(response.errors[field]);
                        }
                        Swal.fire({
                            title: 'خطأ في التحديث',
                            text: 'خطأ: ' + errorMessages.join(', '),
                            icon: 'error',
                            confirmButtonText: 'حسناً',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                }
            },
            error: function(xhr, status, error) {
                Swal.fire({
                    title: 'خطأ في التحديث',
                    text: 'حدث خطأ أثناء حفظ التغيير',
                    icon: 'error',
                    confirmButtonText: 'حسناً',
                    confirmButtonColor: '#dc3545'
                });
            },
            complete: function() {
                // إعادة تفعيل الزر
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>

<style>
.modal-header.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.card.bg-light {
    background-color: #f8f9fc !important;
    border: 1px solid #e3e6f0;
}

.badge {
    font-size: 0.85em;
    padding: 0.4em 0.6em;
}

.form-group label {
    color: #5a5c69;
    font-weight: 600;
}

.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn {
    border-radius: 6px;
    font-weight: 500;
}

.modal-content {
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.form-control {
    border-radius: 6px;
    border: 1px solid #d1d3e2;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}
</style>