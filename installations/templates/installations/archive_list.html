{% extends 'base.html' %}
{% load static %}

{% block title %}أرشيف التركيبات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-archive text-primary"></i>
            أرشيف التركيبات
        </h1>
        <a href="{% url 'installations:dashboard' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
        </a>
    </div>

    <!-- إحصائيات الأرشيف -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                إجمالي التركيبات المكتملة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ archives.count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الأرشيف -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table"></i>
                التركيبات المكتملة
            </h6>
        </div>
        <div class="card-body">
            {% if archives %}
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>العميل</th>
                                <th>الفريق</th>
                                <th>تاريخ التركيب</th>
                                <th>تاريخ الإكمال</th>
                                <th>تم الأرشفة بواسطة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for archive in archives %}
                            <tr>
                                <td>
                                    <a href="{% url 'installations:installation_detail' archive.installation.id %}" 
                                       class="font-weight-bold text-primary">
                                        {{ archive.installation.order.order_number }}
                                    </a>
                                </td>
                                <td>{{ archive.installation.order.customer.name }}</td>
                                <td>{{ archive.installation.team.name|default:"غير محدد" }}</td>
                                <td>{{ archive.installation.scheduled_date|date:"d/m/Y" }}</td>
                                <td>{{ archive.completion_date|date:"d/m/Y H:i" }}</td>
                                <td>{{ archive.archived_by.get_full_name|default:archive.archived_by.username }}</td>
                                <td>
                                    <a href="{% url 'installations:installation_detail' archive.installation.id %}" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- ترقيم الصفحات -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="ترقيم الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-archive fa-4x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-500">لا توجد تركيبات مكتملة</h5>
                    <p class="text-gray-400">لم يتم إكمال أي تركيب بعد</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 