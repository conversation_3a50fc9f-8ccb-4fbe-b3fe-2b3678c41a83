# docker-compose.yml لقسم التركيبات

version: '3.8'

services:
  # تطبيق Django
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=************************************************/installations_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
      - ALLOWED_HOSTS=localhost,127.0.0.1
    volumes:
      - ./media:/app/media
      - ./staticfiles:/app/staticfiles
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # قاعدة البيانات PostgreSQL
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=installations_db
      - POSTGRES_USER=installations_user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis للتخزين المؤقت
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Celery Worker
  celery:
    build: .
    command: celery -A installations worker -l info
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=************************************************/installations_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
    volumes:
      - ./media:/app/media
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # Celery Beat
  celery-beat:
    build: .
    command: celery -A installations beat -l info
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=************************************************/installations_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
    volumes:
      - ./media:/app/media
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # Nginx كخادم عكسي
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./staticfiles:/app/staticfiles
      - ./media:/app/media
    depends_on:
      - web
    restart: unless-stopped

  # Elasticsearch للبحث
  elasticsearch:
    image: elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    restart: unless-stopped

  # Kibana لمراقبة Elasticsearch
  kibana:
    image: kibana:8.8.0
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:

networks:
  default:
    driver: bridge 