# متطلبات قسم التركيبات

# Django
Django>=4.2.0
django-crispy-forms>=2.0
django-filter>=23.0
django-tables2>=2.4.0

# الوسائط والملفات
Pillow>=9.0.0
python-magic>=0.4.24

# قواعد البيانات
psycopg2-binary>=2.9.0
mysqlclient>=2.1.0

# API والـ WebSocket
channels>=4.0.0
channels-redis>=4.0.0
djangorestframework>=3.14.0

# الإشعارات
django-notifications-hq>=1.8.0
celery>=5.2.0
redis>=4.0.0

# التقارير والطباعة
reportlab>=3.6.0
weasyprint>=58.0
xhtml2pdf>=0.2.8

# البحث والفلترة
django-haystack>=3.0.0
elasticsearch>=8.0.0

# الأمان
django-cors-headers>=3.13.0
django-ratelimit>=4.0.0

# التطوير والاختبار
pytest>=7.0.0
pytest-django>=4.5.0
factory-boy>=3.2.0
coverage>=6.0.0

# الأداء
django-debug-toolbar>=3.8.0
django-cacheops>=6.0.0

# التصدير والاستيراد
django-import-export>=2.7.0
openpyxl>=3.0.0

# الرسوم البيانية
matplotlib>=3.5.0
seaborn>=0.11.0

# معالجة التاريخ والوقت
python-dateutil>=2.8.0
pytz>=2021.3

# معالجة النصوص
arabic-reshaper>=2.1.0
python-bidi>=0.4.2

# التشفير
cryptography>=3.4.0

# التكامل مع الخدمات الخارجية
requests>=2.28.0
twilio>=7.16.0

# مراقبة الأداء
sentry-sdk>=1.9.0

# أدوات التطوير
black>=22.0.0
flake8>=4.0.0
isort>=5.10.0 