#!/usr/bin/env python
"""
اختبار الإصلاحات النهائية للمزامنة العكسية
"""

import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'homeupdate.settings')
django.setup()

def test_user_model_fix():
    """اختبار إصلاح نموذج المستخدم"""
    try:
        from django.contrib.auth import get_user_model
        
        print("=== اختبار إصلاح نموذج المستخدم ===")
        
        # الحصول على نموذج المستخدم الصحيح
        User = get_user_model()
        print(f"✅ نموذج المستخدم: {User.__name__}")
        print(f"✅ مسار النموذج: {User._meta.app_label}.{User._meta.model_name}")
        
        # التحقق من وجود مستخدمين مديرين
        admin_users = User.objects.filter(is_superuser=True)
        print(f"✅ عدد المستخدمين المديرين: {admin_users.count()}")
        
        if admin_users.exists():
            admin_user = admin_users.first()
            print(f"✅ أول مستخدم مدير: {admin_user.username}")
            return True
        else:
            print("⚠️ لا يوجد مستخدمين مديرين في النظام")
            return True  # ليس خطأ في الكود، فقط لا يوجد بيانات
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نموذج المستخدم: {str(e)}")
        return False

def test_google_sync_log_model():
    """اختبار نموذج GoogleSyncLog"""
    try:
        from odoo_db_manager.google_sync import GoogleSyncLog, GoogleSyncConfig
        
        print("\n=== اختبار نموذج GoogleSyncLog ===")
        
        # فحص حقول النموذج
        fields = [field.name for field in GoogleSyncLog._meta.fields]
        print(f"✅ حقول GoogleSyncLog: {fields}")
        
        # التحقق من الحقول المطلوبة
        required_fields = ['config', 'status', 'message', 'details', 'created_at']
        missing_fields = [field for field in required_fields if field not in fields]
        
        if not missing_fields:
            print("✅ جميع الحقول المطلوبة موجودة")
        else:
            print(f"❌ حقول مفقودة: {missing_fields}")
            return False
        
        # التحقق من عدم وجود operation_type
        if 'operation_type' not in fields:
            print("✅ تأكيد: لا يوجد حقل operation_type (هذا صحيح)")
        else:
            print("⚠️ يوجد حقل operation_type (غير متوقع)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نموذج GoogleSyncLog: {str(e)}")
        return False

def test_reverse_sync_function_signature():
    """اختبار توقيع دالة المزامنة العكسية"""
    try:
        from odoo_db_manager.google_sync import reverse_sync_from_google_sheets
        import inspect
        
        print("\n=== اختبار توقيع دالة المزامنة العكسية ===")
        
        # فحص توقيع الدالة
        sig = inspect.signature(reverse_sync_from_google_sheets)
        params = list(sig.parameters.keys())
        
        print(f"✅ معاملات الدالة: {params}")
        
        expected_params = ['service', 'spreadsheet_id', 'admin_password', 'delete_old_data']
        if params == expected_params:
            print("✅ توقيع الدالة صحيح")
            return True
        else:
            print(f"❌ توقيع الدالة غير صحيح. المتوقع: {expected_params}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار توقيع الدالة: {str(e)}")
        return False

def test_view_code_fixes():
    """اختبار إصلاحات كود view"""
    try:
        print("\n=== اختبار إصلاحات كود view ===")
        
        # قراءة كود view للتحقق من الإصلاحات
        view_file = 'odoo_db_manager/google_sync_views.py'
        if os.path.exists(view_file):
            with open(view_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من إصلاح GoogleSyncLog
            if 'operation_type=' not in content:
                print("✅ تم إزالة operation_type من GoogleSyncLog.objects.create()")
            else:
                print("❌ ما زال هناك استخدام لـ operation_type")
                return False
            
            # التحقق من وجود details مع operation_type
            if "'operation_type': 'reverse_sync'" in content:
                print("✅ تم نقل operation_type إلى details")
            else:
                print("❌ لم يتم نقل operation_type إلى details")
                return False
            
            # التحقق من إصلاح بيانات الاعتماد
            if 'credentials = config.get_credentials()' in content:
                print("✅ تم إصلاح الحصول على بيانات الاعتماد")
            else:
                print("❌ لم يتم إصلاح بيانات الاعتماد")
                return False
            
            return True
        else:
            print("❌ ملف view غير موجود")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إصلاحات view: {str(e)}")
        return False

def test_google_sync_function_fixes():
    """اختبار إصلاحات دالة google_sync"""
    try:
        print("\n=== اختبار إصلاحات دالة google_sync ===")
        
        # قراءة كود google_sync للتحقق من الإصلاحات
        sync_file = 'odoo_db_manager/google_sync.py'
        if os.path.exists(sync_file):
            with open(sync_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من إصلاح نموذج المستخدم
            if 'from django.contrib.auth import get_user_model' in content:
                print("✅ تم إصلاح استيراد نموذج المستخدم")
            else:
                print("❌ لم يتم إصلاح استيراد نموذج المستخدم")
                return False
            
            # التحقق من استخدام get_user_model()
            if 'User = get_user_model()' in content:
                print("✅ تم استخدام get_user_model() بدلاً من auth.User")
            else:
                print("❌ لم يتم استخدام get_user_model()")
                return False
            
            return True
        else:
            print("❌ ملف google_sync غير موجود")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إصلاحات google_sync: {str(e)}")
        return False

def main():
    print("🔄 بدء اختبار الإصلاحات النهائية للمزامنة العكسية...")
    print("="*70)
    
    # اختبار إصلاح نموذج المستخدم
    user_model_test = test_user_model_fix()
    
    # اختبار نموذج GoogleSyncLog
    log_model_test = test_google_sync_log_model()
    
    # اختبار توقيع دالة المزامنة العكسية
    function_test = test_reverse_sync_function_signature()
    
    # اختبار إصلاحات كود view
    view_fixes_test = test_view_code_fixes()
    
    # اختبار إصلاحات دالة google_sync
    sync_fixes_test = test_google_sync_function_fixes()
    
    # النتيجة النهائية
    print("\n" + "="*70)
    print("📊 ملخص نتائج الاختبار:")
    print("="*70)
    
    if user_model_test and log_model_test and function_test and view_fixes_test and sync_fixes_test:
        print("🎉 جميع الإصلاحات تمت بنجاح!")
        print("\n✅ الإصلاحات المطبقة:")
        print("   ✅ إصلاح نموذج المستخدم (استخدام get_user_model)")
        print("   ✅ إصلاح GoogleSyncLog (إزالة operation_type)")
        print("   ✅ نقل operation_type إلى details")
        print("   ✅ إصلاح بيانات الاعتماد في view")
        print("   ✅ توقيع الدالة صحيح")
        print("\n🚀 المزامنة العكسية جاهزة للاستخدام!")
        print("   الرابط: /odoo-db-manager/google-sync/")
        print("   القسم: 'المزامنة العكسية' في أسفل الصفحة")
        print("   المطلوب: كلمة مرور المدير + ملف بيانات اعتماد صحيح")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    main()
