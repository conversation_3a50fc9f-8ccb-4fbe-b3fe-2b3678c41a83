# Generated by Django 4.2.21 on 2025-07-28 15:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_companyinfo_header_logo_alter_companyinfo_logo'),
        ('inventory', '0001_initial'),
        ('orders', '0008_alter_order_created_at_alter_order_order_date'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='manufacturingdeletionlog',
            name='order',
        ),
        migrations.AddField(
            model_name='manufacturingdeletionlog',
            name='order_id',
            field=models.PositiveIntegerField(default=0, verbose_name='معرف الطلب المحذوف'),
        ),
        migrations.AddField(
            model_name='manufacturingdeletionlog',
            name='order_number',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الطلب المحذوف'),
        ),
        migrations.AlterField(
            model_name='order',
            name='salesperson',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='accounts.salesperson', verbose_name='البائع'),
        ),
        migrations.AlterField(
            model_name='orderitem',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='inventory.product', verbose_name='المنتج'),
        ),
    ]
