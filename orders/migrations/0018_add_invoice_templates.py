# Generated by Django 4.2.21 on 2025-08-09 11:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('orders', '0017_add_reference_number_to_payment'),
    ]

    operations = [
        migrations.CreateModel(
            name='InvoiceTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القالب')),
                ('template_type', models.CharField(choices=[('standard', 'قالب عادي'), ('detailed', 'قالب مفصل'), ('minimal', 'قالب مبسط'), ('custom', 'قالب مخصص')], default='standard', max_length=20, verbose_name='نوع القالب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_default', models.BooleanField(default=False, verbose_name='افتراضي')),
                ('company_name', models.CharField(max_length=200, verbose_name='اسم الشركة')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='invoice_templates/logos/', verbose_name='شعار الشركة')),
                ('company_address', models.TextField(verbose_name='عنوان الشركة')),
                ('company_phone', models.CharField(blank=True, max_length=100, verbose_name='هاتف الشركة')),
                ('company_email', models.EmailField(blank=True, max_length=254, verbose_name='بريد الشركة')),
                ('company_website', models.URLField(blank=True, verbose_name='موقع الشركة')),
                ('primary_color', models.CharField(default='#2563eb', help_text='مثل: #2563eb', max_length=7, verbose_name='اللون الأساسي')),
                ('secondary_color', models.CharField(default='#64748b', help_text='مثل: #64748b', max_length=7, verbose_name='اللون الثانوي')),
                ('font_family', models.CharField(default='Arial, sans-serif', help_text='مثل: Arial, sans-serif', max_length=100, verbose_name='نوع الخط')),
                ('font_size', models.IntegerField(default=12, verbose_name='حجم الخط')),
                ('show_company_logo', models.BooleanField(default=True, verbose_name='إظهار شعار الشركة')),
                ('show_order_details', models.BooleanField(default=True, verbose_name='إظهار تفاصيل الطلب')),
                ('show_customer_details', models.BooleanField(default=True, verbose_name='إظهار بيانات العميل')),
                ('show_payment_details', models.BooleanField(default=True, verbose_name='إظهار تفاصيل الدفع')),
                ('show_notes', models.BooleanField(default=True, verbose_name='إظهار الملاحظات')),
                ('show_terms', models.BooleanField(default=True, verbose_name='إظهار الشروط والأحكام')),
                ('header_text', models.TextField(blank=True, help_text='نص يظهر في أعلى الفاتورة', verbose_name='نص الرأس')),
                ('footer_text', models.TextField(blank=True, help_text='نص يظهر في أسفل الفاتورة', verbose_name='نص التذييل')),
                ('terms_text', models.TextField(blank=True, default='شكراً لتعاملكم معنا. جميع الأسعار شاملة ضريبة القيمة المضافة.', verbose_name='الشروط والأحكام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'قالب فاتورة',
                'verbose_name_plural': 'قوالب الفواتير',
                'ordering': ['-is_default', '-is_active', 'name'],
            },
        ),
        migrations.CreateModel(
            name='InvoicePrintLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('printed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطباعة')),
                ('print_type', models.CharField(choices=[('auto', 'طباعة تلقائية'), ('manual', 'طباعة يدوية')], default='manual', max_length=20, verbose_name='نوع الطباعة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoice_prints', to='orders.order', verbose_name='الطلب')),
                ('printed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الطباعة بواسطة')),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='orders.invoicetemplate', verbose_name='القالب المستخدم')),
            ],
            options={
                'verbose_name': 'سجل طباعة فاتورة',
                'verbose_name_plural': 'سجلات طباعة الفواتير',
                'ordering': ['-printed_at'],
            },
        ),
    ]
