# Generated by Django 4.2.21 on 2025-07-19 14:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0005_order_related_inspection_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='location_type',
            field=models.CharField(blank=True, choices=[('open', 'مفتوح'), ('compound', 'كومبوند')], help_text='نوع المكان (مفتوح أو كومبوند)', max_length=20, null=True, verbose_name='نوع المكان'),
        ),
        migrations.AlterField(
            model_name='order',
            name='installation_status',
            field=models.CharField(choices=[('needs_scheduling', 'بحاجة جدولة'), ('scheduled', 'مجدول'), ('in_installation', 'قيد التركيب'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('modification_required', 'يحتاج تعديل'), ('modification_in_progress', 'التعديل قيد التنفيذ'), ('modification_completed', 'التعديل مكتمل')], default='needs_scheduling', max_length=30, verbose_name='حالة التركيب'),
        ),
    ]
