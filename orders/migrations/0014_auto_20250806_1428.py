# Generated by Django 4.2.21 on 2025-08-06 11:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0013_auto_20250806_1425'),
    ]

    operations = [
        # إضافة فهارس لتحسين أداء admin
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS orders_order_admin_perf ON orders_order (order_number, customer_id, status, order_status);",
            reverse_sql="DROP INDEX IF EXISTS orders_order_admin_perf;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS orders_order_search_perf ON orders_order (order_number, customer_id, salesperson_id, branch_id);",
            reverse_sql="DROP INDEX IF EXISTS orders_order_search_perf;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS customers_customer_admin_perf ON customers_customer (code, name, phone, branch_id, status);",
            reverse_sql="DROP INDEX IF EXISTS customers_customer_admin_perf;"
        ),
    ]
