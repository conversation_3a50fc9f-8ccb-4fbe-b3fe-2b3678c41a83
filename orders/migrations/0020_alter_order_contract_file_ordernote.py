# Generated by Django 4.2.21 on 2025-08-11 08:34

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import orders.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('orders', '0019_invoicetemplate_accent_color_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='order',
            name='contract_file',
            field=models.FileField(blank=True, help_text='يجب أن يكون الملف من نوع PDF وأقل من 50 ميجابايت', null=True, upload_to='contracts/', validators=[orders.models.validate_pdf_file], verbose_name='ملف العقد'),
        ),
        migrations.CreateModel(
            name='OrderNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note_type', models.CharField(choices=[('general', 'عام'), ('inspection', 'معاينة'), ('installation', 'تركيب'), ('manufacturing', 'تصنيع'), ('delivery', 'تسليم'), ('payment', 'دفع'), ('complaint', 'شكوى'), ('status_change', 'تغيير حالة')], default='general', max_length=20, verbose_name='نوع الملاحظة')),
                ('title', models.CharField(blank=True, max_length=200, verbose_name='عنوان الملاحظة')),
                ('content', models.TextField(verbose_name='محتوى الملاحظة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('is_important', models.BooleanField(default=False, verbose_name='ملاحظة مهمة')),
                ('is_visible_to_customer', models.BooleanField(default=False, verbose_name='مرئية للعميل')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_notes', to='orders.order', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'ملاحظة الطلب',
                'verbose_name_plural': 'ملاحظات الطلب',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['order', 'note_type'], name='orders_orde_order_i_348b6e_idx'), models.Index(fields=['created_at'], name='orders_orde_created_88e2be_idx')],
            },
        ),
    ]
