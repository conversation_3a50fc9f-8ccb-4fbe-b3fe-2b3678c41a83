{% extends 'base.html' %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-money-bill-wave"></i> {{ title }}</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <p class="mb-0">
                            <strong>الطلب:</strong> #{{ order.order_number }} - {{ order.customer.name }}<br>
                            <strong>المبلغ الإجمالي:</strong> {{ order.total_amount }} {{ currency_symbol }}<br>
                            <strong>المبلغ المدفوع:</strong> {{ order.paid_amount }} {{ currency_symbol }}<br>
                            <strong>المبلغ المتبقي:</strong> {{ order.remaining_amount }} {{ currency_symbol }}
                        </p>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.amount.id_for_label }}" class="form-label">المبلغ *</label>
                            {{ form.amount }}
                            {% if form.amount.errors %}
                            <div class="text-danger">
                                {% for error in form.amount.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.payment_method.id_for_label }}" class="form-label">طريقة الدفع *</label>
                            {{ form.payment_method }}
                            {% if form.payment_method.errors %}
                            <div class="text-danger">
                                {% for error in form.payment_method.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.reference_number.id_for_label }}" class="form-label">رقم الفاتورة</label>
                            {{ form.reference_number }}
                            {% if form.reference_number.errors %}
                            <div class="text-danger">
                                {% for error in form.reference_number.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <div class="form-text">رقم الفاتورة المرتبطة بهذه الدفعة</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'orders:order_detail' order.pk %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للطلب
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> تسجيل الدفعة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
