{% extends 'base.html' %}
{% load unified_status_tags %}
{% load order_extras %}

{% block title %}طلبات التركيب - نظام الخواجه{% endblock %}

{% block extra_css %}
<style>
.page-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 15px;
}

.installation-badge {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.stats-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.orders-table th {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
    font-weight: 600;
}

.orders-table tbody tr:hover {
    background-color: rgba(240, 147, 251, 0.1);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1><i class="fas fa-tools me-3"></i>طلبات التركيب</h1>
        <p class="mb-0">إدارة ومتابعة جميع طلبات التركيب والتجهيز</p>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-list-alt fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ total_count }}</h4>
                    <small class="text-muted">إجمالي طلبات التركيب</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ needs_scheduling_count }}</h4>
                    <small class="text-muted">بحاجة جدولة</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-tools fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ in_installation_count }}</h4>
                    <small class="text-muted">قيد التركيب</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ completed_count }}</h4>
                    <small class="text-muted">مكتملة</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="بحث برقم الطلب أو اسم العميل" value="{{ search_query }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label">حالة التركيب</label>
                    <select name="installation_status" class="form-select">
                        <option value="">-- جميع الحالات --</option>
                        <option value="needs_scheduling" {% if installation_status_filter == 'needs_scheduling' %}selected{% endif %}>بحاجة جدولة</option>
                        <option value="scheduled" {% if installation_status_filter == 'scheduled' %}selected{% endif %}>مجدول</option>
                        <option value="in_installation" {% if installation_status_filter == 'in_installation' %}selected{% endif %}>قيد التركيب</option>
                        <option value="completed" {% if installation_status_filter == 'completed' %}selected{% endif %}>مك��مل</option>
                        <option value="cancelled" {% if installation_status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">عدد الصفوف</label>
                    <select name="page_size" class="form-select">
                        <option value="10" {% if request.GET.page_size == '10' %}selected{% endif %}>10</option>
                        <option value="25" {% if request.GET.page_size == '25' or not request.GET.page_size %}selected{% endif %}>25</option>
                        <option value="50" {% if request.GET.page_size == '50' %}selected{% endif %}>50</option>
                        <option value="100" {% if request.GET.page_size == '100' %}selected{% endif %}>100</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">تصفية</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="table-container">
        <div class="card-header bg-transparent">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">قائمة طلبات التركيب ({{ total_count }})</h5>
                </div>
                <div class="col-auto">
                    {% if perms.orders.add_order %}
                    <a href="{% url 'orders:order_create' %}?type=installation" class="btn btn-primary">
                        <i class="fas fa-plus"></i> طلب تركيب جديد
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover mb-0 orders-table">
                <thead>
                    <tr>
                        <th class="text-center">رقم الطلب</th>
                        <th class="text-center">العميل</th>
                        <th class="text-center">تاريخ الطلب</th>
                        <th class="text-center">حالة التصنيع</th>
                        <th class="text-center">حالة التركيب</th>
                        <th class="text-center">تاريخ الجدولة</th>
                        <th class="text-center">عنوان التركيب</th>
                        <th class="text-center">البائع</th>
                        <th class="text-center">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in page_obj %}
                    <tr>
                        <td class="text-center">
                            <a href="{% url 'orders:order_detail' order.pk %}" class="text-decoration-none fw-bold">
                                {{ order.order_number }}
                            </a>
                        </td>
                        <td class="text-center">
                            <a href="{% url 'customers:customer_detail' order.customer.pk %}" class="text-decoration-none">
                                <strong>{{ order.customer.name }}</strong>
                            </a>
                            <br>
                            <small class="text-muted">{{ order.customer.phone }}</small>
                        </td>
                        <td class="text-center">
                            {{ order.order_date|date:"Y-m-d" }}
                            <br>
                            <small class="text-muted">{{ order.order_date|date:"H:i" }}</small>
                        </td>
                        <td class="text-center">
                            {% if order.manufacturing_order %}
                                {% get_status_badge order.manufacturing_order.status "manufacturing" %}
                            {% else %}
                                <span class="badge bg-secondary">لا يوجد</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% get_status_badge order.installation_status "installation" %}
                        </td>
                        <td class="text-center">
                            {% with scheduling_date=order.get_scheduling_date_display %}
                                {% if scheduling_date %}
                                    {{ scheduling_date }}
                                    <br>
                                    <small class="text-muted">
                                        {% if order.installation_status == 'scheduled' %}
                                            <i class="fas fa-calendar text-info"></i> مجدول
                                        {% elif order.installation_status == 'in_installation' %}
                                            <i class="fas fa-tools text-warning"></i> قيد التركيب
                                        {% endif %}
                                    </small>
                                {% else %}
                                    <span class="text-muted">غير مجدول</span>
                                {% endif %}
                            {% endwith %}
                        </td>
                        <td class="text-center">
                            {% if order.location_address %}
                                <span title="{{ order.location_address }}">
                                    {{ order.location_address|truncatechars:30 }}
                                </span>
                                {% if order.location_type %}
                                    <br>
                                    <small class="badge bg-info">{{ order.get_location_type_display }}</small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if order.salesperson %}
                                {{ order.salesperson.name }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'orders:order_detail' order.pk %}" class="btn btn-info" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if perms.orders.change_order %}
                                <a href="{% url 'orders:order_update' order.pk %}" class="btn btn-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if perms.orders.delete_order %}
                                <a href="{% url 'orders:order_delete' order.pk %}" class="btn btn-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="card-body text-center py-5">
            <i class="fas fa-tools fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد طلبات تركيب</h5>
            <p class="text-muted">لم يتم العثور على أي طلبات تركيب تطابق معايير البحث</p>
            {% if perms.orders.add_order %}
            <a href="{% url 'orders:order_create' %}?type=installation" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إنشاء طلب تركيب جديد
            </a>
            {% endif %}
        </div>
        {% endif %}

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if installation_status_filter %}&installation_status={{ installation_status_filter }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if installation_status_filter %}&installation_status={{ installation_status_filter }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if installation_status_filter %}&installation_status={{ installation_status_filter }}{% endif %}">
                                {{ num }}
                            </a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if installation_status_filter %}&installation_status={{ installation_status_filter }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if installation_status_filter %}&installation_status={{ installation_status_filter }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}