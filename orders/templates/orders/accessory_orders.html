{% extends 'base.html' %}
{% load unified_status_tags %}
{% load order_extras %}

{% block title %}طلبات الإكسسوار - نظام الخواجه{% endblock %}

{% block extra_css %}
<style>
.page-header {
    background: linear-gradient(135deg, #A0522D 0%, #D2691E 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 15px;
}

.accessory-badge {
    background: linear-gradient(135deg, #A0522D 0%, #D2691E 100%);
    color: white;
}

.stats-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.orders-table th {
    background: linear-gradient(135deg, #A0522D 0%, #D2691E 100%);
    color: white;
    border: none;
    font-weight: 600;
}

.orders-table tbody tr:hover {
    background-color: rgba(160, 82, 45, 0.1);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1><i class="fas fa-gem me-3"></i>طلبات الإكسسوار</h1>
        <p class="mb-0">إدارة ومتابعة جميع طلبات الإكسسوارات والقطع الإضافية</p>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-list-alt fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ total_count }}</h4>
                    <small class="text-muted">إجمالي طلبات الإكسسوار</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ pending_count }}</h4>
                    <small class="text-muted">في الانتظار</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-truck fa-2x text-info mb-2"></i>
                    <h4 class="mb-1">{{ ready_count }}</h4>
                    <small class="text-muted">جاهز للتسليم</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ delivered_count }}</h4>
                    <small class="text-muted">تم التسليم</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="بحث برقم الطلب أو اسم العميل" value="{{ search_query }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">-- جميع الحالات --</option>
                        <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                        <option value="in_progress" {% if status_filter == 'in_progress' %}selected{% endif %}>قيد التحضير</option>
                        <option value="ready" {% if status_filter == 'ready' %}selected{% endif %}>جاهز للتسليم</option>
                        <option value="delivered" {% if status_filter == 'delivered' %}selected{% endif %}>تم التسليم</option>
                        <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">عدد الصفوف</label>
                    <select name="page_size" class="form-select">
                        <option value="10" {% if request.GET.page_size == '10' %}selected{% endif %}>10</option>
                        <option value="25" {% if request.GET.page_size == '25' or not request.GET.page_size %}selected{% endif %}>25</option>
                        <option value="50" {% if request.GET.page_size == '50' %}selected{% endif %}>50</option>
                        <option value="100" {% if request.GET.page_size == '100' %}selected{% endif %}>100</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">تصفية</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="table-container">
        <div class="card-header bg-transparent">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">قائمة طلبات الإكسسوار ({{ total_count }})</h5>
                </div>
                <div class="col-auto">
                    {% if perms.orders.add_order %}
                    <a href="{% url 'orders:order_create' %}?type=accessory" class="btn btn-primary">
                        <i class="fas fa-plus"></i> طلب إكسسوار جديد
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover mb-0 orders-table">
                <thead>
                    <tr>
                        <th class="text-center">رقم الطلب</th>
                        <th class="text-center">العميل</th>
                        <th class="text-center">تاريخ الطلب</th>
                        <th class="text-center">الحالة</th>
                        <th class="text-center">المبلغ الإجمالي</th>
                        <th class="text-center">المبلغ المتبقي</th>
                        <th class="text-center">نوع التسليم</th>
                        <th class="text-center">البائع</th>
                        <th class="text-center">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in page_obj %}
                    <tr>
                        <td class="text-center">
                            <a href="{% url 'orders:order_detail' order.pk %}" class="text-decoration-none fw-bold">
                                {{ order.order_number }}
                            </a>
                        </td>
                        <td class="text-center">
                            <a href="{% url 'customers:customer_detail' order.customer.pk %}" class="text-decoration-none">
                                <strong>{{ order.customer.name }}</strong>
                            </a>
                            <br>
                            <small class="text-muted">{{ order.customer.phone }}</small>
                        </td>
                        <td class="text-center">
                            {{ order.order_date|date:"Y-m-d" }}
                            <br>
                            <small class="text-muted">{{ order.order_date|date:"H:i" }}</small>
                        </td>
                        <td class="text-center">
                            {% with display_info=order.get_display_status %}
                                <span class="badge {{ order.get_display_status_badge_class }}">
                                    <i class="{{ order.get_display_status_icon }} me-1"></i>
                                    {{ order.get_display_status_text }}
                                </span>
                            {% endwith %}
                        </td>
                        <td class="text-center">
                            <strong>{{ order.total_amount|floatformat:2 }} {{ currency_symbol }}</strong>
                        </td>
                        <td class="text-center">
                            {% if order.remaining_amount == 0 or order.remaining_amount == None %}
                                <span class="badge bg-success">مسدد</span>
                            {% else %}
                                <span class="text-danger fw-bold">{{ order.remaining_amount|floatformat:2 }} {{ currency_symbol }}</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if order.delivery_type == 'home' %}
                                <span class="badge bg-info">
                                    <i class="fas fa-home me-1"></i>توصيل منزلي
                                </span>
                                {% if order.delivery_address %}
                                    <br>
                                    <small class="text-muted" title="{{ order.delivery_address }}">
                                        {{ order.delivery_address|truncatechars:20 }}
                                    </small>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-store me-1"></i>استلام من الفرع
                                </span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if order.salesperson %}
                                {{ order.salesperson.name }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'orders:order_detail' order.pk %}" class="btn btn-info" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if perms.orders.change_order %}
                                <a href="{% url 'orders:order_update' order.pk %}" class="btn btn-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if perms.orders.delete_order %}
                                <a href="{% url 'orders:order_delete' order.pk %}" class="btn btn-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="card-body text-center py-5">
            <i class="fas fa-gem fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد طلبات إكسسوار</h5>
            <p class="text-muted">لم يتم العثور على أي طل��ات إكسسوار تطابق معايير البحث</p>
            {% if perms.orders.add_order %}
            <a href="{% url 'orders:order_create' %}?type=accessory" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إنشاء طلب إكسسوار جديد
            </a>
            {% endif %}
        </div>
        {% endif %}

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                                {{ num }}
                            </a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}