{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}
{% load i18n %}

{% block title %}تعديل الطلب {{ order.order_number }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
    .order-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    .order-info-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .formset-item {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: #fff;
    }
    
    .formset-item.to-delete {
        background: #f8d7da;
        border-color: #f5c6cb;
    }
    
    .btn-add-item {
        background: #28a745;
        border-color: #28a745;
        color: white;
    }
    
    .btn-remove-item {
        background: #dc3545;
        border-color: #dc3545;
        color: white;
    }
    
    .readonly-field {
        background-color: #e9ecef;
        opacity: 1;
    }
    
    .total-calculation {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="order-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-edit me-2"></i>تعديل الطلب {{ order.order_number }}</h2>
                <p class="mb-0">العميل: {{ order.customer.name }} | الفرع: {{ order.branch.name }}</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'orders:order_detail_by_number' order_number=order.order_number %}" 
                   class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتفاصيل
                </a>
            </div>
        </div>
    </div>

    <form method="post" enctype="multipart/form-data" id="order-edit-form">
        {% csrf_token %}
        
        <!-- معلومات الطلب الأساسية -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الطلب الأساسية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="{{ form.customer.id_for_label }}" class="form-label">العميل</label>
                            {% render_field form.customer class="form-control" %}
                            {% if form.customer.errors %}
                                <div class="text-danger">{{ form.customer.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="{{ form.branch.id_for_label }}" class="form-label">الفرع</label>
                            {% render_field form.branch class="form-control" %}
                            {% if form.branch.errors %}
                                <div class="text-danger">{{ form.branch.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="{{ form.salesperson.id_for_label }}" class="form-label">البائع</label>
                            {% render_field form.salesperson class="form-control" %}
                            {% if form.salesperson.errors %}
                                <div class="text-danger">{{ form.salesperson.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="{{ form.selected_types.id_for_label }}" class="form-label">نوع الطلب</label>
                            {% render_field form.selected_types class="form-control readonly-field" %}
                            <small class="text-muted">{{ form.selected_types.help_text }}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="{{ form.total_amount.id_for_label }}" class="form-label">إجمالي المبلغ</label>
                            {% render_field form.total_amount class="form-control readonly-field" %}
                            <small class="text-muted">{{ form.total_amount.help_text }}</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-3">
                            <label for="{{ form.paid_amount.id_for_label }}" class="form-label">المبلغ المدفوع</label>
                            {% render_field form.paid_amount class="form-control" %}
                            {% if form.paid_amount.errors %}
                                <div class="text-danger">{{ form.paid_amount.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات الطلب</label>
                            {% render_field form.notes class="form-control" %}
                            {% if form.notes.errors %}
                                <div class="text-danger">{{ form.notes.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عناصر الطلب -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>عناصر الطلب</h5>
                <button type="button" class="btn btn-light btn-sm" id="add-item">
                    <i class="fas fa-plus me-2"></i>إضافة عنصر
                </button>
            </div>
            <div class="card-body">
                {{ formset.management_form }}
                
                <div id="formset-container">
                    {% for form in formset %}
                        <div class="formset-item" data-form-index="{{ forloop.counter0 }}">
                            <div class="row align-items-center">
                                <div class="col-md-1">
                                    <strong>#{{ forloop.counter }}</strong>
                                    {% for hidden in form.hidden_fields %}
                                        {{ hidden }}
                                    {% endfor %}
                                    {% if form.item_type %}
                                        {{ form.item_type.as_hidden }}
                                    {% else %}
                                        <input type="hidden" name="{{ form.prefix }}-item_type" value="product">
                                    {% endif %}
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label">اسم المنتج</label>
                                        {% render_field form.product class="form-control product-select" %}
                                        {% if form.product.errors %}
                                            <div class="text-danger">{{ form.product.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="form-label">الكمية</label>
                                        {% render_field form.quantity class="form-control item-quantity" %}
                                        {% if form.quantity.errors %}
                                            <div class="text-danger">{{ form.quantity.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="form-label">سعر الوحدة</label>
                                        {% render_field form.unit_price class="form-control item-price" %}
                                        {% if form.unit_price.errors %}
                                            <div class="text-danger">{{ form.unit_price.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label">ملاحظات</label>
                                        {% render_field form.notes class="form-control" %}
                                        {% if form.notes.errors %}
                                            <div class="text-danger">{{ form.notes.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    {% if form.DELETE %}
                                        <div class="form-check">
                                            {% render_field form.DELETE class="form-check-input delete-checkbox" %}
                                            <label class="form-check-label text-danger">حذف</label>
                                        </div>
                                    {% else %}
                                        <button type="button" class="btn btn-sm btn-remove-item remove-item">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                
                <!-- حساب الإجمالي -->
                <div class="total-calculation">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-calculator me-2"></i>حساب الإجمالي</h6>
                            <p class="mb-0">سيتم حساب المبلغ الإجمالي تلقائياً من عناصر الطلب</p>
                        </div>
                        <div class="col-md-6 text-end">
                            <h4 class="mb-0">الإجمالي: <span id="calculated-total">{{ order.total_amount|floatformat:2 }}</span> ج.م</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="card">
            <div class="card-body text-center">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>حفظ التعديلات
                </button>
                <a href="{% url 'orders:order_detail_by_number' order_number=order.order_number %}" 
                   class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {

    // متغيرات للـ formset
    let formIndex = {{ formset.total_form_count }};
    const maxForms = {{ formset.max_num|default:1000 }};

    // تفعيل Select2 للمنتجات
    function initializeSelect2(element) {
        $(element).select2({
            theme: 'bootstrap-5',
            width: '100%',
            placeholder: 'اختر المنتج',
            allowClear: true
        });
    }

    // تفعيل Select2 للمنتجات الموجودة
    $('.product-select').each(function() {
        initializeSelect2(this);
    });

    // دالة حساب الإجمالي
    function calculateTotal() {
        let total = 0;
        $('.formset-item:not(.to-delete)').each(function() {
            const quantity = parseFloat($(this).find('.item-quantity').val()) || 0;
            const price = parseFloat($(this).find('.item-price').val()) || 0;
            total += quantity * price;
        });
        $('#calculated-total').text(total.toFixed(2));
        $('input[name="total_amount"]').val(total.toFixed(2));
    }

    // تحديث السعر عند تغيير المنتج
    $(document).on('change', '.product-select', function() {
        const selectedOption = $(this).find('option:selected');
        const priceInput = $(this).closest('.formset-item').find('.item-price');
        const price = selectedOption.attr('data-price');

        console.log('Selected product price:', price); // للتشخيص

        if (price && parseFloat(price) > 0) {
            priceInput.val(parseFloat(price).toFixed(2));
            calculateTotal();
        }
    });

    // حساب الإجمالي عند تغيير الكمية أو السعر
    $(document).on('input', '.item-quantity, .item-price', calculateTotal);

    // إضافة عنصر جديد
    $('#add-item').click(function(e) {
        e.preventDefault();

        if (formIndex >= maxForms) {
            alert('تم الوصول للحد الأقصى من العناصر');
            return;
        }

        // إنشاء خيارات المنتجات
        let productOptions = '<option value="">اختر المنتج</option>';
        {% for product in formset.form.base_fields.product.queryset %}
        productOptions += '<option value="{{ product.id }}" data-price="{{ product.price }}">{{ product.name }} - {{ product.price }} ج.م</option>';
        {% endfor %}

        const emptyForm = `
            <div class="formset-item" data-form-index="${formIndex}">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <strong>#${formIndex + 1}</strong>
                        <input type="hidden" name="items-${formIndex}-id" id="id_items-${formIndex}-id">
                        <input type="hidden" name="items-${formIndex}-order" value="{{ order.id }}" id="id_items-${formIndex}-order">
                        <input type="hidden" name="items-${formIndex}-item_type" value="product" id="id_items-${formIndex}-item_type">
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">اسم المنتج</label>
                            <select name="items-${formIndex}-product" class="form-control product-select" id="id_items-${formIndex}-product">
                                ${productOptions}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label class="form-label">الكمية</label>
                            <input type="number" name="items-${formIndex}-quantity" class="form-control item-quantity" id="id_items-${formIndex}-quantity" min="1" step="1" value="1">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label class="form-label">سعر الوحدة</label>
                            <input type="number" name="items-${formIndex}-unit_price" class="form-control item-price" id="id_items-${formIndex}-unit_price" min="0" step="0.01">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="items-${formIndex}-notes" class="form-control" id="id_items-${formIndex}-notes" rows="2"></textarea>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <button type="button" class="btn btn-sm btn-danger remove-item">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        $('#formset-container').append(emptyForm);

        // تفعيل Select2 للعنصر الجديد
        initializeSelect2(`#id_items-${formIndex}-product`);

        formIndex++;
        $('input[name="items-TOTAL_FORMS"]').val(formIndex);

        calculateTotal();
    });

    // حذف عنصر
    $(document).on('click', '.remove-item', function() {
        $(this).closest('.formset-item').remove();
        calculateTotal();
    });

    // تعليم العناصر للحذف
    $(document).on('change', '.delete-checkbox', function() {
        const formItem = $(this).closest('.formset-item');
        if ($(this).is(':checked')) {
            formItem.addClass('to-delete');
        } else {
            formItem.removeClass('to-delete');
        }
        calculateTotal();
    });

    // حساب الإجمالي عند تحميل الصفحة
    calculateTotal();

    // التحقق من صحة النموذج قبل الإرسال
    $('#order-edit-form').submit(function(e) {
        let hasValidItems = false;
        $('.formset-item:not(.to-delete)').each(function() {
            const product = $(this).find('select[name*="-product"]').val();
            const quantity = $(this).find('input[name*="-quantity"]').val();
            const price = $(this).find('input[name*="-unit_price"]').val();

            if (product && quantity && price) {
                hasValidItems = true;
                return false; // break
            }
        });

        if (!hasValidItems) {
            e.preventDefault();
            alert('يجب أن يحتوي الطلب على عنصر واحد صحيح على الأقل');
            return false;
        }
    });
});
</script>
{% endblock %}
